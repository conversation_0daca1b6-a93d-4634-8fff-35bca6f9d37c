package com.shands.mod.rabbitmq.constant;

public class ActivityMqConstant {

  //活动：延迟
  //活动订单 超时未支付自动取消 交换机
  public static final String ACTIVITY_ORDER_APP = "exchange.delay.activity.order.update.audio";

  //活动订单 超时未支付
  public static final String QUEUE_DELAY_ACTIVITY_ORDER_AUDIO = "queue.delay.activity.order.audio";

  //活动订单 超时未支付自动取消 delay
  public static final String ACTIVITY_ORDER_RULE = "activity.delay.order.app";


  //活动订单，未核销，系统退款
  public static final String ACTIVITY_ORDER_REFUND = "exchange.delay.activity.order.automaticRefund.audio";

  //活动订单 未核销
  public static final String QUEUE_DELAY_ACTIVITY_ORDER_REFUND_AUDIO = "queue.delay.activity.order.automaticRefund.audio";

  //活动订单 未核销 delay
  public static final String ACTIVITY_ORDER_REFUND_RULE = "activity.delay.order.automaticRefund.app";

}
