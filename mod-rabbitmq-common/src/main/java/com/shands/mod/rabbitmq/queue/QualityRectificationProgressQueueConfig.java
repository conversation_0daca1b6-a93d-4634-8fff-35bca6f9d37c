package com.shands.mod.rabbitmq.queue;

import com.shands.mod.rabbitmq.constant.QualityMqConstant;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @desc 直连交换机 巡查进度通知 配置类
 */
@Configuration
public class QualityRectificationProgressQueueConfig {


  @Resource
  private RabbitAdmin rabbitAdmin;

  @Bean
  public void directExcitationQualityRectificationProgressRabbitConfigInit() {
    //活动超时未核销
    rabbitAdmin.declareExchange(delayQualityRectificationProgressExchage());
    rabbitAdmin.declareQueue(delayQualityRectificationProgressQueue());
  }
  @Bean
  CustomExchange delayQualityRectificationProgressExchage() {
    Map<String, Object> args = new HashMap<>();
    args.put("x-delayed-type", "direct");
    return new CustomExchange(QualityMqConstant.EXCHANGE_RECTIFICATION_PROGRESS_PUSH, "x-delayed-message",
      true, false, args);
  }

  @Bean
  Queue delayQualityRectificationProgressQueue() {
    return new Queue(QualityMqConstant.QUEUE_RECTIFICATION_PROGRESS_PUSH);
  }

  @Bean
  Binding delayQualityRectificationProgressBinding(Queue delayQualityRectificationProgressQueue,
    Exchange delayQualityRectificationProgressExchage) {
    return BindingBuilder.bind(delayQualityRectificationProgressQueue).to(delayQualityRectificationProgressExchage)
      .with(QualityMqConstant.RECTIFICATION_PROGRESS_PUSH_RULE).noargs();
  }
}
