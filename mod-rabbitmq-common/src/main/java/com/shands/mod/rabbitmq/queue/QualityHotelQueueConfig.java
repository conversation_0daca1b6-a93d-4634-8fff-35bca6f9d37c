package com.shands.mod.rabbitmq.queue;

import com.shands.mod.rabbitmq.constant.QualityMqConstant;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @desc 直连交换机 质检酒店通知 配置类
 */
@Configuration
public class QualityHotelQueueConfig {

  @Resource
  private RabbitAdmin rabbitAdmin;

  @Bean
  public void directExcitationQualityHotelRabbitConfigInit() {
    rabbitAdmin.declareExchange(delayQualityHotelExchage());
    rabbitAdmin.declareQueue(delayQualityHotelQueue());
  }
  @Bean
  CustomExchange delayQualityHotelExchage() {
    Map<String, Object> args = new HashMap<>();
    args.put("x-delayed-type", "direct");
    return new CustomExchange(QualityMqConstant.EXCHANGE_QUALITY_HOTEL_PUSH, "x-delayed-message",
      true, false, args);
  }

  @Bean
  Queue delayQualityHotelQueue() {
    return new Queue(QualityMqConstant.QUEUE_QUALITY_HOTEL_PUSH);
  }

  @Bean
  Binding delayQualityHotelBinding(Queue delayQualityHotelQueue,
    Exchange delayQualityHotelExchage) {
    return BindingBuilder.bind(delayQualityHotelQueue).to(delayQualityHotelExchage)
      .with(QualityMqConstant.QUALITY_HOTEL_PUSH_RULE).noargs();
  }
}
