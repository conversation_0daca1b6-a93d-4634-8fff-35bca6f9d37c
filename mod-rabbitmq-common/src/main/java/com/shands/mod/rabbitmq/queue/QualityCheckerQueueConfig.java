package com.shands.mod.rabbitmq.queue;

import com.shands.mod.rabbitmq.constant.QualityMqConstant;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @desc 直连交换机 质检检查人通知 配置类
 */
@Configuration
public class QualityCheckerQueueConfig {


  @Resource
  private RabbitAdmin rabbitAdmin;

  @Bean
  public void directExcitationQualityCheckerRabbitConfigInit() {
    //活动超时未核销
    rabbitAdmin.declareExchange(delayQualityCheckerExchage());
    rabbitAdmin.declareQueue(delayQualityCheckerQueue());
  }
  @Bean
  CustomExchange delayQualityCheckerExchage() {
    Map<String, Object> args = new HashMap<>();
    args.put("x-delayed-type", "direct");
    return new CustomExchange(QualityMqConstant.EXCHANGE_QUALITY_CHECKER_PUSH, "x-delayed-message",
      true, false, args);
  }

  @Bean
  Queue delayQualityCheckerQueue() {
    return new Queue(QualityMqConstant.QUEUE_QUALITY_CHECKER_PUSH);
  }

  @Bean
  Binding delayQualityCheckerBinding(Queue delayQualityCheckerQueue,
    Exchange delayQualityCheckerExchage) {
    return BindingBuilder.bind(delayQualityCheckerQueue).to(delayQualityCheckerExchage)
      .with(QualityMqConstant.QUALITY_CHECKER_PUSH_RULE).noargs();
  }
}
