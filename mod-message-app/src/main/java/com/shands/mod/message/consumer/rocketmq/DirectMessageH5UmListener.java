package com.shands.mod.message.consumer.rocketmq;

import com.shands.mod.message.consumer.ExcitationMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 会员激励信息-礼包售卖监听器
 *
 * <AUTHOR>
 * @date 2023/11/2
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(
    consumerGroup = "${rocketmq.consumer_14.group}",
    topic = "${rocketmq.consumer_14.topic}",
    accessKey = "${rocketmq.consumer.access-key}",
    secretKey = "${rocketmq.consumer.secret-key}")
public class DirectMessageH5UmListener implements RocketMQListener<String> {

  @Resource
  private ExcitationMessage excitationMessage;

  @Override
  public void onMessage(String message) {
    log.info("会员激励信息-礼包售卖监听器接收到消息: {}", message);
    excitationMessage.sendH5(message);
  }
}
