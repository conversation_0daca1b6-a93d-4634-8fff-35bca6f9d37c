package com.shands.mod.message.enums;

/**
 * 友盟回执类型
 *
 * <AUTHOR>
 * @date 2023/9/19
 **/
public enum UmCallbackEnum {

  /**
   * 送达
   */
  DELIVERY("0", "送达"),

  /**
   * 点击
   */
  CLICK("1", "点击"),

  /**
   * 忽略
   */
  IGNORE("2", "忽略"),

  /**
   * 厂商通道点击
   */
  FACTORY_CHANNEL_CLICK("21", "厂商通道点击"),

  /**
   * 推送
   */
  PUSH("8", "推送");

  private String code;

  private String desc;

  UmCallbackEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }
}
