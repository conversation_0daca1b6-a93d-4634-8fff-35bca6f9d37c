package com.shands.mod.message.consumer.rocketmq;

import com.shands.mod.message.consumer.ExcitationMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 会员激励信息-提醒-nomo监听器
 *
 * <AUTHOR>
 * @date 2023/9/6
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(
    consumerGroup = "${rocketmq.consumer_3.group}",
    topic = "${rocketmq.consumer_3.topic}",
    accessKey = "${rocketmq.consumer.access-key}",
    secretKey = "${rocketmq.consumer.secret-key}")
public class DirectMessageNomoUmListener implements RocketMQListener<String> {

  @Resource
  private ExcitationMessage excitationMessage;

  @Override
  public void onMessage(String message) {
    log.info("会员激励信息-提醒-nomo监听器接收到消息: {}", message);
    excitationMessage.sendNomo(message);
  }
}
