package com.shands.mod.message.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.call.ModCallNotifyMapper;
import com.shands.mod.dao.mapper.call.ModCallRecordLogMapper;
import com.shands.mod.dao.model.call.ModCallNotify;
import com.shands.mod.dao.model.call.ModCallRecordLog;
import com.shands.mod.message.service.IVocustCloudCallService;
import com.shands.mod.message.util.HttpClientUtil;
import com.shands.mod.util.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

@Slf4j
@Service
public class VocustCloudCallServiceImpl implements IVocustCloudCallService {

  /**
   * 访问密钥id
   */
  @Value("${vocustCloud.appKey:1382600161078845440}")
  private String appKey;

  /**
   * 访问秘钥
   */
  @Value("${vocustCloud.appSecret:8d050768-ed62-4fe0-a485-d645a1bd}")
  private String appSecret;

  /**
   * 请求地址
   */
  @Value("${vocustCloud.url:https://xagent.vocustcloud.com}")
  private String url;

  /**
   * 创建外呼任务uri
   */
  @Value("${vocustCloud.callUri:/apigateway/CallCenterOBServiceNew/api/addObTaskBatch}")
  private String callUri;

  /**
   * 获取令牌uri
   */
  @Value("${vocustCloud.tokenUri:/apigateway/ObeliskService/api/v1/refreshToken}")
  private String tokenUri;

  /**
   * 语音话术
   */
  @Value("${vocustCloud.tts:您有一笔工单已超时，请及时处理}")
  private String tts;

  /**
   * 令牌(默认为初始token 之后需定时进行刷新)
   */
  @Value("${vocustCloud.token:59f58e3c9e69e07fce61a35ee5da69cf34ecbd5a10716940b0c4bd9fc16f475ddcfa7d06d5111ca32e55445131e81a9bd9a40970cf33b2bc7dfb223e453960e8}")
  private String token;

  @Value("${vocustCloud.processId:541}")
  private Integer processId;

  @Value("${vocustCloud.notifyUrl:http://test-web-shandsplusadmin.shands.cn/message/call/notify}")
  private String notifyUrl;

  /**
   * 请求失败重试参数
   */
  private Integer retry;

  //华客云固定参数
  private static final String CALL_TYPE = "AUTO";
  private static final String CALL_STATUS = "INIT";
  private static final String CALL_SUCCESS = "Success";
  private static final String TOKEN_EXPIRED = "13003";

  @Resource
  private RedisTemplate redisTemplate;

  @Resource
  private ModCallRecordLogMapper callRecordLogMapper;

  @Resource
  private ModCallNotifyMapper notifyMapper;

  @Override
  public Boolean voiceCall(List<JSONObject> tasks) {
    boolean isOk;
    retry = 0;
    //获取token
    getToken();
    //请求语音外呼接口
    JSONObject post = sendPost(tasks);
    //对返回结果进行解析并判断是否需要失败重试
    isOk = parseResult(post, tasks);
    return isOk;
  }

  @Override
  public Boolean callNotify(Map<String, Object> map, HttpServletRequest request) throws Exception{

    String appkey = request.getHeader("Appkey");
    String singnature = request.getHeader("Singnature");
    String timestamp = request.getHeader("Timestamp");

    Assert.hasText(appkey, "缺少appkey");
    Assert.hasText(singnature, "缺少singnature");
    Assert.hasText(timestamp, "缺少timestamp");

    log.info("[华客云语音回调][请求头信息]：appkey：{}  singnature：{}   timestamp：{}", appkey, singnature, timestamp);

    //验签
    boolean b = verifySignature(appkey, singnature, Long.parseLong(timestamp));
    Assert.isTrue(b, "签名异常");

    log.info("[华客云语音回调][接收参数]：{}", map);

    ModCallNotify notify = new ModCallNotify();
    BeanUtil.copyProperties(map, notify);

    notify.setId(null);
    notify.setTaskId(Integer.valueOf(map.get("id").toString()));
    notify.setDeleted(BaseConstants.DATA_UNDELETED);
    notify.setCreateTime(new Date());

    if (map.containsKey("lastDialedTime") && map.get("lastDialedTime") != null){
      Calendar calendar = DateUtil.calendar(Long.parseLong(map.get("lastDialedTime").toString()));
      notify.setLastDialedTime(calendar.getTime());
    }

    Assert.notNull(notify, "获取数据异常");

    return notifyMapper.insert(notify) > 0;
  }


  /**
   * 回调验证签名
   *
   * @param appKey    appkey
   * @param signature 签名
   * @param timestamp 时间戳
   * @return boolean
   * @throws NoSuchAlgorithmException 算法异常
   *
   */
  private boolean verifySignature(String appKey, String signature, long timestamp) throws NoSuchAlgorithmException {

    if (timestamp < System.currentTimeMillis()) {
      throw new RuntimeException("signagure expired");
    }

    MessageDigest sha512 = MessageDigest.getInstance("SHA-512");
    String tempStringSignature = appKey + appSecret + timestamp;
    byte[] tempSignature = sha512.digest(tempStringSignature.getBytes());
    return signature.equals(String.valueOf(Hex.encodeHex(tempSignature)));

  }

  /**
   * 发起请求
   *
   * @param tasks 任务
   * @return {@link JSONObject}
   */
  private JSONObject sendPost(List<JSONObject> tasks) {
    Map<String, String> headers = new HashMap<>();
    headers.put("token", token);
    JSONObject post = new JSONObject();
    try {
      createHeaderParams(headers);
      Map<String, Object> params = createReqParams(tasks);
      post = HttpClientUtil.doJsonPost(url + callUri, headers, params);
//      if (log.isDebugEnabled()) {
      log.info("[请求语音电话header]：{}", headers);
      log.info("[组装请求参数]：{}", params);
      log.info("[返回结果]：{}", post);
//      }
    } catch (Exception e) {
      log.error("[请求华客云语音外呼接口异常]: {}", e);
    }
    return post;
  }

  /**
   * 创建请求参数
   *
   * @param tasks 任务
   * @return {@link JSONObject}
   */
  private JSONObject createReqParams(List<JSONObject> tasks) {
    JSONObject params = new JSONObject();
    Map<String, Object> map = new HashMap<>();
//    List<String> voices = new ArrayList<>();
//    voices.add(voiceUrl);
    //tts话术
    map.put("tts", tts);
    //录音文件地址（播报完tts再播报录音文件）
//    map.put("voiceFiles", "");
    //播报循环次数（默认无限次）
//    map.put("loopCount", "3");
    //播报循环间隔毫秒数（默认5000）
//    map.put("loopInterval", "2000");
//    语音文件
//    map.put("voiceFiles",voices);
    JSONObject jsonParam = new JSONObject();
    Set<Entry<String, Object>> entrySet = map.entrySet();
    for (Map.Entry<String, Object> entry : entrySet) {
      jsonParam.put(entry.getKey(), entry.getValue());
    }

    for (Map<String, Object> task : tasks) {
      task.put("status", CALL_STATUS);
      task.put("dialedType", "1");
      task.put("content", jsonParam.toString());
    }

    params.put("obTasks", tasks);
    params.put("type", CALL_TYPE);
    params.put("processId", processId);
    params.put("notifyUrl", notifyUrl);

    return params;
  }

  /**
   * 判断redis中是否存在token
   */
  private void getToken() {
    Object obj = this.redisTemplate.opsForValue().get(BaseConstants.CACHE_VOCUST_TOKEN);
    if (obj != null && !"".equals(obj.toString())) {
      token = obj.toString();
    } else {
      token = getTokenReq(token);
    }
  }

  /**
   * 刷新token
   *
   * @param token 令牌
   * @return {@link String}
   */
  private String getTokenReq(String token) {
    Map<String, String> headers = new HashMap<>();
    String newToken = "";
    retry += 1;
    try {
      headers.put("token", token);
      createHeaderParams(headers);
      log.info("[华客云语音外呼][获取token] url: {}   param：{}", url + tokenUri, headers);
      JSONObject json = HttpClientUtil.doJsonPost(url + tokenUri, headers, null);
      log.info("[获取token返回结果]：{}", json);
      if (json != null && !json.isEmpty() && !json.getJSONObject("responseStatusType").isEmpty()) {
        JSONObject statusType = json.getJSONObject("responseStatusType");
        if (CALL_SUCCESS.equals(statusType.get("ack"))) {
          newToken = json.getString("newToken");
          this.redisTemplate.opsForValue()
              .set(BaseConstants.CACHE_VOCUST_TOKEN, newToken);
          log.info("[获取华客云新token成功] : {}", newToken);
        } else {
          log.error("[获取华客云新token失败]：{}", statusType);
        }
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return newToken;
  }

  /**
   * 组装请求头参数
   *
   * @param headers 头
   * @throws Exception 异常
   */
  private void createHeaderParams(Map<String, String> headers) throws Exception {
    long timestamp = System.currentTimeMillis() + 300000L;
    headers.put("Appkey", appKey);
    headers.put("Singnature", createSignature(appKey, appSecret, timestamp));
    headers.put("Timestamp", String.valueOf(timestamp));
    headers.put("Content-Type", "application/json;charset=utf-8");
    headers.put("Accept", "application/json, text/plain, */*");
  }

  /**
   * 创建签名
   *
   * @param appKey    应用的关键
   * @param secret    秘密
   * @param timestamp 时间戳
   * @return {@link String}
   * @throws Exception 异常
   */
  private String createSignature(String appKey, String secret, long timestamp) throws Exception {
    try {
      MessageDigest sha512 = MessageDigest.getInstance("SHA-512");
      String tempStringSignature = appKey + secret + timestamp;
      byte[] tempSignature = sha512.digest(tempStringSignature.getBytes());
      return String.valueOf(Hex.encodeHex(tempSignature));
    } catch (NoSuchAlgorithmException e) {
      throw new Exception(e.getMessage());
    }
  }

  /**
   * 解析结果
   *
   * @param post  返回数据
   * @param tasks 任务列表
   * @return {@link Boolean}
   */
  private Boolean parseResult(JSONObject post, List<JSONObject> tasks) {
    boolean fals = false;
    if (!post.isEmpty() && !"".equals(post.get("responseStatusType"))) {
      JSONObject statusType = post.getJSONObject("responseStatusType");
      StringBuilder phoneNums = new StringBuilder();
      for (JSONObject task : tasks) {
        phoneNums.append(task.getString("dialedNumber")).append(",");
      }
      ModCallRecordLog callRecordLog = new ModCallRecordLog();
      callRecordLog.setCalledNumber(phoneNums.substring(0, phoneNums.length() - 1));
      callRecordLog.setAck(statusType.getString("ack"));
      callRecordLog.setCreateTime(new Date());
      if (CALL_SUCCESS.equals(statusType.get("ack"))) {
        callRecordLog.setTaskids(post.getString("taskIdList"));
        fals = true;
        callRecordLogMapper.insert(callRecordLog);
      } else {
        if (statusType.containsKey("errors")) {
          JSONArray errors = statusType.getJSONArray("errors");
          if (!errors.isEmpty()) {
            JSONObject json;
            List<String> errorCodes = new ArrayList<>();
            for (Object error : errors) {
              json = (JSONObject) JSONObject.toJSON(error);
              errorCodes.add(json.getString("errorCode"));
            }
            callRecordLog.setCodeDes(errorCodes.toString());
            String magesse = JSONObject.parseObject(errors.get(0).toString()).getString("message");
            if (!StringUtils.isEmpty(magesse) && magesse.length() > 255) {
              magesse = magesse.substring(0, 200);
            }
            callRecordLog.setErrorsMessage(magesse);
            //请求失败 刷新token并进行重试 当前设置重试2次
            if (errorCodes.contains(TOKEN_EXPIRED) && retry < 2) {
              log.info("[token失效重新获取并重试]:第{}次", retry);
              getTokenReq(token);
              parseResult(sendPost(tasks), tasks);
            } else {
              callRecordLogMapper.insert(callRecordLog);
            }
          }
        }
        log.error("[请求华客云语音电话失败]");
      }
    }
    return fals;
  }
}
