package com.shands.mod.message.controller;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.model.req.UmMessageReq;
import com.shands.mod.message.service.UmMessagePushService;
import com.shands.mod.message.vo.UmCallBackVo;
import com.shands.mod.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/08/10 1:55 下午 <br>
 * @see com.shands.mod.message.controller <br>
 */
@Slf4j
@RestController
@RequestMapping("/umMessagePush")
public class UmMessagePushController {

  @Autowired
  private UmMessagePushService umMessagePushService;

  @PostMapping("/sendUmMessage")
  public ResultVO<String> sendUmMessage(@RequestBody UmMessageReq umMessageReq){
    log.info("UmMessagePushController.sendUmMessage请求参数:{}", JSONObject.toJSONString(umMessageReq));
    return umMessagePushService.sendMessage(umMessageReq);
  }

  /**
   * 友盟推送回执
   * @param callBackVoList 回执列表
   * @return 响应
   */
  @PostMapping("/work/push/callback")
  public ResultVO<String> pushCallBack(@RequestBody List<UmCallBackVo> callBackVoList,
      HttpServletRequest request) {
    log.info("um推送回执内容: {}", callBackVoList);
    return umMessagePushService.pushCallback(callBackVoList, request);
  }



}
