package com.shands.mod.message.service;

import com.shands.mod.dao.model.company.WeixinMessage;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;

import java.util.List;

/**
 * 微信消息服务接口
 *
 * <AUTHOR>
 */
public interface IWeixinService {

  /**
   * 发送模版消息
   *
   * @param weixinMessage 微信消息配置
   * @param openId 微信用户openId
   * @param data 模版数据map
   * @param url 跳转的url
   * @return
   */
  boolean sendTemplateMessage(
      WeixinMessage weixinMessage, String openId, List<WxMpTemplateData> data, String url);

  /**
   * 获取企业微信配置
   *
   * @param companyId
   * @return
   */
  WeixinMessage getWeixinMessageConfig(int companyId);
}
