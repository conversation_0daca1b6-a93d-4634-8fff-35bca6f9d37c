package com.shands.mod.dao.model.v0701.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/7/28
 * @desc 服务订单创建返回参数
 */

@Data
@ApiModel(value = "服务订单创建返回参数Vo")
public class OrderInstrVo {

  @ApiModelProperty(value = "订单编号")
  private Integer orderId;

  @ApiModelProperty(value = "服务类型")
  private String serviceType;

  @ApiModelProperty(value = "订单总价")
  private BigDecimal totalPrice;

  @ApiModelProperty(value = "支付方式")
  private String payType;

  @ApiModelProperty(value = "手机号")
  private String mobile;

  @ApiModelProperty(value = "酒店编码")
  private Integer companyId;
}
