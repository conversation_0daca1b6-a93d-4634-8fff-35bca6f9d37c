package com.shands.mod.dao.model.enums;

import java.util.Arrays;

/**
 * 用户状态
 * <AUTHOR>
 * @date 2023/9/26
 **/
public enum UserStatusEnum {
  /**
   * 用户状态未知
   */
  NULL(-1,"未知"),

  ONLINE(0,"在线"),

  MEETING(1,"会议中"),

  LEAVE_MOMENT(2,"暂时离开"),

  SLEEP(3,"休息")
  ;

  /**
   * 状态码
   */
  private Integer code;

  /**
   * 状态描述
   */
  private String desc;

  UserStatusEnum(Integer code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public Integer getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  /**
   * 通过code查找状态枚举
   * @param code 状态码
   * @return 状态枚举
   */
  public static UserStatusEnum findEnumByCode(Integer code) {
    return Arrays.stream(UserStatusEnum.values()).filter(status -> status.code.equals(code))
        .findFirst().orElse(NULL);
  }
}
