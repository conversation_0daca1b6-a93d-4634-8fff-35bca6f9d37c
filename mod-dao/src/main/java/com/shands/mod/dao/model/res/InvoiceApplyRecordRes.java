package com.shands.mod.dao.model.res;

import com.shands.mod.dao.model.InvoiceApplyRecord;
import io.swagger.annotations.ApiModelProperty;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/12/07 10:59 上午 <br>
 * @see com.shands.mod.dao.model.res <br>
 */
public class InvoiceApplyRecordRes extends InvoiceApplyRecord {

  @ApiModelProperty("开票总额")
  private String amount;

  @ApiModelProperty("发票总数")
  private Integer invoiceCount;

  public String getAmount() {
    return amount;
  }

  public void setAmount(String amount) {
    this.amount = amount;
  }

  public Integer getInvoiceCount() {
    return invoiceCount;
  }

  public void setInvoiceCount(Integer invoiceCount) {
    this.invoiceCount = invoiceCount;
  }
}
