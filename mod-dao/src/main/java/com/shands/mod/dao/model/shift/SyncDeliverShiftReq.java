package com.shands.mod.dao.model.shift;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/24 16:36
 */
@Data
public class SyncDeliverShiftReq {
  /**
   * 服务类型
   */
  @NotEmpty(message = "serviceType不能为空")
  private String serviceType;
  /**
   * 排班日期
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @NotNull(message = "shiftDate不能为空")
  private Date shiftDate;
}
