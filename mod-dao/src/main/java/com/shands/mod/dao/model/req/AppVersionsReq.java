package com.shands.mod.dao.model.req;

import io.swagger.annotations.ApiModel;
import java.util.Date;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class AppVersionsReq {

  private Integer id;

  @NotBlank(message = "app类型不能为空")
  private String type;

  @NotNull(message = "app版本不能为空")
  private Integer appVersion;

  @NotBlank(message = "app详细版本不能为空")
  private String appDetail;

  @NotNull(message = "是否强制更新不能为空")
  private Integer isForce;

  @NotBlank(message = "下载地址不能为空")
  private String url;

  @NotNull(message = "状态不能为空")
  private Integer status;

  @NotBlank(message = "版本描述不能为空")
  private String remark;

  private Integer createUser;

  private Date createTime;

  private Integer updateUser;

  private Date updateTime;

  @Override
  public String toString() {
    return "AppVersionsReq{" +
        ", type='" + type + '\'' +
        ", appVersion=" + appVersion +
        ", appDetail='" + appDetail + '\'' +
        ", isForce=" + isForce +
        ", url='" + url + '\'' +
        ", status=" + status +
        ", remark='" + remark + '\'' +
        ", createUser=" + createUser +
        ", createTime=" + createTime +
        ", updateUser=" + updateUser +
        ", updateTime=" + updateTime +
        '}';
  }


}
