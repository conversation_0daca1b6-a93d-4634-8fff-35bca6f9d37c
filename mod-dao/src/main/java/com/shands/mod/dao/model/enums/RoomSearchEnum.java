package com.shands.mod.dao.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RoomSearchEnum {

  ALLROOM(1,"所有房间"),

  ALLOCATEDROOM(2,"已分配房间"),
  ABSENCEROOM(3,"未分配房间");

  private String name;

  private Integer code;

  RoomSearchEnum(int code, String name) {
    this.code = code;
    this.name = name;
  }
  public static RoomSearchEnum int2Priority(int i) {
    switch (i) {
      case 1:
        return ALLROOM;
      case 2:
        return ALLOCATEDROOM;
      case 3:
        return ABSENCEROOM;
    }
    return null;
  }
}
