package com.shands.mod.dao.model.quality.bo.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("设置默认配置请求参数")
public class DefaultSettingBo {

  @ApiModelProperty("默认任务时长")
  private Integer defaultTaskTime;

  @ApiModelProperty("默认优先级")
  private String defaultPriority;

  @ApiModelProperty("酒店id")
  private Integer hotelId;

  @ApiModelProperty("创建人")
  private Integer createUser;

}
