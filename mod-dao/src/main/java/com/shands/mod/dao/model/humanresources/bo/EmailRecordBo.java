package com.shands.mod.dao.model.humanresources.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/10/25
 **/
@Data
@ApiModel("查询邮件记录 入参")
public class EmailRecordBo {

  @ApiModelProperty("邮箱")
  private String sendStatus;

  @ApiModelProperty("文件表id")
  private Integer fileId;

  @ApiModelProperty(value = "页行数",required = true)
  private Integer pageNo;

  @ApiModelProperty(value = "页编码",required = true)
  private Integer pageSize;

  @ApiModelProperty("姓名")
  private String name;

  @ApiModelProperty("酒店名称")
  private String hotelName;
}
