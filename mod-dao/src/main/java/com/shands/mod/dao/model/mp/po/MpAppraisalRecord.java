package com.shands.mod.dao.model.mp.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;

/**
 * 工单配置字段详情表(MpAppraisalRecord)实体类
 *
 * <AUTHOR>
 * @since 2022-11-03 11:40:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class MpAppraisalRecord implements Serializable {
    
    private Integer id;
    /**
     * 版本号
版本号
     */
    private Integer version;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private Integer createUser;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改人
     */
    private Integer updateUser;
    /**
     * 方案code
     */
    private String taskCode;
    /**
     * 总经理id
     */
    private Integer userId;
    /**
     * 总经理姓名
     */
    private String userName;

    private Date bizDate;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 事业部code
     */
    private String deptCode;
    /**
     * 事业部
     */
    private String deptName;
    /**
     * 品牌code
     */
    private String brandCode;
    /**
     * 品牌
     */
    private String brandName;
    /**
     * 考核分
     */
    private Double assessScore;
    /**
     * 综合分
     */
    private Double synthesizeScore;
    /**
     * 排名
     */
    private Integer rank;
    /**
     * 星级
     */
    private Integer star;

    private String remark;

    private String picture;

    private String hotelStatus;

    private Integer ifStar;

    private Date startDate;

    private Date endDate;

    private String hotelName;

    private String hotelCode;

    private Double totalAssessScore;
}

