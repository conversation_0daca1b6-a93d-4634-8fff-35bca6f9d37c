package com.shands.mod.dao.model.workorder.po;

import com.shands.mod.dao.model.workorder.enums.DictTypeEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 操作日志(NwDict)实体类
 *
 * <AUTHOR>
 * @since 2022-05-05 15:56:28
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class NwOpterDetail implements Serializable {

  private static final long serialVersionUID = -38150123242838655L;

  private Integer id;
  /**
   * 创建时间
   */
  private Date createTime;

  private String tableType;

  private Integer tableId;

  private String beforeData;

  private String afterData;

  private Integer opterUser;
}

