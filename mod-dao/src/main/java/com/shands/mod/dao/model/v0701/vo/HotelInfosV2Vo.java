package com.shands.mod.dao.model.v0701.vo;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/11
 * @desc 酒店列表返回对象v2
*/

@Data
public class HotelInfosV2Vo {

  @ApiModelProperty(value = "酒店类型")
  private String hotelInfoType;

  @ApiModelProperty(value = "酒店列表")
  private List<HotelInfosVo> hotelInfos;

  public HotelInfosV2Vo(String key, List<HotelInfosVo> value) {
    this.hotelInfoType = key;
    this.hotelInfos = value;
  }
}
