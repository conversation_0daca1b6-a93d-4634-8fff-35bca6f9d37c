<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.training.ModCourseLabelMapper">

    <resultMap type="com.shands.mod.dao.model.training.po.ModCourseLabel" id="ModCourseLabelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="labelName" column="label_name" jdbcType="VARCHAR"/>
        <result property="labelType" column="label_type" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ModCourseLabelMap">
        select
        id,label_name,label_type from mod_course_label
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ModCourseLabelMap">
        select
          id, label_name, label_type
        from mod_course_label
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="labelName != null and labelName != ''">
                and label_name = #{labelName}
            </if>
            <if test="labelType != null and labelType != ''">
                and label_type = #{labelType}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mod_course_label
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="labelName != null and labelName != ''">
                and label_name = #{labelName}
            </if>
            <if test="labelType != null and labelType != ''">
                and label_type = #{labelType}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course_label(label_name, label_type,hotel_code)
        values (#{labelName}, #{labelType},#{hotelCode})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course_label(label_name, label_type)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.labelName}, #{entity.labelType})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course_label(label_name, label_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.labelName}, #{entity.labelType})
        </foreach>
        on duplicate key update
        label_name = values(label_name),
        label_type = values(label_type)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mod_course_label
        <set>
            <if test="labelName != null and labelName != ''">
                label_name = #{labelName},
            </if>
            <if test="labelType != null and labelType != ''">
                label_type = #{labelType},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mod_course_label where id = #{id}
    </delete>

  <select id="queryLabel" resultType="com.shands.mod.dao.model.training.vo.LabelVo">
  select id as labelId ,label_name as labelName from mod_course_label where hotel_code=#{labelBo.hotelId}
  and label_type=#{labelBo.labelType}
    <if test="labelBo.labelName != null and labelBo.labelName != ''">
      and label_name  like CONCAT('%',#{labelBo.labelName},'%')
    </if>
  </select>

  <select id="selectLabelByCourseId" resultType="java.lang.String">
  SELECT cl.label_name from mod_course mc
  LEFT JOIN mod_course_label  cl on FIND_IN_SET(cl.id,mc.label_id) WHERE mc.id=#{courseId}
  </select>
  <select id="queryOneLabel" resultType="com.shands.mod.dao.model.training.vo.LabelVo">
    select id as labelId ,label_name as labelName from mod_course_label where hotel_code=#{labelBo.hotelId}
    and label_type=#{labelBo.labelType} and label_name = #{labelBo.labelName}
  </select>
  <select id="selectLabelByCollectionId" resultType="java.lang.String">

     SELECT cl.label_name from mod_course_collection mc
  LEFT JOIN mod_course_label  cl on FIND_IN_SET(cl.id,mc.label_id) WHERE mc.id=#{collectionId}

  </select>

</mapper>