package com.shands.mod.dao.model.res.hs.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("计划卫生房间列表中楼宇信息")
public class RoomListForBuildRes {

  @ApiModelProperty("楼宇名称")
  private String buildName;

  @ApiModelProperty("楼宇code")
  private String building;

  @ApiModelProperty("楼层")
  private List<RoomListForFloorRes> floorRes;

  @ApiModelProperty("未分配房间")
  private String dirtyStr;

}
