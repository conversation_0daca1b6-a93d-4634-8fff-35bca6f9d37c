package com.shands.mod.dao.model.datarevision.vo;

import com.shands.mod.dao.util.ThousandSeparatorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("会员法宝分析")
public class MemberMagicVo {

  @ApiModelProperty("名称")
  private String key;

  @ApiModelProperty("父级架构名")
  private String parentName;

  @ApiModelProperty("发展人数")
  private BigDecimal memberAppendNum;

  @ApiModelProperty("千分位发展人数")
  private String memberAppendNumStr;

  @ApiModelProperty("领取人数")
  private BigDecimal magicObtainNum;

  @ApiModelProperty("千分位领取人数")
  private String magicObtainNumStr;

  @ApiModelProperty("领取率")
  private BigDecimal obtainPercent;

  private List<MemberMagicVo> child;

  public String getMemberAppendNumStr() {
    memberAppendNumStr = ThousandSeparatorUtil.format(memberAppendNum);
    return memberAppendNumStr;
  }

  public String getMagicObtainNumStr() {
    magicObtainNumStr = ThousandSeparatorUtil.format(magicObtainNum);
    return magicObtainNumStr;
  }
}
