package com.shands.mod.dao.mapper.fw;

import com.shands.mod.dao.model.fw.FwReserveTime;
import com.shands.mod.dao.model.res.fw.ReserveTimeRes;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FwReserveTimeMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(FwReserveTime record);

    int insertSelective(FwReserveTime record);

    FwReserveTime selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FwReserveTime record);

    int updateByPrimaryKey(FwReserveTime record);

    int batchInsert(@Param("list") List<FwReserveTime> list);

    List<ReserveTimeRes> listByReserve(@Param("reserveId") Integer reserveId,@Param("companyId") int companyId,
    @Param("group") int group);

    int deleted(Integer reserveId);

    int sum(FwReserveTime time);

  void del(@Param("id") Integer id, @Param("companyId") int companyId,
      @Param("group") int group);

}