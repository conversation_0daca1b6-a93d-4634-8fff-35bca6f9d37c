package com.shands.mod.dao.mapper.mp;

import com.shands.mod.dao.model.mp.po.MpPriceInversion;
import com.shands.mod.dao.model.mp.vo.PriceInversionListVo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 价格倒挂记录(MpPriceInversion)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-16 10:16:13
 */
public interface MpPriceInversionDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MpPriceInversion queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param mpPriceInversion 查询条件
     * @return 对象列表
     */
    List<MpPriceInversion> queryAllByLimit(MpPriceInversion mpPriceInversion);

    /**
     * 统计总行数
     *
     * @param mpPriceInversion 查询条件
     * @return 总行数
     */
    long count(MpPriceInversion mpPriceInversion);

    /**
     * 新增数据
     *
     * @param mpPriceInversion 实例对象
     * @return 影响行数
     */
    int insert(MpPriceInversion mpPriceInversion);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<MpPriceInversion> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<MpPriceInversion> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<MpPriceInversion> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<MpPriceInversion> entities);

    /**
     * 修改数据
     *
     * @param mpPriceInversion 实例对象
     * @return 影响行数
     */
    int update(MpPriceInversion mpPriceInversion);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<PriceInversionListVo> selectList(@Param("taskCode") String taskCode,@Param("userId") Integer userId,@Param("hotelCode") String hotelCode);

    Integer queryAbnormalNum(@Param("userId") Integer userId);

  /**
   * 根据用户id列表查询
   *
   * @param userIds
   * @return
   */
  Integer queryAbnormalNumByUserIds(List<Integer> userIds);

}

