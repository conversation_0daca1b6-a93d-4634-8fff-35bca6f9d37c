package com.shands.mod.dao.model.req.elsreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("会员消费数据接口请求参数")
@Data
public class MemberDeconsumptionReq {

  @ApiModelProperty("会员类型")
  private String memberType;

  @ApiModelProperty("业务日期")
  private String dateStr;


  @ApiModelProperty("酒店code")
  private String hotelCodes;

  @ApiModelProperty("对照年")
  private String contrastDateStr;

  private Integer pageNo;
  private Integer pageSize;

  @ApiModelProperty("分类维度")
  private String statisticalEnum;

  @ApiModelProperty("酒店code")
  private String hotelCode;


}
