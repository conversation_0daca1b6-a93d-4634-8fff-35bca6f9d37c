package com.shands.mod.dao.model.req.hs.goods;

import com.shands.mod.dao.model.req.hs.HsBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class GoodsTypeAddAddReq extends HsBase {
  /** 分类id */
  private Integer id;
  /** 分类名称 */
  private String name;
  /** 上下架状态 1有效 */
  // private Integer status;

  private Integer hotelServiceId;

  private String serviceType;

  private Integer listByServiceId;
}
