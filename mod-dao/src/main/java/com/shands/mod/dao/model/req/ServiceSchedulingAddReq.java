package com.shands.mod.dao.model.req;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/11/25 1:26 下午 <br>
 * @see com.shands.mod.dao.model.req <br>
 */
public class ServiceSchedulingAddReq {


  @ApiModelProperty("楼宇、楼层、房间号")
  private List<Area> areaList;

  private List<ServiceReq> serviceReqs;

  private List<Date> dateList;

  private ServiceSchedulingShiftsReq serviceSchedulingShiftsReq;

  private List<ServiceSchedulingUser> serviceSchedulingUsers;


  public List<Area> getAreaList() {
    return areaList;
  }

  public void setAreaList(List<Area> areaList) {
    this.areaList = areaList;
  }

  public List<ServiceReq> getServiceReqs() {
    return serviceReqs;
  }

  public void setServiceReqs(List<ServiceReq> serviceReqs) {
    this.serviceReqs = serviceReqs;
  }

  public List<Date> getDateList() {
    return dateList;
  }

  public void setDateList(List<Date> dateList) {
    this.dateList = dateList;
  }

  public ServiceSchedulingShiftsReq getServiceSchedulingShiftsReq() {
    return serviceSchedulingShiftsReq;
  }

  public void setServiceSchedulingShiftsReq(ServiceSchedulingShiftsReq serviceSchedulingShiftsReq) {
    this.serviceSchedulingShiftsReq = serviceSchedulingShiftsReq;
  }

  public List<ServiceSchedulingUser> getServiceSchedulingUsers() {
    return serviceSchedulingUsers;
  }

  public void setServiceSchedulingUsers(List<ServiceSchedulingUser> serviceSchedulingUsers) {
    this.serviceSchedulingUsers = serviceSchedulingUsers;
  }
}
