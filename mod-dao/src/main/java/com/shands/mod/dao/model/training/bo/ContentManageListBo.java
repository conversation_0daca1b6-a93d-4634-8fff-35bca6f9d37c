package com.shands.mod.dao.model.training.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("课程管理页面 入参")
public class ContentManageListBo {
  @ApiModelProperty("课程合集主键id")
  private Integer collectionId;

  @ApiModelProperty("课程名称")
  private String coursesName;

  @ApiModelProperty("课程类型（视频，pdf，图文）")
  private String type;

  @ApiModelProperty("状态（已上架，已下架）")
  private Integer status;
}
