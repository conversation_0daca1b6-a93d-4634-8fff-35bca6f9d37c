package com.shands.mod.dao.model.res.invoice;

import com.shands.mod.dao.model.invoice.ModAssociatedOrder;
import com.shands.mod.dao.model.invoice.ModInvioceProject;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class InvoiceDetailRes {

  private Integer id;
  private String serialNo;
  private String invoiceNo;
  private String applyNo;
  private Integer invoiceType;
  private Integer status;
  private String workUnitName;
  private String userName;
  private String staffMark;
  private String taxNumber;
  private String bankName;
  private String bankAccount;
  private String workUnitAddress;
  private String companyPhone;
  private Date createTime;
  private String roomNumber;
  private String personName;
  private String email;
  private String realName;
  private List<OpenInvoiceProjectRes> projectList;
  List<AssociatedOrderRes> orders;
}
