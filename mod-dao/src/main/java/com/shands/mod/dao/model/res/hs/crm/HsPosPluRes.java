package com.shands.mod.dao.model.res.hs.crm;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 菜项返回类
 *
 * <AUTHOR>
 * @date 2021/05/18
 */
@Data
@ApiModel
public class HsPosPluRes {
  private static final long serialVersionUID = 654747874441142474L;
  /**
   * 菜项 绿云pos系统
   */
  private Integer id;
  /**
   * 菜类代码
   */
  private String sortCode;
  /**
   * 菜项代码
   */
  private String pluCode;
  /**
   * 菜项描述  菜项名称
   */
  private String pluDescript;
  /**
   * 价格
   */
  private Double price;
  /**
   * 标注位
   */
  private String pluFlag;
  /**
   * 单位
   */
  private String unit;
  /**
   * 报表数据项
   */
  private String toCode;
  /**
   * 图片地址
   */
  private String pluPhotoUrl;
  /**
   * 用户上传的图片地址
   */
  private String pluPhotoUrlNew;
  /**
   * 描述
   */
  private String pluDesc;
  /**
   * 估清菜剩余数量
   */
  private String pluNumber;
  /**
   * 状态 1有效 0无效
   */
  private Integer status;
  /**
   * 菜本code
   */
  private String noteCode;
  /**
   * 营业点名称
   */
  private String outletName;
  /**
   * 营业点code
   */
  private String outletCode;
  /**
   * 排序
   */
  private Integer num;
  /**
   * 是否临时菜 1：是  0：否
   */
  private Integer tempFlag;
  /**
   * 是否打折菜 1：是  0：否
   */
  private String discountFlag;
  /**
   * 所属公司id
   */
  private Integer companyId;
  /**
   * 集团id
   */
  private Integer groupId;
  /**
   * 删除标志
   */
  private Integer deleted;
  /**
   * 创建时间
   */
  private Date createTime;
  /**
   * 创建用户
   */
  private Integer createUser;
  /**
   * 更新用户
   */
  private Integer updateUser;
  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 菜类
   */
  private String sortDescript;

  /**
   * 菜本
   */
  private String noteDescript;

  /**
   * 做法列表
   */
  private List<HsPosPluPracticeRes> practiceList;

  /**
   * 菜项英文名称 plu_descript_en
   */
  private String pluDescriptEn;

}
