package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.CustomerOrder;
import com.shands.mod.dao.model.req.hs.consumer.ConsumerOrderQueryReq;
import com.shands.mod.dao.model.req.hs.mobile.MobileConsumerOrderQueryReq;
import com.shands.mod.dao.model.req.hs.workorder.WorkOrderConsOrderQueryReq;
import com.shands.mod.dao.model.res.hs.app.consumer.ConsumerOrderRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerOrderMapper {


  int deleteByPrimaryKey(Integer id);

  int insert(CustomerOrder record);

  int insertSelective(CustomerOrder record);

  CustomerOrder selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(CustomerOrder record);

  int updateByPrimaryKey(CustomerOrder record);

  List<CustomerOrder> findByOrderState(
      @Param("consumerOrderQueryReq") ConsumerOrderQueryReq consumerOrderQueryReq);

  List<CustomerOrder> workOrderSelect(
      @Param("workOrderConsOrderQueryReq") WorkOrderConsOrderQueryReq workOrderConsOrderQueryReq);

  /**
   * 查询未支付状态的订单
   *
   * @param toPayCode 未支付 code
   * @param earlyTime 超时时间
   * @return
   */
  List<CustomerOrder> findByToPayAndMinute(
      @Param("toPayCode") int toPayCode, @Param("earlyTime") int earlyTime);

  int findByCompIdCount(@Param("companyId") int companyId);

  CustomerOrder findBySn(@Param("sn") String sn);

  List<CustomerOrder> findByCompIdAndGroupId(
      @Param("compId") Integer compId, @Param("hotelServiceId") Integer hotelServiceId);

  List<CustomerOrder> findAll();

  List<CustomerOrder> queryByNameAndPhone(
      @Param("mobileConsumerOrderQueryReq")
          MobileConsumerOrderQueryReq mobileConsumerOrderQueryReq);

  List<CustomerOrder> findByHotelServiceAndGroupIdAndCompanyId(
      @Param("hotelServiceId") Integer hotelServiceId,
      @Param("groupId") Integer groupId,
      @Param("companyId") Integer companyId);

  List<ConsumerOrderRes> newfindByStatys(@Param("consumerOrderQueryReq") ConsumerOrderQueryReq consumerOrderQueryReq);

  List<CustomerOrder> todoConsumerOrder(@Param("consumerOrderQueryReq") ConsumerOrderQueryReq consumerOrderQueryReq);


}
