<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.hs.WorkOrderMapper">
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.hs.WorkOrder">
    <id column="ID" jdbcType="INTEGER" property="id"/>
    <result column="SN" jdbcType="VARCHAR" property="sn"/>
    <result column="CUSTOMER_ORDER_ID" jdbcType="INTEGER" property="customerOrderId"/>
    <result column="HOTEL_SERVICE_ID" jdbcType="INTEGER" property="hotelServiceId"/>
    <result column="SOURCE" jdbcType="TINYINT" property="source"/>
    <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
    <result column="WORK_TIME" jdbcType="INTEGER" property="workTime"/>
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="ACCEPT_DEPT" jdbcType="INTEGER" property="acceptDept"/>
    <result column="ACCEPT_USER" jdbcType="INTEGER" property="acceptUser"/>
    <result column="PRIORITY" jdbcType="TINYINT" property="priority"/>
    <result column="PICTURE" jdbcType="VARCHAR" property="picture"/>
    <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
    <result column="SITE_ID" jdbcType="INTEGER" property="siteId"/>
    <result column="PATROL_TASK_ID" jdbcType="INTEGER" property="patrolTaskId"/>
    <result column="SEND_TO" jdbcType="VARCHAR" property="sendTo"/>
    <result column="STATUS" jdbcType="TINYINT" property="status"/>
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
    <result column="GROUP_ID" jdbcType="INTEGER" property="groupId"/>
    <result column="VERSION" jdbcType="INTEGER" property="version"/>
    <result column="DELETED" jdbcType="TINYINT" property="deleted"/>
    <result column="CREATE_USER" jdbcType="INTEGER" property="createUser"/>
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="UPDATE_USER" jdbcType="INTEGER" property="updateUser"/>
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="MANAGER_ID" jdbcType="INTEGER" property="managerId"/>
    <result column="REPAY_TIME" jdbcType="TIMESTAMP" property="repayTime"/>
    <result column="CUSTOMER_ODER_NAME" jdbcType="VARCHAR" property="customerOderName"/>
    <result column="CUSTOMER_ODER_PHONE" jdbcType="VARCHAR" property="customerOderPhone"/>
    <result column="CUSTOMER_ODER_SN" jdbcType="VARCHAR" property="customerOderSn"/>
    <result column="SUBSCRIBE" jdbcType="TINYINT" property="subscribe"/>
    <result column="SUB_TIME" jdbcType="TIMESTAMP" property="subTime"/>
    <result column="HOTEL_SERVICE_EXTEND_ID" jdbcType="VARCHAR" property="hotelServiceExtendId"/>
    <result column="DINING_STYLE" jdbcType="VARCHAR" property="diningStyle"/>
    <result column="DINING_TABLE_ID" jdbcType="INTEGER" property="diningTableId"/>
    <result column="ROOM_NUM" jdbcType="VARCHAR" property="roomNum"/>
    <result column="ORDER_MASTER" jdbcType="INTEGER" property="orderMaster"/>
    <result column="promote_state" jdbcType="TINYINT" property="promoteState"/>
    <result column="predict_time_over" jdbcType="INTEGER" property="predictTimeOver"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, SN, CUSTOMER_ORDER_ID, HOTEL_SERVICE_ID, SOURCE, ADDRESS, WORK_TIME, END_TIME,
    ACCEPT_DEPT, ACCEPT_USER, PRIORITY, PICTURE, REMARK, SITE_ID, PATROL_TASK_ID, SEND_TO,
    STATUS, COMPANY_ID, GROUP_ID, VERSION, DELETED, CREATE_USER, CREATE_TIME, UPDATE_USER,
    UPDATE_TIME,MANAGER_ID,REPAY_TIME,CUSTOMER_ODER_NAME,CUSTOMER_ODER_PHONE,CUSTOMER_ODER_SN,
    SUBSCRIBE,SUB_TIME,HOTEL_SERVICE_EXTEND_ID,DINING_STYLE,DINING_TABLE_ID,ROOM_NUM,ORDER_MASTER,promote_state,
    predict_time_over
  </sql>

  <sql id="columnSql">
    a.id                                                AS id,
       a.sn                                                AS sn,
       a.CUSTOMER_ORDER_ID                                 AS customerOrderId,
       a.HOTEL_SERVICE_ID                                  as hotelServiceId,
       b.CNNAME                                            as hotelServiceName,
       b.SERVICE_TYPE                                      as hotelServiceType,
       a.SOURCE                                            as SOURCE,
       a.ADDRESS                                           as address,
       a.ROOM_NUM                                          as roomNum,
       a.WORK_TIME                                         as workTime,
       a.END_TIME                                          as endTime,
       a.ACCEPT_DEPT                                       as acceptDept,
       a.ACCEPT_USER                                       as acceptUser,
       a.PRIORITY                                          as priority,
       a.PICTURE                                           as picture,
       a.REMARK                                            as remark,
       a.SITE_ID                                           as siteId,
       a.PATROL_TASK_ID                                    as patrolTaskId,
       a.SEND_TO                                           as sendTo,
       a.`STATUS`                                          as `status`,
       a.COMPANY_ID                                        as companyId,
       a.GROUP_ID                                          as groupId,
       a.MANAGER_ID                                        as managerId,
       a.REPAY_TIME                                        as repayTime,
       a.CUSTOMER_ODER_NAME                                as customerOderName,
       a.CUSTOMER_ODER_PHONE                               as customerOderPhone,
       a.CUSTOMER_ODER_SN                                  as customerOderSn,
       a.SUB_TIME                                          as subTime,
       a.SUBSCRIBE                                         as subscribe,
       a.DINING_STYLE                                      as diningStyle,
       c.`NAME`                                            as hotelServiceExtendName,
       a.CREATE_TIME                                       as createTime,
       a.promote_state                                     as promoteState,
       ms.service_extend_type                              as serviceExtendType,
       case when a.SUB_TIME >= NOW() then '1' else '2' end AS result
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete
    from hs_work_order
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.hs.WorkOrder">
    insert into hs_work_order (ID, SN, CUSTOMER_ORDER_ID,
                               HOTEL_SERVICE_ID, SOURCE, ADDRESS,
                               WORK_TIME, END_TIME, ACCEPT_DEPT,
                               ACCEPT_USER, PRIORITY, PICTURE,
                               REMARK, SITE_ID, PATROL_TASK_ID,
                               SEND_TO, STATUS, COMPANY_ID,
                               GROUP_ID, VERSION, DELETED,
                               CREATE_USER, CREATE_TIME, UPDATE_USER,
                               UPDATE_TIME, MANAGER_ID, REPAY_TIME, CUSTOMER_ODER_NAME,
                               CUSTOMER_ODER_PHONE, CUSTOMER_ODER_SN,
                               SUBSCRIBE, SUB_TIME, HOTEL_SERVICE_EXTEND_ID, DINING_STYLE,
                               DINING_TABLE_ID, ROOM_NUM,ORDER_MASTER)
    values (#{id,jdbcType=INTEGER}, #{sn,jdbcType=VARCHAR}, #{customerOrderId,jdbcType=INTEGER},
            #{hotelServiceId,jdbcType=INTEGER}, #{source,jdbcType=TINYINT},
            #{address,jdbcType=VARCHAR},
            #{workTime,jdbcType=INTEGER}, #{endTime,jdbcType=TIMESTAMP},
            #{acceptDept,jdbcType=INTEGER},
            #{acceptUser,jdbcType=INTEGER}, #{priority,jdbcType=TINYINT},
            #{picture,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR}, #{siteId,jdbcType=INTEGER},
            #{patrolTaskId,jdbcType=INTEGER},
            #{sendTo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER},
            #{groupId,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT},
            #{createUser,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
            #{updateUser,jdbcType=INTEGER},
            #{updateTime,jdbcType=TIMESTAMP}, #{managerId,jdbcType=INTEGER},
            #{repayTime,jdbcType=TIMESTAMP},
            #{customerOderName,jdbcType=VARCHAR}, #{customerOderPhone,jdbcType=VARCHAR},
            #{customerOderSn,jdbcType=VARCHAR},
            #{subscribe,jdbcType=INTEGER}, #{subTime,jdbcType=TIMESTAMP},
            #{hotelServiceExtendId,jdbcType=VARCHAR}, #{diningStyle,jdbcType=VARCHAR},
            #{diningTableId,jdbcType=INTEGER}, #{roomNum,jdbcType=VARCHAR},#{orderMaster})

  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.hs.WorkOrder"
          useGeneratedKeys="true"
          keyColumn="id" keyProperty="id">
    insert into hs_work_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="sn != null">
        SN,
      </if>
      <if test="customerOrderId != null">
        CUSTOMER_ORDER_ID,
      </if>
      <if test="hotelServiceId != null">
        HOTEL_SERVICE_ID,
      </if>
      <if test="source != null">
        SOURCE,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="workTime != null">
        WORK_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="acceptDept != null">
        ACCEPT_DEPT,
      </if>
      <if test="acceptUser != null">
        ACCEPT_USER,
      </if>
      <if test="priority != null">
        PRIORITY,
      </if>
      <if test="picture != null">
        PICTURE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="siteId != null">
        SITE_ID,
      </if>
      <if test="patrolTaskId != null">
        PATROL_TASK_ID,
      </if>
      <if test="sendTo != null">
        SEND_TO,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="version != null">
        VERSION,
      </if>
      <if test="deleted != null">
        DELETED,
      </if>
      <if test="createUser != null">
        CREATE_USER,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateUser != null">
        UPDATE_USER,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="managerId != null">
        MANAGER_ID,
      </if>
      <if test="repayTime != null">
        REPAY_TIME,
      </if>
      <if test="customerOderName != null and customerOderName != ''">
        CUSTOMER_ODER_NAME,
      </if>
      <if test="customerOderPhone != null and customerOderPhone != ''">
        CUSTOMER_ODER_PHONE,
      </if>
      <if test="customerOderSn != null and customerOderSn != ''">
        CUSTOMER_ODER_SN,
      </if>
      <if test="subscribe != null">
        SUBSCRIBE,
      </if>
      <if test="subTime != null">
        SUB_TIME,
      </if>
      <if test="hotelServiceExtendId != null and hotelServiceExtendId != ''">
        HOTEL_SERVICE_EXTEND_ID,
      </if>
      <if test="diningStyle != null and diningStyle != ''">
        DINING_STYLE,
      </if>
      <if test="diningTableId != null">
        DINING_TABLE_ID,
      </if>
      <if test="orderMaster != null and orderMaster != ''">
        ORDER_MASTER,
      </if>
      <if test="roomNum != null and roomNum != ''">
        ROOM_NUM,
      </if>
      <if test="promoteState != null">
        promote_state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderId != null">
        #{customerOrderId,jdbcType=INTEGER},
      </if>
      <if test="hotelServiceId != null">
        #{hotelServiceId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="workTime != null">
        #{workTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="acceptDept != null">
        #{acceptDept,jdbcType=INTEGER},
      </if>
      <if test="acceptUser != null">
        #{acceptUser,jdbcType=INTEGER},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=TINYINT},
      </if>
      <if test="picture != null">
        #{picture,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="siteId != null">
        #{siteId,jdbcType=INTEGER},
      </if>
      <if test="patrolTaskId != null">
        #{patrolTaskId,jdbcType=INTEGER},
      </if>
      <if test="sendTo != null">
        #{sendTo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="managerId != null">
        #{managerId,jdbcType=INTEGER},
      </if>
      <if test="repayTime != null">
        #{repayTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerOderName != null and customerOderName != ''">
        #{customerOderName,jdbcType=VARCHAR},
      </if>
      <if test="customerOderPhone != null and customerOderPhone != ''">
        #{customerOderPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerOderSn != null and customerOderSn != ''">
        #{customerOderSn,jdbcType=VARCHAR},
      </if>
      <if test="subscribe != null">
        #{subscribe,jdbcType=INTEGER},
      </if>
      <if test="subTime != null">
        #{subTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hotelServiceExtendId != null and hotelServiceExtendId != ''">
        #{hotelServiceExtendId,jdbcType=VARCHAR},
      </if>
      <if test="diningStyle != null and diningStyle != ''">
        #{diningStyle,jdbcType=VARCHAR},
      </if>
      <if test="diningTableId != null">
        #{diningTableId,jdbcType=INTEGER},
      </if>
      <if test="orderMaster != null and orderMaster != ''">
        #{orderMaster,jdbcType=VARCHAR},
      </if>
      <if test="roomNum != null and roomNum != ''">
        #{roomNum,jdbcType=VARCHAR},
      </if>
      <if test="promoteState != null">
        #{promoteState,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.hs.WorkOrder">
    update hs_work_order
    <set>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderId != null">
        CUSTOMER_ORDER_ID = #{customerOrderId,jdbcType=INTEGER},
      </if>
      <if test="hotelServiceId != null">
        HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        SOURCE = #{source,jdbcType=TINYINT},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="workTime != null">
        WORK_TIME = #{workTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="acceptDept != null">
        ACCEPT_DEPT = #{acceptDept,jdbcType=INTEGER},
      </if>
      <if test="acceptUser != null">
        ACCEPT_USER = #{acceptUser,jdbcType=INTEGER},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority,jdbcType=TINYINT},
      </if>
      <if test="picture != null">
        PICTURE = #{picture,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="siteId != null">
        SITE_ID = #{siteId,jdbcType=INTEGER},
      </if>
      <if test="patrolTaskId != null">
        PATROL_TASK_ID = #{patrolTaskId,jdbcType=INTEGER},
      </if>
      <if test="sendTo != null">
        SEND_TO = #{sendTo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        GROUP_ID = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        DELETED = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        CREATE_USER = #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="managerId != null">
        MANAGER_ID = #{managerId,jdbcType=INTEGER},
      </if>
      <if test="repayTime != null">
        REPAY_TIME = #{repayTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerOderName != null and customerOderName != ''">
        CUSTOMER_ODER_NAME = #{customerOderName,jdbcType=VARCHAR},
      </if>
      <if test="customerOderPhone != null and customerOderPhone != ''">
        CUSTOMER_ODER_PHONE = #{customerOderPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerOderSn != null and customerOderSn != ''">
        CUSTOMER_ODER_SN = #{customerOderSn,jdbcType=VARCHAR},
      </if>
      <if test="subscribe != null">
        SUBSCRIBE=#{subscribe,jdbcType=INTEGER},
      </if>
      <if test="subTime != null">
        SUB_TIME=#{subTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hotelServiceExtendId != null and hotelServiceExtendId != ''">
        HOTEL_SERVICE_EXTEND_ID = #{hotelServiceExtendId,jdbcType=VARCHAR},
      </if>
      <if test="diningStyle != null and diningStyle != ''">
        DINING_STYLE = #{diningStyle,jdbcType=VARCHAR},
      </if>
      <if test="diningTableId != null">
        DINING_TABLE_ID = #{diningTableId,jdbcType=INTEGER},
      </if>
      <if test="roomNum != null and roomNum != ''">
        ROOM_NUM = #{roomNum,jdbcType=VARCHAR},
      </if>
      <if test="promoteState != null">
        promote_state = #{promoteState,jdbcType=TINYINT},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeySelectiveV2" parameterType="com.shands.mod.dao.model.hs.WorkOrder">
    update hs_work_order
    <set>
      ACCEPT_DEPT = #{acceptDept,jdbcType=INTEGER},
      ACCEPT_USER = #{acceptUser,jdbcType=INTEGER},
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderId != null">
        CUSTOMER_ORDER_ID = #{customerOrderId,jdbcType=INTEGER},
      </if>
      <if test="hotelServiceId != null">
        HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        SOURCE = #{source,jdbcType=TINYINT},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="workTime != null">
        WORK_TIME = #{workTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority,jdbcType=TINYINT},
      </if>
      <if test="picture != null">
        PICTURE = #{picture,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="siteId != null">
        SITE_ID = #{siteId,jdbcType=INTEGER},
      </if>
      <if test="patrolTaskId != null">
        PATROL_TASK_ID = #{patrolTaskId,jdbcType=INTEGER},
      </if>
      <if test="sendTo != null">
        SEND_TO = #{sendTo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        GROUP_ID = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        DELETED = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        CREATE_USER = #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="managerId != null">
        MANAGER_ID = #{managerId,jdbcType=INTEGER},
      </if>
      <if test="repayTime != null">
        REPAY_TIME = #{repayTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerOderName != null and customerOderName != ''">
        CUSTOMER_ODER_NAME = #{customerOderName,jdbcType=VARCHAR},
      </if>
      <if test="customerOderPhone != null and customerOderPhone != ''">
        CUSTOMER_ODER_PHONE = #{customerOderPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerOderSn != null and customerOderSn != ''">
        CUSTOMER_ODER_SN = #{customerOderSn,jdbcType=VARCHAR},
      </if>
      <if test="subscribe != null">
        SUBSCRIBE=#{subscribe,jdbcType=INTEGER},
      </if>
      <if test="subTime != null">
        SUB_TIME=#{subTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hotelServiceExtendId != null and hotelServiceExtendId != ''">
        HOTEL_SERVICE_EXTEND_ID = #{hotelServiceExtendId,jdbcType=VARCHAR},
      </if>
      <if test="diningStyle != null and diningStyle != ''">
        DINING_STYLE = #{diningStyle,jdbcType=VARCHAR},
      </if>
      <if test="diningTableId != null">
        DINING_TABLE_ID = #{diningTableId,jdbcType=INTEGER},
      </if>
      <if test="roomNum != null and roomNum != ''">
        ROOM_NUM = #{roomNum,jdbcType=VARCHAR}
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.hs.WorkOrder">
    update hs_work_order
    set SN                     = #{sn,jdbcType=VARCHAR},
        CUSTOMER_ORDER_ID      = #{customerOrderId,jdbcType=INTEGER},
        HOTEL_SERVICE_ID       = #{hotelServiceId,jdbcType=INTEGER},
        SOURCE                 = #{source,jdbcType=TINYINT},
        ADDRESS                = #{address,jdbcType=VARCHAR},
        WORK_TIME              = #{workTime,jdbcType=INTEGER},
        END_TIME               = #{endTime,jdbcType=TIMESTAMP},
        ACCEPT_DEPT            = #{acceptDept,jdbcType=INTEGER},
        ACCEPT_USER            = #{acceptUser,jdbcType=INTEGER},
        PRIORITY               = #{priority,jdbcType=TINYINT},
        PICTURE                = #{picture,jdbcType=VARCHAR},
        REMARK                 = #{remark,jdbcType=VARCHAR},
        SITE_ID                = #{siteId,jdbcType=INTEGER},
        PATROL_TASK_ID         = #{patrolTaskId,jdbcType=INTEGER},
        SEND_TO                = #{sendTo,jdbcType=VARCHAR},
        STATUS                 = #{status,jdbcType=TINYINT},
        COMPANY_ID             = #{companyId,jdbcType=INTEGER},
        GROUP_ID               = #{groupId,jdbcType=INTEGER},
        VERSION                = #{version,jdbcType=INTEGER},
        DELETED                = #{deleted,jdbcType=TINYINT},
        CREATE_USER            = #{createUser,jdbcType=INTEGER},
        CREATE_TIME            = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER            = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME            = #{updateTime,jdbcType=TIMESTAMP},
        MANAGER_ID             = #{managerId,jdbcType=INTEGER},
        REPAY_TIME             = #{repayTime,jdbcType=TIMESTAMP},
        CUSTOMER_ODER_NAME     =#{customerOderName,jdbcType=VARCHAR},
        CUSTOMER_ODER_PHONE    = #{customerOderPhone,jdbcType=VARCHAR},
        CUSTOMER_ODER_SN=#{customerOderSn,jdbcType=VARCHAR},
        SUBSCRIBE=#{subscribe,jdbcType=INTEGER},
        SUB_TIME=#{subTime,jdbcType=TIMESTAMP},
        HOTEL_SERVICE_EXTEND_ID=#{hotelServiceExtendId,jdbcType=VARCHAR},
        DINING_STYLE=#{diningStyle,jdbcType=VARCHAR},
        DINING_TABLE_ID=#{diningTableId,jdbcType=INTEGER},
        ROOM_NUM=#{roomNum,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <update id="updatePromoteState">
    update hs_work_order
    set promote_state = #{promoteState}
    where ID = #{workId,jdbcType=INTEGER}
  </update>

  <!-- 自定义SQL -->
  <select id="statisticsBySource"
          resultType="com.shands.mod.dao.model.res.hs.report.StatisticBySourceRes">
    SELECT * from
    (SELECT
    DATE_FORMAT(a.CREATE_TIME,"%Y-%m-%d") as `date`,COUNT(*) as customer
    FROM
    hs_work_order a
    WHERE
    1=1
    <if test="date !=null">
      and a.CREATE_TIME &gt;= concat(#{date},' 00:00:00')
    </if>
    <if test="date != null">
      AND a.CREATE_TIME &lt;= concat(#{date},' 23:59:59')
    </if>
    and a.SOURCE=1)c
    LEFT JOIN (SELECT
    COUNT(*) as hotel
    FROM
    hs_work_order a
    WHERE
    1=1
    <if test="date !=null">
      and a.CREATE_TIME &gt;= concat(#{date},' 00:00:00')
    </if>
    <if test="date != null">
      AND a.CREATE_TIME &lt;= concat(#{date},' 23:59:59')
    </if>
    and a.SOURCE=2)b on 1=1
  </select>
  <select id="findByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order where CUSTOMER_ORDER_ID=#{customerOrderId,jdbcType=INTEGER}
  </select>
  <select id="todayWorkOrderStatistics" resultType="java.lang.Integer">

    select count(*) from hs_work_order
    where
    1=1
    <if test="userId != null">and ACCEPT_USER = #{userId,jdbcType=INTEGER}</if>
    <if test="companyId != null">and COMPANY_ID = #{companyId,jdbcType=INTEGER}</if>
    <if test="dept != null">and ACCEPT_DEPT = #{dept,jdbcType=INTEGER}</if>
    and DATE_SUB(CURDATE(), INTERVAL 0 DAY) &lt;= date(CREATE_TIME)
  </select>
  <select id="workOrderCompletedStatistics" resultType="java.lang.Integer">
    select count(*)
    from hs_work_order
    where
    1=1
    <if test="userId != null">and ACCEPT_USER = #{userId,jdbcType=INTEGER}</if>
    <if test="companyId != null">and COMPANY_ID = #{companyId,jdbcType=INTEGER}</if>
    <if test="dept != null">and ACCEPT_DEPT=#{dept,jdbcType=INTEGER}</if>
    <if test="states !=null">

      and STATUS in
      <foreach collection="states" open="(" separator="," close=")" item="haha">
        #{haha}
      </foreach>
    </if>
    and DATE_SUB(CURDATE(), INTERVAL 0 DAY) &lt;= date(END_TIME)

  </select>
  <select id="earlyWarringWorkOrderStatistics" resultType="java.lang.Integer">
    select count(*) from hs_work_order
    where 1=1
    <if test="userId != null">and ACCEPT_USER = #{userId,jdbcType=INTEGER}</if>
    <if test="companyId != null">and COMPANY_ID = #{companyId,jdbcType=INTEGER}</if>
    <if test="groupId != null">and GROUP_ID =#{groupId,jdbcType=INTEGER}</if>
    <if test="dept != null">and ACCEPT_DEPT = #{dept,jdbcType=INTEGER}</if>
    <if test="earlyWorkOrderId !=null ">

      and ID in
      <foreach collection="earlyWorkOrderId" open="(" separator="," close=")" item="haha">
        #{haha}
      </foreach>
    </if>
  </select>

  <select id="query" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    a.ROOM_NUM as roomNum,
    b.SERVICE_TYPE as hotelServiceType,
    b.CNNAME as hotelServiceName,
    hhse.name as serviceContent,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    if(a.ACCEPT_USER = #{workOrderQueryReq.userId},1,0) as isHandler,
    a.PRIORITY as priority,
    a.SITE_ID as siteId,
    a.`STATUS` as `status`,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.CREATE_TIME as createTime,
    a.COMPANY_ID as companyId,
    c.NAME as acceptUserName,
    muc.NAME as createUserName,
    d.NAME as acceptDeptName,
    ms.service_extend_type as serviceExtendType
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    left join hs_hotel_service_extend hhse on hhse.id = a.HOTEL_SERVICE_EXTEND_ID
    left join mod_user c on a.ACCEPT_USER = c.ID
    LEFT JOIN mod_user muc ON a.CREATE_USER = muc.ID
    left join mod_dept d on a.ACCEPT_DEPT=d.id
    left join hs_work_order_goods_info hws on hws.work_order_id = a.id
    left join mod_service ms on b.mod_service_id = ms.ID
    where 1=1
    <if test="workOrderQueryReq.hotelService != null and workOrderQueryReq.hotelService.size() > 0">
      and a.HOTEL_SERVICE_ID in
      <foreach collection="workOrderQueryReq.hotelService" index="index"
        item="service" open="(" separator="," close=")">
        #{service}
      </foreach>
    </if>
    <if test="workOrderQueryReq.status != null and workOrderQueryReq.status.size() > 0">
      and a.STATUS in
      <foreach collection="workOrderQueryReq.status" index="index"
        item="state" open="(" separator="," close=")">
        #{state}
      </foreach>
    </if>
    <if test="workOrderQueryReq.dept != null and workOrderQueryReq.dept.size() > 0">
      and a.ACCEPT_DEPT in
      <foreach collection="workOrderQueryReq.dept" index="index"
        item="depart" open="(" separator="," close=")">
        #{depart}
      </foreach>
    </if>
    <if test="workOrderQueryReq.priority != null and workOrderQueryReq.priority != ''">
      and a.PRIORITY = #{workOrderQueryReq.priority,jdbcType=INTEGER}
    </if>
    <if test="workOrderQueryReq.workId != null">
      and a.id = #{workOrderQueryReq.workId,jdbcType=INTEGER}
    </if>
    <if test="workOrderQueryReq.patrolTaskId != null">
      and a.PATROL_TASK_ID = #{workOrderQueryReq.patrolTaskId,jdbcType=INTEGER}
    </if>
    <if test="workOrderQueryReq.workOrderSn != null and workOrderQueryReq.workOrderSn != ''">
      and a.SN = #{workOrderQueryReq.workOrderSn}
    </if>
    <if test="workOrderQueryReq.orderId != null and workOrderQueryReq.orderId != ''">
      and a.CUSTOMER_ODER_SN = #{workOrderQueryReq.orderId}
    </if>
    <if test="workOrderQueryReq.hotelServiceId != null and workOrderQueryReq.hotelServiceId != ''">
      and a.HOTEL_SERVICE_ID = #{workOrderQueryReq.hotelServiceId}
    </if>
    <if
      test="workOrderQueryReq.workOrderStateId != null and workOrderQueryReq.workOrderStateId != ''">
      and a.STATUS = #{workOrderQueryReq.workOrderStateId}
    </if>
    <if test="workOrderQueryReq.timeType == 1">
      <if test="workOrderQueryReq.startTime != null ">
        and a.CREATE_TIME &gt;= #{workOrderQueryReq.startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="workOrderQueryReq.endTime != null ">
        and a.CREATE_TIME &lt;= #{workOrderQueryReq.endTime,jdbcType=TIMESTAMP}
      </if>
    </if>
    <if test="workOrderQueryReq.timeType == 2">
      <if test="workOrderQueryReq.startTime != null ">
        and a.END_TIME &gt;= #{workOrderQueryReq.startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="workOrderQueryReq.endTime != null  ">
        and a.END_TIME &lt;= #{workOrderQueryReq.endTime,jdbcType=TIMESTAMP}
      </if>
    </if>
    <if test="workOrderQueryReq.sourceId != null and workOrderQueryReq.sourceId!= ''">
      and a.SOURCE = #{workOrderQueryReq.sourceId}
    </if>
    <if test="workOrderQueryReq.accDeptId != null and workOrderQueryReq.accDeptId != null">
      and a.ACCEPT_DEPT = #{workOrderQueryReq.accDeptId}
    </if>
    <if test="workOrderQueryReq.accDeptUserId != null and workOrderQueryReq.accDeptUserId != ''">
      and a.ACCEPT_USER = #{workOrderQueryReq.accDeptUserId}
    </if>
    <if test="workOrderQueryReq.roomNum != null and workOrderQueryReq.roomNum != null">
      and a.ROOM_NUM like CONCAT('%',#{workOrderQueryReq.roomNum},'%')
    </if>
    <if test="workOrderQueryReq.serviceContent != null and workOrderQueryReq.serviceContent != null">
      and (
      hhse.name like CONCAT('%',#{workOrderQueryReq.serviceContent},'%')
      or
      hws.good_name like CONCAT('%',#{workOrderQueryReq.serviceContent},'%')
      )
    </if>
    <if test="workOrderQueryReq.accDeptUserName != null and workOrderQueryReq.accDeptUserName != ''">
      and c.NAME like CONCAT('%',#{workOrderQueryReq.accDeptUserName},'%')
    </if>
    <if test="workOrderQueryReq.createUserName != null and workOrderQueryReq.createUserName != ''">
      and muc.NAME like CONCAT('%',#{workOrderQueryReq.createUserName},'%')
    </if>
    <if test="workOrderQueryReq.companyId != null">
      and a.COMPANY_ID = #{workOrderQueryReq.companyId,jdbcType=INTEGER}
    </if>
    <if test="workOrderQueryReq.groupId != null">
      and a.GROUP_ID = #{workOrderQueryReq.groupId,jdbcType=INTEGER}
    </if>
    group by id
    order by a.CREATE_TIME desc
  </select>

  <select id="todayWorkOrder"
          resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    a.ROOM_NUM as roomNum,
    b.SERVICE_TYPE as hotelServiceType,
    b.CNNAME as hotelServiceName,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    c.`NAME` as hotelServiceExtendName,
    a.CREATE_TIME as createTime,
    ms.service_extend_type as serviceExtendType,
    case when a.SUB_TIME>=NOW() then '1'
    else '2' end
    AS result
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join mod_service ms on b.mod_service_id = ms.ID
    where 1=1
    <if test="today != null">
      and a.CREATE_TIME &gt;= #{today}
    </if>
    <if test="tomorrow != null">
      and a.CREATE_TIME &lt; #{tomorrow}
    </if>
    <if test="status != null and status.size() > 0">
      and a.STATUS in
      <foreach collection="status" open="(" separator="," close=")" item="status">
        #{status}
      </foreach>
    </if>
    <if test="userId != null">
      and a.ACCEPT_USER = #{userId}
    </if>
    <if test="deptId != null">
      and a.ACCEPT_DEPT = #{deptId}
    </if>
    <if test="companyId != null">and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}</if>
    order by a.priority desc,a.CREATE_TIME desc,result, a.SUB_TIME
  </select>

  <select id="todayCompeletWorkOrder"
          resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    a.ROOM_NUM as roomNum,
    b.SERVICE_TYPE as hotelServiceType,
    b.CNNAME as hotelServiceName,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    c.`NAME` as hotelServiceExtendName,
    a.CREATE_TIME as createTime,
    ms.service_extend_type as serviceExtendType,
    case when a.SUB_TIME>=NOW() then '1'
    else '2' end
    AS result
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join mod_service ms on b.mod_service_id = ms.ID
    where 1=1
    <if test="today != null">
      and a.END_TIME &gt;= #{today}
    </if>
    <if test="tomorrow != null">
      and a.END_TIME &lt; #{tomorrow}
    </if>
    <if test="status != null and status.size() > 0">
      and a.STATUS in
      <foreach collection="status" open="(" separator="," close=")" item="status">
        #{status}
      </foreach>
    </if>
    <if test="userId != null">
      and a.ACCEPT_USER = #{userId}
    </if>
    <if test="deptId != null">
      and a.ACCEPT_DEPT = #{deptId}
    </if>
    <if test="companyId != null">and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}</if>
    order by a.priority desc,a.CREATE_TIME desc,result, a.SUB_TIME
  </select>

  <select id="earlyWorkOrder"
          resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    b.CNNAME as hotelServiceName,
    b.SERVICE_TYPE as hotelServiceType,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.ROOM_NUM as roomNum,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    c.`NAME` as hotelServiceExtendName,
    a.CREATE_TIME as createTime,
    case when a.SUB_TIME>=NOW() then '1'
    else '2' end
    AS result
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    where 1=1
    <if test="id != null and id.size() > 0">
      and a.ID in
      <foreach collection="id" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    <if test="userId != null">
      and a.ACCEPT_USER = #{userId}
    </if>
    <if test="companyId != null">
      and a.COMPANY_ID = #{companyId}
    </if>
    <if test="dept">
      and a.ACCEPT_DEPT = #{dept}
    </if>

    order by a.priority desc,a.CREATE_TIME desc,result, a.SUB_TIME
  </select>

  <select id="findByStateAndDas" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    WHERE 1=1
    and DATE_SUB(CURDATE(), INTERVAL #{taskQuery.day,jdbcType=INTEGER} DAY) = date(END_TIME)
    AND STATUS = #{taskQuery.workState , jdbcType=INTEGER}
  </select>

  <select id="compWorkOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    where 1=1
    <if test="today != null">
      and END_TIME &gt; #{today}
    </if>
    <if test="tomorrow != null">
      and END_TIME &lt; #{tomorrow}
    </if>
    <if test="status != null and status.size() > 0">
      and STATUS in
      <foreach collection="status" open="(" separator="," close=")" item="status">
        #{status}
      </foreach>
    </if>
    <if test="userId != null">
      and ACCEPT_USER = #{userId}
    </if>
    <if test="deptId != null">
      and ACCEPT_DEPT = #{deptId}
    </if>
  </select>

  <select id="create" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    where 1=1
    <if test="today != null">
      and CREATE_TIME &gt;= concat(#{today},' 00:00:00')
    </if>
    <if test="tomorrow != null">
      and CREATE_TIME &lt;= concat(#{tomorrow},' 23:59:59')
    </if>
    <if test="deptId != null">
      and ACCEPT_DEPT = #{deptId}
    </if>
    <if test="userId != null">
      and ACCEPT_USER = #{userId}
    </if>
    and COMPANY_ID = #{companyId}
  </select>

  <select id="end" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    where 1=1
    <if test="today != null">
      and END_TIME &gt; #{today}
    </if>
    <if test="tomorrow != null">
      and END_TIME &lt; #{tomorrow}
    </if>
    <if test="deptId != null">
      and ACCEPT_DEPT = #{deptId}
    </if>
    and COMPANY_ID = #{companyId}
  </select>
  <select id="findByCompIdCount" resultType="int">
    select count(*)
    from hs_work_order
    where COMPANY_ID = #{companyId}
  </select>
  <select id="remind" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    where 1=1
    <if test="workOrderId != null and workOrderId.size() > 0">
      and ID in
      <foreach collection="workOrderId" open="(" separator="," close=")" item="workOrderId">
        #{workOrderId}
      </foreach>
    </if>
    <if test="today != null">
      and END_TIME &gt; #{today}
    </if>
    <if test="tomorrow != null">
      and END_TIME &lt; #{tomorrow}
    </if>
    <if test="deptId != null">
      and ACCEPT_DEPT = #{deptId}
    </if>
    <if test="userId != null">
      and ACCEPT_USER = #{userId}
    </if>
    and COMPANY_ID = #{companyId}
  </select>


  <select id="groupServiceCount"
          resultType="com.shands.mod.dao.model.res.hs.report.StatisticByWorkDb">
    SELECT
    b.SERVICE_TYPE as hotelService, count(*) as totel,
    DATE_FORMAT(a.CREATE_TIME,"%Y-%m-%d") as `date`
    FROM
    hs_work_order a
    LEFT JOIN hs_hotel_service b on a.HOTEL_SERVICE_ID=b.id
    WHERE
    1=1
    <if test="statisticsByWorkTypeReq.startDay !=null">
      and a.CREATE_TIME &gt;= concat(#{statisticsByWorkTypeReq.startDay},' 00:00:00')
    </if>
    <if test="statisticsByWorkTypeReq.endDay != null">
      AND a.CREATE_TIME &lt;= concat(#{statisticsByWorkTypeReq.endDay},' 23:59:59')
    </if>
    group by a.HOTEL_SERVICE_ID
  </select>
  <select id="findWorkOrderCount" resultType="java.util.Map">
    SELECT
    d.userId as userId,IFNULL(c.workOrderCount,0) as workOrderCount
    FROM
    (SELECT a.id as userId from mod_user a LEFT JOIN mod_user_extend b on a.ID=b.USER_ID where
    b.DEPT_ID=
    #{deptId})d
    left JOIN (SELECT a.ACCEPT_USER as userId,COUNT(*) as workOrderCount from hs_work_order a WHERE
    a.CREATE_TIME >= CURDATE()
    AND a.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    <if test="status != null and status.size() > 0">
      and a.`STATUS` in
      <foreach collection="status" open="(" separator="," close=")" item="status">
        #{status}
      </foreach>
    </if>
    GROUP BY userId
    ORDER BY workOrderCount)c on c.userId=d.userId
  </select>
  <select id="untreatedWorkOrderCount" resultType="long">
    select count(id)
    from hs_work_order
    where 1=1
    <if test="companyId != null">and COMPANY_ID = #{companyId,jdbcType=INTEGER}</if>
    <if test="dept != null">
      and ACCEPT_DEPT = #{dept}
    </if>
    <if test="userId != null">
      and ACCEPT_USER = #{userId}
    </if>
    and STATUS not in (99,7,401,10)
  </select>
  <select id="newById" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    SELECT
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    b.SERVICE_TYPE as hotelServiceType,
    c.NAME as hotelServiceExtendName,
    b.CNNAME as hotelServiceName,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.ROOM_NUM as roomNum,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    a.CREATE_TIME as createTime,
    e.PAY_STATE as payStatus,
    a.CREATE_USER as createUser,
    a.CREATE_TIME as createTime,
    GROUP_CONCAT(f.PAY_TYPE) as payType
    FROM
    hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join hs_customer_order_n e on a.CUSTOMER_ORDER_ID=e.ID
    LEFT JOIN hs_hotel_service_pay_type f on a.HOTEL_SERVICE_ID=f.HOTEL_SERVICE_ID
    WHERE
    1=1
    <if test="workOrderId != null">
      and a.ID = #{workOrderId}
    </if>
    group by a.id
  </select>

<!-- 根据工单编号查询工单详情 -->
  <select id="selectWorkOrderInfoById" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    SELECT
    uu.name AS acceptUserName,
    mm.name AS acceptDeptName,
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    b.SERVICE_TYPE as hotelServiceType,
    c.NAME as hotelServiceExtendName,
    a.HOTEL_SERVICE_EXTEND_ID as hotelServiceExtendId,
    b.CNNAME as hotelServiceName,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.ROOM_NUM as roomNum,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as consumerNote,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    a.CREATE_TIME as createTime,
    e.PAY_STATE as payStatus,
    a.CREATE_USER as createUser,
    a.CREATE_TIME as createTime,
    ms.service_extend_type as serviceExtendType,
    GROUP_CONCAT(f.PAY_TYPE) as payType
    FROM
    hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    LEFT join hs_customer_order_n e on a.CUSTOMER_ORDER_ID=e.ID
    LEFT JOIN hs_hotel_service_pay_type f on a.HOTEL_SERVICE_ID=f.HOTEL_SERVICE_ID
    LEFT JOIN mod_user uu ON a.ACCEPT_USER = uu.id
    LEFT JOIN mod_dept mm ON mm.id = a.ACCEPT_DEPT
    left join mod_service ms on b.mod_service_id = ms.ID
    WHERE a.ID = #{workOrderId} and a.DELETED = 0
  </select>

  <!--auto generated by MybatisCodeHelper on 2020-07-21-->
  <select id="appQuery" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    a.ROOM_NUM as roomNum,
    b.SERVICE_TYPE as hotelServiceType,
    b.CNNAME as hotelServiceName,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    e.PAY_STATE as payStatus,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    c.`NAME` as hotelServiceExtendName,
    a.CREATE_TIME as createTime,
    a.promote_state as promoteState,
    ms.service_extend_type as serviceExtendType,
    GROUP_CONCAT(f.PAY_TYPE) as payType,
    case when a.SUB_TIME>=NOW() then '1'
    else '2' end
    AS result
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join hs_customer_order_n e on a.CUSTOMER_ORDER_ID=e.ID
    LEFT JOIN hs_hotel_service_pay_type f on a.HOTEL_SERVICE_ID=f.HOTEL_SERVICE_ID
    left join mod_service ms on b.mod_service_id = ms.ID
    where 1=1
    <if test="WorkOrderAppQuery.status != null and WorkOrderAppQuery.status.size() > 0">
      and a.STATUS in
      <foreach collection="WorkOrderAppQuery.status" open="(" separator="," close=")" item="status">
        #{status}
      </foreach>
    </if>
    <if test="WorkOrderAppQuery.dept != null and WorkOrderAppQuery.dept.size() > 0">
      and a.ACCEPT_DEPT in
      <foreach collection="WorkOrderAppQuery.dept" open="(" separator="," close=")" item="dept">
        #{dept}
      </foreach>
    </if>
    <if test="WorkOrderAppQuery.hotelService != null and WorkOrderAppQuery.hotelService.size() > 0">
      and a.HOTEL_SERVICE_ID in
      <foreach collection="WorkOrderAppQuery.hotelService" open="(" separator="," close=")"
               item="hotelService">
        #{hotelService}
      </foreach>
    </if>
    <if test="WorkOrderAppQuery.priority != null">
      and a.PRIORITY = #{WorkOrderAppQuery.priority,jdbcType=INTEGER}
    </if>
    and (a.COMPANY_ID = #{WorkOrderAppQuery.companyId,jdbcType=INTEGER} or a.ACCEPT_USER = #{WorkOrderAppQuery.userId,jdbcType=INTEGER})
    group by a.id
    order by
    <choose>
      <when test="WorkOrderAppQuery.sort == 'create_time'">
        a.CREATE_TIME desc
      </when>
      <otherwise>
        a.END_TIME desc
      </otherwise>
    </choose>
  </select>

  <!--auto generated by MybatisCodeHelper on 2020-07-21-->
  <select id="findByStatus" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    b.CNNAME as hotelServiceName,
    b.SERVICE_TYPE as hotelServiceType,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.ROOM_NUM as roomNum,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    c.`NAME` as hotelServiceExtendName,
    a.CREATE_TIME as createTime,
    ms.service_extend_type as serviceExtendType,
    case when a.SUB_TIME>=NOW() then '1'
    else '2' end
    AS result
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join mod_service ms on b.mod_service_id = ms.ID
    where 1=1
    <if
      test="defaultMobileIndexWorkOrderReq.sendTo != null and defaultMobileIndexWorkOrderReq.sendTo != ''">
      and find_in_set( #{defaultMobileIndexWorkOrderReq.sendTo,jdbcType=INTEGER}, a.SEND_TO )
    </if>
    <if
      test="defaultMobileIndexWorkOrderReq.createUserId != null and defaultMobileIndexWorkOrderReq.createUserId != ''">
      and a.CREATE_USER in (#{defaultMobileIndexWorkOrderReq.createUserId,jdbcType=INTEGER})
    </if>
    <if
      test="defaultMobileIndexWorkOrderReq.userId != null and defaultMobileIndexWorkOrderReq.userId != ''">
      and a.ACCEPT_USER = #{defaultMobileIndexWorkOrderReq.userId,jdbcType=INTEGER}
    </if>
    <if
      test="defaultMobileIndexWorkOrderReq.deptId != null and defaultMobileIndexWorkOrderReq.deptId != ''">
      and a.ACCEPT_DEPT = #{defaultMobileIndexWorkOrderReq.deptId}
    </if>
    <if test="defaultMobileIndexWorkOrderReq.status != null">
      and a.STATUS in
      <foreach collection="defaultMobileIndexWorkOrderReq.status" open="(" separator="," close=")"
               item="status">
        #{status}
      </foreach>
    </if>
    <if
      test="defaultMobileIndexWorkOrderReq.companyId != null and defaultMobileIndexWorkOrderReq.companyId != ''">
      and a.COMPANY_ID = #{defaultMobileIndexWorkOrderReq.companyId}
    </if>
    group by id
    order by a.CREATE_TIME desc
  </select>

  <select id="findTodoPend" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    <include refid="columnSql"/>
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    LEFT JOIN mod_message_log mml on a.id = mml.work_id
    left join mod_service ms on b.mod_service_id = ms.ID
    <where>
      <if
        test="defaultMobileIndexWorkOrderReq.userId != null and defaultMobileIndexWorkOrderReq.userId2 != ''">
        and a.status in (4,5) and mml.recipient_id = #{defaultMobileIndexWorkOrderReq.userId2}
      </if>
      <if
        test="defaultMobileIndexWorkOrderReq.companyId != null and defaultMobileIndexWorkOrderReq.companyId != ''">
        and a.COMPANY_ID = #{defaultMobileIndexWorkOrderReq.companyId}
      </if>
    </where>

    group by id
    union all
    select <include refid="columnSql"/>
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join mod_service ms on b.mod_service_id = ms.ID
    <where>
      <if
        test="defaultMobileIndexWorkOrderReq.userId != null and defaultMobileIndexWorkOrderReq.userId2 != ''">
        a.status in (5,6,-1) and a.ACCEPT_USER = #{defaultMobileIndexWorkOrderReq.userId}
      </if>
      <if
        test="defaultMobileIndexWorkOrderReq.companyId != null and defaultMobileIndexWorkOrderReq.companyId != ''">
        and a.COMPANY_ID = #{defaultMobileIndexWorkOrderReq.companyId}
      </if>
    </where>

    group by id
    order by createTime desc
  </select>

  <select id="findTodoFinish" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderByIdRes">
    select
    a.id AS id,
    a.sn AS sn,
    a.CUSTOMER_ORDER_ID AS customerOrderId,
    a.HOTEL_SERVICE_ID as hotelServiceId,
    b.CNNAME as hotelServiceName,
    b.SERVICE_TYPE as hotelServiceType,
    a.SOURCE as SOURCE,
    a.ADDRESS as address,
    a.ROOM_NUM as roomNum,
    a.WORK_TIME as workTime,
    a.END_TIME as endTime,
    a.ACCEPT_DEPT as acceptDept,
    a.ACCEPT_USER as acceptUser,
    a.PRIORITY as priority,
    a.PICTURE as picture,
    a.REMARK as remark,
    a.SITE_ID as siteId,
    a.PATROL_TASK_ID as patrolTaskId,
    a.SEND_TO as sendTo,
    a.`STATUS` as `status`,
    a.COMPANY_ID as companyId,
    a.GROUP_ID as groupId,
    a.MANAGER_ID as managerId,
    a.REPAY_TIME as repayTime,
    a.CUSTOMER_ODER_NAME as customerOderName,
    a.CUSTOMER_ODER_PHONE as customerOderPhone,
    a.CUSTOMER_ODER_SN as customerOderSn,
    a.SUB_TIME as subTime,
    a.SUBSCRIBE as subscribe,
    a.DINING_STYLE as diningStyle,
    c.`NAME` as hotelServiceExtendName,
    a.CREATE_TIME as createTime,
    a.promote_state as promoteState,
    ms.service_extend_type as serviceExtendType,
    case when a.SUB_TIME>=NOW() then '1'
    else '2' end
    AS result
    from hs_work_order a
    LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.ID
    LEFT JOIN hs_hotel_service_extend c ON a.HOTEL_SERVICE_EXTEND_ID = c.ID
    left join mod_service ms on b.mod_service_id = ms.ID
    where 1=1
    <if
      test="defaultMobileIndexWorkOrderReq.userId != null and defaultMobileIndexWorkOrderReq.userId != ''">
      and (a.status in (99,10) and a.ACCEPT_USER = #{defaultMobileIndexWorkOrderReq.userId})
    </if>
    <if
      test="defaultMobileIndexWorkOrderReq.companyId != null and defaultMobileIndexWorkOrderReq.companyId != ''">
      and a.COMPANY_ID = #{defaultMobileIndexWorkOrderReq.companyId}
    </if>
    group by id
    order by a.end_time desc
  </select>

  <select id="findByStatusCount" resultType="java.lang.Integer">
    select
    count(*)
    from hs_work_order a
    where a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
    <if test="deptId != null">
      and a.ACCEPT_DEPT=#{deptId,jdbcType=INTEGER}
    </if>
    <if test="userId != null">
      and a.ACCEPT_USER=#{userId,jdbcType=INTEGER}
    </if>
    <if test="status != null">
      and a.STATUS in
      <foreach collection="status" open="(" separator="," close=")" item="status">
        #{status}
      </foreach>
    </if>
  </select>

  <!--auto generated by MybatisCodeHelper on 2020-08-06-->
  <select id="findByCustomerOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_work_order
    where CUSTOMER_ORDER_ID=#{customerOrderId,jdbcType=INTEGER}
  </select>

  <select id="findComplateVo" resultType="com.shands.mod.dao.model.res.hs.workorder.ComlateWorkOrderVo">
    SELECT
      ww.room_num as roomNum,
      hh.cnname as serviceName,
      ww.CUSTOMER_ODER_NAME as customerName,
      hh.SERVICE_TYPE serviceType
    FROM
      hs_work_order ww
      INNER JOIN hs_hotel_service hh ON ww.hotel_service_id = hh.id
    WHERE
      ww.id = #{workOrderId}
  </select>

  <!--查询所有待审核的工单id-->
  <select id="getAllIdByStatus" resultType="java.lang.Integer">
	   SELECT
	  id
    FROM
	  hs_work_order
    WHERE
	  DELETED = 0
	  AND COMPANY_ID = #{companyId}
	  AND `STATUS` = #{status}
	  AND id NOT IN ( SELECT work_order_id FROM hs_pc_message WHERE user_id = #{userId} AND company_id = #{companyId} GROUP BY work_order_id )
  </select>

  <!--24小时 工单总数-->
  <select id="countWork" resultType="com.shands.mod.dao.model.req.hs.report.CountWorkRes">
    SELECT
    hh.HOUR as specialTime,
    ifnull( WORK.work_count, 0 )  workSum
    FROM
    mod_hours_list hh
    LEFT JOIN (
    SELECT HOUR
    ( create_time ) AS work_time,
    count( 1 ) AS work_count
    FROM
    hs_work_order
    WHERE deleted = 0
    and  company_id = #{statisticsAppReq.companyId}
    <if test="statisticsAppReq.type ==1">
      and CREATE_TIME >= CURDATE()
      AND CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==2">
      and CREATE_TIME >= date_add(CURDATE(), interval -6 day)
      AND CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==3">
      and CREATE_TIME >= date_add(CURDATE(), interval -29 day)
      AND CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==-1">
      and create_time >= #{statisticsAppReq.startDate,jdbcType=TIMESTAMP}
      and create_time &lt; date_add(#{statisticsAppReq.endDate,jdbcType=TIMESTAMP}, interval 1 day)
    </if>
    GROUP BY
    HOUR ( CREATE_TIME ))WORK ON hh.HOUR = WORK.work_time
    ORDER BY
    hh.HOUR
  </select>
  <!--具体时间 每个服务工单有多少-->
  <select id="workByTime" resultType="com.shands.mod.dao.model.req.hs.report.StatisticsAppRes">
    SELECT
	  count( hwo.id ) as count,
	  hhs.CNNAME as serviceName
    FROM
	  hs_work_order hwo
	  LEFT JOIN hs_hotel_service hhs ON hwo.hotel_service_id = hhs.id
    WHERE
	  hwo.company_id = #{statisticsAppReq.companyId}
	  AND hhs.company_id = #{statisticsAppReq.companyId}
	  AND HOUR ( hwo.CREATE_TIME ) = #{statisticsAppReq.specialTime}
    <if test="statisticsAppReq.type ==1">
      and hwo.CREATE_TIME >= CURDATE()
      AND hwo.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==2">
      and hwo.CREATE_TIME >= date_add(CURDATE(), interval -6 day)
      AND hwo.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==3">
      and hwo.CREATE_TIME >= date_add(CURDATE(), interval -29 day)
      AND hwo.CREATE_TIME &lt; date_add(CURDATE(), interval 1 day)
    </if>
    <if test="statisticsAppReq.type ==-1">
      and hwo.create_time >= #{statisticsAppReq.startDate,jdbcType=TIMESTAMP}
      and hwo.create_time &lt; date_add(#{statisticsAppReq.endDate,jdbcType=TIMESTAMP}, interval 1 day)
    </if>
    GROUP BY
	  hwo.hotel_service_id
  </select>
  <select id="queryWorkDiscount"
          resultType="com.shands.mod.dao.model.v0701.vo.WorkDiscountVo">
    SELECT n.total_price totalPrice, n.actual_price actualPrice, n.discount_ratio discountRatio,
           n.discount_price discountPrice, n.discount_context discountContext, n.service_ratio serviceRatio,
           n.service_price servicePrice, n.pay_state payState, n.pay_type payType,b.card_no  cardNum,n.pos_num posNum
           FROM hs_customer_order_n n left join hs_work_order a on a.CUSTOMER_ORDER_ID=n.id
            left join hs_sks_card_pay_log b on n.id = b.order_no
            where a.id=#{id}
            group by a.id
  </select>

  <!--服务工单量-->
  <select id="orderService" resultType="java.lang.Integer">
    select count(1)
    from hs_work_order
    where deleted = 0
    and company_id = #{companyId}
    <if test="acceptUser == null">
      AND DATE_FORMAT( create_time, '%Y-%m-%d' ) = DATE_FORMAT( now( ), '%Y-%m-%d' )
    </if>
    <if test="acceptUser != null">
      AND DATE_FORMAT( END_TIME, '%Y-%m-%d' ) = DATE_FORMAT( now( ), '%Y-%m-%d' )
     and accept_user = #{acceptUser}
     and status = 99
    </if>
  </select>

  <update id="updatePredictTimeOverById">
    update hs_work_order
    set predict_time_over = #{over}
    where id = #{workId}
  </update>
  <select id="queryDetailByWorkOrderSn" resultType="com.shands.mod.dao.model.res.hs.workorder.WorkOrderBySnRes">
    select
    hws.ID as id,
    hws.SN AS sn,
    hws.ROOM_NUM as roomNum,
    mhi.hotel_name as hotelName,
    hhs.SERVICE_TYPE as serviceType,
    hhs.CNNAME as serviceName,
    hhse.name as serviceContent,
    hws.SOURCE as source,
    ms.service_extend_type as serviceExtendType,
    hws.STATUS as statusId,
    hws.CREATE_TIME as createTime,
    hws.END_TIME as finishTime,
    md.uc_id as acceptUcDeptId,
    md.name as acceptDeptName,
    mu.uc_id as acceptUserUcId,
    mu.NAME as acceptUserName
    from hs_work_order hws
    LEFT JOIN hs_hotel_service hhs ON hws.HOTEL_SERVICE_ID = hhs.ID
    left join hs_hotel_service_extend hhse on hhse.id = hws.HOTEL_SERVICE_EXTEND_ID
    left join mod_dept md on md.id = hws.ACCEPT_DEPT
    left join mod_user mu on mu.id = hws.ACCEPT_USER
    left join mod_hotel_info mhi on mhi.hotel_id = hws.COMPANY_ID
    left join mod_service ms on ms.ID = hhs.mod_service_id
    where hws.sn in
    <foreach collection="workOrderSnList" open="(" separator="," close=")" item="workOrderSn">
      #{workOrderSn}
    </foreach>
  </select>

</mapper>