package com.shands.mod.dao.model;

import java.util.Date;

public class PackageWriteOffRecord {
    private Integer id;

    private String ticketNumber;

    private String goodCode;

    private String validityPeriod;

    private String mobile;

    private Integer companyId;

    private Date createTime;

    private String operateMan;

    private String remark;

    private String ticketName;

    //类型：rightsOff权益，packageOff包价  WriteOffTypeEnum
    private String type;

    //房间号
    private String roomNo;

    private Integer staffId;

    //customer_name 用户姓名
    private String customerName;

    //crs_no crs订单号
    private String crsNo;

    private int ifVoucherNum;

  public String getTicketName() {
    return ticketName;
  }

  public void setTicketName(String ticketName) {
    this.ticketName = ticketName;
  }

  public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTicketNumber() {
        return ticketNumber;
    }

    public void setTicketNumber(String ticketNumber) {
        this.ticketNumber = ticketNumber == null ? null : ticketNumber.trim();
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode == null ? null : goodCode.trim();
    }

    public String getValidityPeriod() {
        return validityPeriod;
    }

    public void setValidityPeriod(String validityPeriod) {
        this.validityPeriod = validityPeriod == null ? null : validityPeriod.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getOperateMan() {
        return operateMan;
    }

    public void setOperateMan(String operateMan) {
        this.operateMan = operateMan == null ? null : operateMan.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getRoomNo() {
    return roomNo;
  }

  public void setRoomNo(String roomNo) {
    this.roomNo = roomNo;
  }

  public Integer getStaffId() {
    return staffId;
  }

  public void setStaffId(Integer staffId) {
    this.staffId = staffId;
  }

  public String getCustomerName() {
    return customerName;
  }

  public void setCustomerName(String customerName) {
    this.customerName = customerName;
  }

  public String getCrsNo() {
    return crsNo;
  }

  public void setCrsNo(String crsNo) {
    this.crsNo = crsNo;
  }

  public int getIfVoucherNum() {
    return ifVoucherNum;
  }

  public void setIfVoucherNum(int ifVoucherNum) {
    this.ifVoucherNum = ifVoucherNum;
  }
}