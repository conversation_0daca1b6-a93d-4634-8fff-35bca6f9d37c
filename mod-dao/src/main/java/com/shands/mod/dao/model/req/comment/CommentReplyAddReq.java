package com.shands.mod.dao.model.req.comment;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class CommentReplyAddReq {

  @ApiModelProperty(value = "评论id")
  private String commentId;

  @ApiModelProperty(value = "是否置顶（1是，0否）")
  private Integer topSign;

  @ApiModelProperty(value = "回复内容")
  private String content;
}
