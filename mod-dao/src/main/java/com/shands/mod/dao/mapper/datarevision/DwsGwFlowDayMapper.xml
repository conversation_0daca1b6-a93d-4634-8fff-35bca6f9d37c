<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.datarevision.DwsGwFlowDayMapper">

    <resultMap type="com.shands.mod.dao.model.datarevision.po.DwsGwFlowDay" id="DwsGwFlowDayMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="bizDate" column="biz_date" jdbcType="TIMESTAMP"/>
        <result property="appDownload" column="app_download" jdbcType="INTEGER"/>
        <result property="appAccess" column="app_access" jdbcType="INTEGER"/>
        <result property="appRegister" column="app_register" jdbcType="INTEGER"/>
        <result property="bdwRoomNight" column="bdw_room_night" jdbcType="INTEGER"/>
        <result property="allRoomNight" column="all_room_night" jdbcType="INTEGER"/>
        <result property="sqRoomNight" column="sq_room_night" jdbcType="INTEGER"/>
        <result property="bdwAppOrder" column="bdw_app_order" jdbcType="INTEGER"/>
        <result property="bdwMiniOrder" column="bdw_mini_order" jdbcType="INTEGER"/>
        <result property="xcRoomNight" column="xc_room_night" jdbcType="INTEGER"/>
        <result property="mtRoomNight" column="mt_room_night" jdbcType="INTEGER"/>
        <result property="visitRoomNight" column="visit_room_night" jdbcType="INTEGER"/>
        <result property="saleDeptRoomNight" column="sale_dept_room_night" jdbcType="INTEGER"/>
        <result property="bookRoomNight" column="book_room_night" jdbcType="INTEGER"/>
        <result property="otherRoomNight" column="other_room_night" jdbcType="INTEGER"/>
        <result property="websiteRoomNight" column="website_room_night" jdbcType="INTEGER"/>
        <result property="websiteRevenue" column="website_revenue" jdbcType="INTEGER"/>
        <result property="newlyAppRegister" column="newly_app_register" jdbcType="INTEGER"/>
        <result property="newlyAppRegisterRate" column="newly_app_register_rate" jdbcType="VARCHAR"/>
        <result property="totalRoomNight" column="total_room_night" jdbcType="VARCHAR"/>
        <result property="roomNightsPmsN" column="room_nights_pms_n" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="DwsGwFlowDayMap">
        select
          id, biz_date, app_download, app_access, app_register, bdw_room_night, all_room_night,sq_room_night, bdw_app_order, bdw_mini_order
          ,xc_room_night,mt_room_night,visit_room_night,sale_dept_room_night,book_room_night,other_room_night,website_room_night,website_revenue,newly_app_register,newly_app_register_rate
        from dws_gw_flow_day
        where id = #{id}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from dws_gw_flow_day
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="bizDate != null">
                and biz_date = #{bizDate}
            </if>
            <if test="appDownload != null">
                and app_download = #{appDownload}
            </if>
            <if test="appAccess != null">
                and app_access = #{appAccess}
            </if>
            <if test="appRegister != null">
                and app_register = #{appRegister}
            </if>
            <if test="bdwRoomNight != null">
                and bdw_room_night = #{bdwRoomNight}
            </if>
            <if test="sqRoomNight != null">
                and sq_room_night = #{sqRoomNight}
            </if>
          <if test="allRoomNight != null">
            and all_room_night = #{allRoomNight}
          </if>
            <if test="bdwAppOrder != null">
                and bdw_app_order = #{bdwAppOrder}
            </if>
            <if test="bdwMiniOrder != null">
                and bdw_mini_order = #{bdwMiniOrder}
            </if>
            <if test="xcRoomNight != null">
              and xc_room_night = #{xcRoomNight}
            </if>
            <if test="mtRoomNight != null">
              and mt_room_night = #{mtRoomNight}
            </if>
            <if test="visitRoomNight != null">
              and visit_room_night = #{visitRoomNight}
            </if>
            <if test="saleDeptRoomNight != null">
              and sale_dept_room_night = #{saleDeptRoomNight}
            </if>
            <if test="bookRoomNight != null">
              and book_room_night = #{bookRoomNight}
            </if>
            <if test="otherRoomNight != null">
              and other_room_night = #{otherRoomNight}
            </if>
            <if test="websiteRoomNight != null">
              and website_room_night = #{websiteRoomNight}
            </if>
            <if test="websiteRevenue != null">
              and website_revenue = #{websiteRevenue}
            </if>
            <if test="newlyAppRegister != null">
              and newly_app_register = #{newlyAppRegister}
            </if>
            <if test="newlyAppRegisterRate != null">
              and newly_app_register_rate = #{newlyAppRegisterRate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into dws_gw_flow_day(biz_date, app_download, app_access, app_register, bdw_room_night, sq_room_night, bdw_app_order, bdw_mini_order, all_room_night, xc_room_night, mt_room_night, visit_room_night, sale_dept_room_night, book_room_night, other_room_night,website_room_night,website_revenue,newly_app_register,newly_app_register_rate)
        values (#{bizDate}, #{appDownload}, #{appAccess}, #{appRegister}, #{bdwRoomNight}, #{sqRoomNight}, #{bdwAppOrder}, #{bdwMiniOrder}, #{allRoomNight}, #{xcRoomNight}, #{mtRoomNight}, #{visitRoomNight}, #{saleDeptRoomNight}, #{bookRoomNight}, #{otherRoomNight}, #{websiteRoomNight}, #{websiteRevenue},#{newlyAppRegister},#{newlyAppRegisterRate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
      insert into dws_gw_flow_day(biz_date, app_download, app_access, app_register, bdw_room_night, sq_room_night, bdw_app_order, bdw_mini_order, all_room_night, xc_room_night, mt_room_night, visit_room_night, sale_dept_room_night, book_room_night, other_room_night,website_room_night,website_revenue,newly_app_register,newly_app_register_rate)
      values
      <foreach collection="entities" item="entity" separator=",">
        (#{entity.bizDate}, #{entity.appDownload}, #{entity.appAccess}, #{entity.appRegister}, #{entity.bdwRoomNight}, #{entity.sqRoomNight}, #{entity.bdwAppOrder}, #{entity.bdwMiniOrder}, #{entity.allRoomNight}, #{entity.xcRoomNight}, #{entity.mtRoomNight}, #{entity.visitRoomNight}, #{entity.saleDeptRoomNight}, #{entity.bookRoomNight}, #{entity.otherRoomNight}, #{entity.websiteRoomNight}, #{entity.websiteRevenue},#{entity.newlyAppRegister},#{entity.newlyAppRegisterRate})
      </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
      insert into dws_gw_flow_day(biz_date, app_download, app_access, app_register, bdw_room_night, sq_room_night, bdw_app_order, bdw_mini_order, all_room_night, xc_room_night, mt_room_night, visit_room_night, sale_dept_room_night, book_room_night, other_room_night,website_room_night,website_revenue,newly_app_register,newly_app_register_rate)
      values
      <foreach collection="entities" item="entity" separator=",">
        (#{entity.bizDate}, #{entity.appDownload}, #{entity.appAccess}, #{entity.appRegister}, #{entity.bdwRoomNight}, #{entity.sqRoomNight}, #{entity.bdwAppOrder}, #{entity.bdwMiniOrder}, #{entity.allRoomNight}, #{entity.xcRoomNight}, #{entity.mtRoomNight}, #{entity.visitRoomNight}, #{entity.saleDeptRoomNight}, #{entity.bookRoomNight}, #{entity.otherRoomNight}, #{entity.websiteRoomNight}, #{entity.websiteRevenue},#{entity.newlyAppRegister},#{entity.newlyAppRegisterRate})
      </foreach>
      on duplicate key update
      biz_date = values(biz_date),
      app_download = values(app_download),
      app_access = values(app_access),
      app_register = values(app_register),
      bdw_room_night = values(bdw_room_night),
      sq_room_night = values(sq_room_night),
      bdw_app_order = values(bdw_app_order),
      bdw_mini_order = values(bdw_mini_order),
      all_room_night = values(all_room_night),
      xc_room_night = values(xc_room_night),
      mt_room_night = values(mt_room_night),
      visit_room_night = values(visit_room_night),
      sale_dept_room_night = values(sale_dept_room_night),
      book_room_night = values(book_room_night),
      other_room_night = values(other_room_night)
      website_room_night = values(websiteRoomNight),
      website_revenue = values(websiteRevenue)
      newly_app_register = values(newlyAppRegister),
      newly_app_register_rate = values(newlyAppRegisterRate)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
      update dws_gw_flow_day
      <set>
        <if test="bizDate != null">
          biz_date = #{bizDate},
        </if>
        <if test="appDownload != null">
          app_download = #{appDownload},
        </if>
        <if test="appAccess != null">
          app_access = #{appAccess},
        </if>
        <if test="appRegister != null">
          app_register = #{appRegister},
        </if>
        <if test="bdwRoomNight != null">
          bdw_room_night = #{bdwRoomNight},
        </if>
        <if test="sqRoomNight != null">
          sq_room_night = #{sqRoomNight},
        </if>
        <if test="bdwAppOrder != null">
          bdw_app_order = #{bdwAppOrder},
        </if>
        <if test="bdwMiniOrder != null">
          bdw_mini_order = #{bdwMiniOrder},
        </if>
        <if test="allRoomNight != null">
          all_room_night = #{allRoomNight},
        </if>
        <if test="xcRoomNight != null">
          xc_room_night = #{xcRoomNight},
        </if>
        <if test="mtRoomNight != null">
          mt_room_night = #{mtRoomNight},
        </if>
        <if test="visitRoomNight != null">
          visit_room_night = #{visitRoomNight},
        </if>
        <if test="saleDeptRoomNight != null">
          sale_dept_room_night = #{saleDeptRoomNight},
        </if>
        <if test="bookRoomNight != null">
          book_room_night = #{bookRoomNight},
        </if>
        <if test="otherRoomNight != null">
          other_room_night = #{otherRoomNight},
        </if>
        <if test="websiteRoomNight != null">
          website_room_night = #{websiteRoomNight},
        </if>
        <if test="websiteRevenue != null">
          website_revenue = #{websiteRevenue},
        </if>
        <if test="newlyAppRegister != null">
          and newly_app_register = #{newlyAppRegister}
        </if>
        <if test="newlyAppRegisterRate != null">
          and newly_app_register_rate = #{newlyAppRegisterRate}
        </if>
      </set>
      where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from dws_gw_flow_day where id = #{id}
    </delete>

  <select id="findMaxDate" resultType="java.util.Date">
    SELECT MAX(biz_date) FROM dws_gw_flow_day
  </select>

  <select id="selectAppInstalle" resultType="com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo">
    SELECT  a.detailValue, '百达屋APP下载量' AS detailName,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanYesterday)/a.thanYesterday * 100,DECIMAL(12,2)),0),'%') as  thanYesterday,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanLastMonth)/a.thanLastMonth * 100,DECIMAL(12,2)),0),'%') as  thanLastMonth
    FROM
    (SELECT app_download AS 'detailValue',
    (SELECT app_download  FROM dws_gw_flow_day WHERE biz_date = #{yesterday}) as 'thanYesterday',
    (SELECT app_download  FROM dws_gw_flow_day WHERE biz_date = #{lastMonthDay}) as 'thanLastMonth'
    FROM dws_gw_flow_day WHERE biz_date = #{bizDate})a
  </select>

  <select id="selectAppAccess" resultType="com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo">
    SELECT  a.detailValue,'百达屋APP访问量' AS detailName,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanYesterday)/a.thanYesterday * 100,DECIMAL(12,2)),0),'%') as  thanYesterday,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanLastMonth)/a.thanLastMonth * 100,DECIMAL(12,2)),0),'%') as  thanLastMonth
    FROM
    (SELECT app_access AS 'detailValue',
    (SELECT app_access  FROM dws_gw_flow_day WHERE biz_date = #{yesterday}) as 'thanYesterday',
    (SELECT app_access  FROM dws_gw_flow_day WHERE biz_date = #{lastMonthDay}) as 'thanLastMonth'
    FROM dws_gw_flow_day WHERE biz_date = #{bizDate})a
  </select>

  <select id="selectBdwRoomNight" resultType="com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo">
    SELECT  a.detailValue,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanYesterday)/a.thanYesterday * 100,DECIMAL(12,2)),0),'%') as  thanYesterday,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanLastMonth)/a.thanLastMonth * 100,DECIMAL(12,2)),0),'%') as  thanLastMonth
    FROM
    (SELECT IFNULL(CONVERT((bdw_room_night/room_nights_pms_n * 100),DECIMAL(12,2)),0) AS 'detailValue',
    (SELECT IFNULL(CONVERT((bdw_room_night/room_nights_pms_n * 100),DECIMAL(12,2)),0)
    FROM dws_gw_flow_day WHERE biz_date = #{yesterday}) as 'thanYesterday',
    (SELECT IFNULL(CONVERT((bdw_room_night/room_nights_pms_n * 100),DECIMAL(12,2)),0)  FROM dws_gw_flow_day WHERE biz_date = #{lastMonthDay}) as 'thanLastMonth'
    FROM dws_gw_flow_day WHERE biz_date = #{bizDate})a
  </select>

  <select id="selectSqRoomNight" resultType="com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo">
    SELECT  a.detailValue,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanYesterday)/a.thanYesterday * 100,DECIMAL(12,2)),0),'%') as  thanYesterday,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanLastMonth)/a.thanLastMonth * 100,DECIMAL(12,2)),0),'%') as  thanLastMonth
    FROM
    (SELECT IFNULL(CONVERT((sq_room_night/room_nights_pms_n * 100),DECIMAL(12,2)),0) AS 'detailValue',
    (SELECT IFNULL(CONVERT((sq_room_night/room_nights_pms_n * 100),DECIMAL(12,2)),0)  FROM dws_gw_flow_day WHERE biz_date = #{yesterday}) as 'thanYesterday',
    (SELECT IFNULL(CONVERT((sq_room_night/room_nights_pms_n * 100),DECIMAL(12,2)),0)  FROM dws_gw_flow_day WHERE biz_date = #{lastMonthDay}) as 'thanLastMonth'
    FROM dws_gw_flow_day WHERE biz_date = #{bizDate})a
  </select>
    <select id="selectTrafficDistributionOfBdw" resultType="com.shands.mod.dao.model.datarevision.vo.TrafficVo">
      SELECT
        h.time time,
        h.number number,
        h.pro trafficType
      FROM
        (
          SELECT
            a.biz_date time,
            a.app_download number,
            'APP下载量' AS pro
          FROM
            dws_gw_flow_day a
          GROUP BY
            a.biz_date UNION ALL
          SELECT
            b.biz_date time,
            b.app_access number,
            'APP访问用户' AS pro
          FROM
            dws_gw_flow_day b
          GROUP BY
            b.biz_date UNION ALL
          SELECT
            c.biz_date time,
            c.app_register number,
            '新注册会员APP下载量' AS pro
          FROM
            dws_gw_flow_day c
          GROUP BY
            c.biz_date
        ) h
        <where>
          <if test="memberAnalysisBo.startTime != null and memberAnalysisBo.startTime != ''">
            AND h.time &gt;= #{memberAnalysisBo.startTime}
          </if>
          <if test="memberAnalysisBo.endTime != null and memberAnalysisBo.endTime != ''">
            AND h.time &lt;= #{memberAnalysisBo.endTime}
          </if>
        </where>
    </select>
  <select id="selectNightTrafficDistribution" resultType="com.shands.mod.dao.model.datarevision.vo.NightTrafficVo">
    SELECT
      h.time time,
      h.number number,
      h.pro nightTrafficType
    FROM
      (
        SELECT
        c.biz_date time,
        c.sq_room_night+c.bdw_room_night+c.xc_room_night+c.mt_room_night+c.visit_room_night+c.sale_dept_room_night+c.book_room_night+c.other_room_night number,
        '合计' AS pro
        FROM
        dws_gw_flow_day c
        GROUP BY
        c.biz_date UNION ALL
        SELECT
          a.biz_date time,
          a.bdw_room_night number,
          '百达屋' AS pro
        FROM
          dws_gw_flow_day a
        GROUP BY
          a.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.sq_room_night number,
          '商祺会' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date UNION ALL
        SELECT
        d.biz_date time,
        d.sq_room_night+d.bdw_room_night number,
        '官渠合计' AS pro
        FROM
        dws_gw_flow_day d
        GROUP BY
        d.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.xc_room_night number,
          '携程' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.mt_room_night number,
          '美团' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.visit_room_night number,
          '上门散客' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.sale_dept_room_night number,
          '酒店销售部散客' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.book_room_night number,
          '个人直接订房' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date UNION ALL
        SELECT
          b.biz_date time,
          b.other_room_night number,
          '其他' AS pro
        FROM
          dws_gw_flow_day b
        GROUP BY
          b.biz_date
      ) h
    <where>
      <if test="memberAnalysisBo.startTime != null and memberAnalysisBo.startTime != ''">
        AND h.time &gt;= #{memberAnalysisBo.startTime}
      </if>
      <if test="memberAnalysisBo.endTime != null and memberAnalysisBo.endTime != ''">
        AND h.time &lt;= #{memberAnalysisBo.endTime}
      </if>
    </where>
  </select>
  <select id="selectNightTrafficProportion" resultType="com.shands.mod.dao.model.datarevision.vo.NightTrafficVo">
    SELECT
      h.time time,
      h.number number,
      h.pro nightTrafficType
    FROM
      (
      SELECT
      c.biz_date time,
      ROUND((c.sq_room_night+c.bdw_room_night)*100/c.room_nights_pms_n,2) number,
      '官渠合计' AS pro
      FROM
      dws_gw_flow_day c
      GROUP BY
      c.biz_date UNION ALL
      SELECT
        a.biz_date time,
        ROUND(a.bdw_room_night*100/a.room_nights_pms_n,2) number,
        '百达屋' AS pro
      FROM
        dws_gw_flow_day a
      GROUP BY
        a.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.sq_room_night*100/b.room_nights_pms_n,2) number,
        '商祺会' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.xc_room_night*100/b.room_nights_pms_n,2) number,
        '携程' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.mt_room_night*100/b.room_nights_pms_n,2) number,
        '美团' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.visit_room_night*100/b.room_nights_pms_n,2) number,
        '上门散客' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.sale_dept_room_night*100/b.room_nights_pms_n,2) number,
        '酒店销售部散客' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.book_room_night*100/b.room_nights_pms_n,2) number,
        '个人直接订房' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date UNION ALL
      SELECT
        b.biz_date time,
        ROUND(b.other_room_night*100/b.room_nights_pms_n,2) number,
        '其他' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date
      ) h
    <where>
      <if test="memberAnalysisBo.startTime != null and memberAnalysisBo.startTime != ''">
        AND h.time &gt;= #{memberAnalysisBo.startTime}
      </if>
      <if test="memberAnalysisBo.endTime != null and memberAnalysisBo.endTime != ''">
        AND h.time &lt;= #{memberAnalysisBo.endTime}
      </if>
    </where>
  </select>
  <select id="selectBdwOrderSourceTrend" resultType="com.shands.mod.dao.model.datarevision.vo.BdwOrderVo">
    SELECT
      h.time time,
      h.number number,
      h.pro orderType
    FROM
      (
      SELECT
        a.biz_date time,
        a.bdw_app_order number,
        '百达屋APP' AS pro
      FROM
        dws_gw_flow_day a
      GROUP BY
        a.biz_date UNION ALL
      SELECT
        b.biz_date time,
        b.bdw_mini_order number,
        '百达屋小程序' AS pro
      FROM
        dws_gw_flow_day b
      GROUP BY
        b.biz_date
      ) h
    <where>
      <if test="memberAnalysisBo.startTime != null and memberAnalysisBo.startTime != ''">
        AND h.time &gt;= #{memberAnalysisBo.startTime}
      </if>
      <if test="memberAnalysisBo.endTime != null and memberAnalysisBo.endTime != ''">
        AND h.time &lt;= #{memberAnalysisBo.endTime}
      </if>
    </where>
  </select>
  <select id="selectBdwOrderSourceProportion" resultType="com.shands.mod.dao.model.datarevision.vo.GeneralDrawingVo">
    SELECT
      biz_date time,
      ROUND(
        bdw_app_order*100 / (
          bdw_app_order + bdw_mini_order
        ),
        2
      ) number
    FROM
      dws_gw_flow_day
    <where>
      <if test="memberAnalysisBo.startTime != null and memberAnalysisBo.startTime != ''">
        AND biz_date &gt;= #{memberAnalysisBo.startTime}
      </if>
      <if test="memberAnalysisBo.endTime != null and memberAnalysisBo.endTime != ''">
        AND biz_date &lt;= #{memberAnalysisBo.endTime}
      </if>
    </where>
    GROUP BY biz_date
  </select>
  <select id="selectNewlyMemberDownloads"
          resultType="com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo">
    SELECT  a.detailValue,'新会员APP下载量' AS detailName,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanYesterday)/a.thanYesterday * 100,DECIMAL(12,2)),0),'%') as  thanYesterday,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanLastMonth)/a.thanLastMonth * 100,DECIMAL(12,2)),0),'%') as  thanLastMonth
    FROM
    (SELECT newly_app_register AS 'detailValue',
    (SELECT newly_app_register  FROM dws_gw_flow_day WHERE biz_date = #{yesterday}) as 'thanYesterday',
    (SELECT newly_app_register  FROM dws_gw_flow_day WHERE biz_date = #{lastMonthDay}) as 'thanLastMonth'
    FROM dws_gw_flow_day WHERE biz_date = #{maxDate})a
  </select>
  <select id="selectNewlyMemberDownloadsRate"
          resultType="com.shands.mod.dao.model.datarevision.vo.HomeDataDetailVo">
    SELECT  a.detailValue,'新会员APP下载比例' AS detailName,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanYesterday)/a.thanYesterday * 100,DECIMAL(12,2)),0),'%') as  thanYesterday,
    CONCAT(IFNULL(CONVERT((a.detailValue - a.thanLastMonth)/a.thanLastMonth * 100,DECIMAL(12,2)),0),'%') as  thanLastMonth
    FROM
    (SELECT newly_app_register_rate AS 'detailValue',
    (SELECT newly_app_register_rate  FROM dws_gw_flow_day WHERE biz_date = #{yesterday}) as 'thanYesterday',
    (SELECT newly_app_register_rate  FROM dws_gw_flow_day WHERE biz_date = #{lastMonthDay}) as 'thanLastMonth'
    FROM dws_gw_flow_day WHERE biz_date = #{maxDate})a
  </select>
</mapper>
