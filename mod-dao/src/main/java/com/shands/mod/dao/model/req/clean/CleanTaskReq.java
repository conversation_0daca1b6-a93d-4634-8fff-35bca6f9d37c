package com.shands.mod.dao.model.req.clean;

import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-09
 * @description 做房记录查询接收
 */
@Data
public class CleanTaskReq {

  /**
   * 酒店id
   */
  private Integer companyId;
  /**
   * 清扫人员姓名
   */
  private String cleanUserName;
  /**
   * 开始时间
   */
  private Date startTime;
  /**
   * 结束时间
   */
  private Date endTime;
  /**
   * 任务状态
   */
  private Integer cleanStatus;

  /**
   * 清扫人员id
   */
  private Integer cleanUser;

  /**
   * 月份（yyyy-mm）
   */
  private String month;

  /**
   * 日期（yyyy-mm-dd）
   */
  private String day;
  /**
   * 房间号
   */
  private String roomNo;
}
