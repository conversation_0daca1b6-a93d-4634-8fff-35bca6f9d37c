package com.shands.mod.dao.model.enums;

/**
 * 优惠政策  税率分类
 */
public enum LslbsEnum {
  export_rebates(0, "出口退税"),

  tax_exemption(1, "免税"),

  dont_impose(2, "不征收"),

  zero_rate(3, "普通零税率");

  // 空：非零利率,0：出口退税,1：免税,2：不征收,3普通零税率
  private Integer code;
  private String desc;

  LslbsEnum(Integer code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }
}
