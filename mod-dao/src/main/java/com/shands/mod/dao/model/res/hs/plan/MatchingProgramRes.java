package com.shands.mod.dao.model.res.hs.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("自动匹配项目")
public class MatchingProgramRes {

  @ApiModelProperty("计划卫生id")
  private Integer programId;

  @ApiModelProperty("计划卫生名称")
  private String programName;

  @ApiModelProperty("分类id")
  private Integer classifyId;

  @ApiModelProperty("自动匹配房间信息")
  private List<HsPlanSanitateTaskRes> rooms;
}
