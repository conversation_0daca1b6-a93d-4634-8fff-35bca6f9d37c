package com.shands.mod.dao.model.datarevision.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/5/31 16:17
 */
@Data
@ApiModel("酒店项目地图分布请求参数")
public class HotelProjectMapBo {

  @ApiModelProperty("酒店维度code")
  private String dataDimensionCode;

  @ApiModelProperty("值code")
  private String valueCode;

  @ApiModelProperty("业务时间")
  private Date bizDate;
}
