package com.shands.mod.dao.mapper.hs.cleanRoom;

import com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomBaseScore;
import com.shands.mod.dao.model.req.clean.BaseRoomTypeScoreReq;
import com.shands.mod.dao.model.res.clean.BaseScoreRes;
import com.shands.mod.dao.model.res.clean.RoomTypeAndScoreRes;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface HsCleanRoomBaseScoreMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(HsCleanRoomBaseScore record);

  int insertSelective(HsCleanRoomBaseScore record);

  HsCleanRoomBaseScore selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HsCleanRoomBaseScore record);

  int updateByPrimaryKey(HsCleanRoomBaseScore record);

  List<HsCleanRoomBaseScore> selectByCompanyId(Integer companyId);

  /**
   * 批量插入
   *
   * @param list 列表
   * @return int
   */
  int insertBatch(@Param("entities") List<BaseRoomTypeScoreReq> list);

  /**
   * 批量更新
   *
   * @param list 列表
   * @return int
   */
  int updateBatch(@Param("entities") List<BaseRoomTypeScoreReq> list);

  /**
   * 通过酒店id查询房型工分
   *
   * @param companyId 酒店id
   * @return {@link List<BaseScoreRes>}
   */
  List<BaseScoreRes> queryByCompanyId(Integer companyId);

  HsCleanRoomBaseScore scoreByType(Integer companyId,String roomType);

  RoomTypeAndScoreRes scoreAndType(Integer companyId,String roomNo);


}