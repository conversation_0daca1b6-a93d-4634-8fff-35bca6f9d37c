<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheck" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="clean_times" property="cleanTimes" jdbcType="INTEGER" />
    <result column="start_clean" property="startClean" jdbcType="TIMESTAMP" />
    <result column="end_clean" property="endClean" jdbcType="TIMESTAMP" />
    <result column="start_check" property="startCheck" jdbcType="TIMESTAMP" />
    <result column="end_check" property="endCheck" jdbcType="TIMESTAMP" />
    <result column="total_time" property="totalTime" jdbcType="VARCHAR" />
    <result column="clean_user" property="cleanUser" jdbcType="INTEGER" />
    <result column="check_user" property="checkUser" jdbcType="INTEGER" />
    <result column="company_id" property="companyId" jdbcType="INTEGER" />
    <result column="deleted" property="deleted" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="check_result" property="checkResult" jdbcType="VARCHAR"/>
  </resultMap>

  <resultMap id="BaseResultMapA" type="com.shands.mod.dao.model.res.clean.TodayCheckRes" >
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="end_clean" property="endClean" jdbcType="TIMESTAMP" />
    <result column="end_check" property="endCheck" jdbcType="TIMESTAMP" />
    <result column="room_no" property="roomNo" jdbcType="VARCHAR" />
    <result column="clean_type" property="cleanType" jdbcType="VARCHAR" />
    <result column="clean_user_name" property="cleanUserName" jdbcType="VARCHAR" />
    <result column="clean_status" property="cleanStatus" jdbcType="INTEGER" />
    <result column="clean_user" property="cleanUser" jdbcType="INTEGER" />
    <result column="check_result" property="checkResult" jdbcType="INTEGER" />
    <result column="not_check" property="notCheck" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="BaseResultMapB" type="com.shands.mod.dao.model.res.clean.TodayCheckUserRes" >
    <result column="check_user_name" property="checkUserName" jdbcType="VARCHAR" />
    <result column="check_user" property="checkUser" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, task_id, clean_times, start_clean, end_clean, start_check, end_check, total_time, 
    clean_user, check_user, company_id, deleted, create_time, update_time,check_result
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from hs_clean_room_check
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from hs_clean_room_check
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheck" >
    insert into hs_clean_room_check (id, task_id, clean_times, 
      start_clean, end_clean, start_check, 
      end_check, total_time, clean_user, 
      check_user, company_id, deleted, 
      create_time, update_time,check_result)
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{cleanTimes,jdbcType=INTEGER}, 
      #{startClean,jdbcType=TIMESTAMP}, #{endClean,jdbcType=TIMESTAMP}, #{startCheck,jdbcType=TIMESTAMP}, 
      #{endCheck,jdbcType=TIMESTAMP}, #{totalTime,jdbcType=VARCHAR}, #{cleanUser,jdbcType=INTEGER}, 
      #{checkUser,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{checkResult,jdbcType=VARCHAR}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         })
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheck" >
    insert into hs_clean_room_check
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="cleanTimes != null" >
        clean_times,
      </if>
      <if test="startClean != null" >
        start_clean,
      </if>
      <if test="endClean != null" >
        end_clean,
      </if>
      <if test="startCheck != null" >
        start_check,
      </if>
      <if test="endCheck != null" >
        end_check,
      </if>
      <if test="totalTime != null" >
        total_time,
      </if>
      <if test="cleanUser != null" >
        clean_user,
      </if>
      <if test="checkUser != null" >
        check_user,
      </if>
      <if test="companyId != null" >
        company_id,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="checkResult != null">
        check_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="cleanTimes != null" >
        #{cleanTimes,jdbcType=INTEGER},
      </if>
      <if test="startClean != null" >
        #{startClean,jdbcType=TIMESTAMP},
      </if>
      <if test="endClean != null" >
        #{endClean,jdbcType=TIMESTAMP},
      </if>
      <if test="startCheck != null" >
        #{startCheck,jdbcType=TIMESTAMP},
      </if>
      <if test="endCheck != null" >
        #{endCheck,jdbcType=TIMESTAMP},
      </if>
      <if test="totalTime != null" >
        #{totalTime,jdbcType=VARCHAR},
      </if>
      <if test="cleanUser != null" >
        #{cleanUser,jdbcType=INTEGER},
      </if>
      <if test="checkUser != null" >
        #{checkUser,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheck" >
    update hs_clean_room_check
    <set >
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="cleanTimes != null" >
        clean_times = #{cleanTimes,jdbcType=INTEGER},
      </if>
      <if test="startClean != null" >
        start_clean = #{startClean,jdbcType=TIMESTAMP},
      </if>
      <if test="endClean != null" >
        end_clean = #{endClean,jdbcType=TIMESTAMP},
      </if>
      <if test="startCheck != null" >
        start_check = #{startCheck,jdbcType=TIMESTAMP},
      </if>
      <if test="endCheck != null" >
        end_check = #{endCheck,jdbcType=TIMESTAMP},
      </if>
      <if test="totalTime != null" >
        total_time = #{totalTime,jdbcType=VARCHAR},
      </if>
      <if test="cleanUser != null" >
        clean_user = #{cleanUser,jdbcType=INTEGER},
      </if>
      <if test="checkUser != null" >
        check_user = #{checkUser,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkResult != null">
        check_result = #{checkResult,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheck" >
    update hs_clean_room_check
    set task_id = #{taskId,jdbcType=INTEGER},
      clean_times = #{cleanTimes,jdbcType=INTEGER},
      start_clean = #{startClean,jdbcType=TIMESTAMP},
      end_clean = #{endClean,jdbcType=TIMESTAMP},
      start_check = #{startCheck,jdbcType=TIMESTAMP},
      end_check = #{endCheck,jdbcType=TIMESTAMP},
      total_time = #{totalTime,jdbcType=VARCHAR},
      clean_user = #{cleanUser,jdbcType=INTEGER},
      check_user = #{checkUser,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      check_result = #{checkResult,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--根据任务id获得检查信息-->
  <select id="checkByTask" resultType="com.shands.mod.dao.model.res.clean.CheckByTaskRes">
  SELECT
	a.id checkId,
	a.clean_times cleanTimes,
	a.start_clean startClean,
	a.end_clean endClean,
	a.total_time totalTime,
	a.start_check startCheck,
	a.end_check endCheck,
	b.name checkUser,
	a.check_result checkResult
FROM
	hs_clean_room_check a left join mod_user b on a.check_user = b.id
WHERE
	a.task_id = #{taskId}
	and a.deleted =0
	order by clean_times
  </select>
  <select id="queryWaitingCheck" resultMap="BaseResultMapA">
    select t.task_id,t.end_clean,t.end_check,s.room_no,s.clean_type,s.clean_status,t.clean_user,s.clean_user_name
    from hs_clean_room_task s left join hs_clean_room_check t on t.task_id = s.id
    where t.end_clean is not null
      and t.end_check is null
      and s.clean_status in(3,6)
      and t.company_id=#{companyId}
      and s.check_user=#{checkUser}
      and DATE_FORMAT(t.end_clean,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d')
    order by s.room_no asc, t.create_time asc
  </select>

  <select id="queryWaitingCheckByUserIds" resultMap="BaseResultMapA">
    select t.task_id,t.end_clean,t.end_check,s.room_no,s.clean_type,s.clean_status,t.clean_user,s.clean_user_name
    from hs_clean_room_task s left join hs_clean_room_check t on t.task_id = s.id
    where t.end_clean is not null
      and t.end_check is null
      and s.clean_status in(3,6)
      and t.company_id=#{companyId}
      <if test="checkUser != null and checkUser.size > 0">
        AND s.check_user in
        <foreach item="item" index="index" collection="checkUser" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and DATE_FORMAT(t.end_clean,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d')
    order by s.room_no asc, t.create_time asc
  </select>

  <select id="queryWaitingCheckUser" resultMap="BaseResultMapB">
    select s.check_user,s.check_user_name
    from hs_clean_room_task s left join hs_clean_room_check t on t.task_id = s.id
    where t.end_clean is not null
      and t.end_check is null
      and s.clean_status in(3,6)
      and t.company_id=#{companyId}
      and DATE_FORMAT(t.end_clean,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d')
   group by s.check_user
  </select>


  <select id="queryCompletedCheck" resultMap="BaseResultMapA">
    SELECT
      s.*,
      s1.room_no,
      s1.clean_type,
      s1.clean_status,
      s1.clean_user_name,
      s1.not_check
    FROM
      (
        SELECT
          t.task_id,
          t.end_clean,
          t.end_check,
          t.clean_user,
          t.check_result
        FROM hs_clean_room_check t
               INNER JOIN (SELECT t1.task_id, max(t1.clean_times) clean_times
                           FROM hs_clean_room_check t1
                           where t1.company_id = #{companyId}
                             AND t1.check_user = #{checkUserId}
                           GROUP BY t1.task_id) b ON b.task_id = t.task_id
          AND b.clean_times = t.clean_times
        WHERE
          DATE_FORMAT( t.end_check, '%Y-%m-%d' ) = DATE_FORMAT( now(), '%Y-%m-%d' )
          AND t.check_user = #{checkUserId}
          AND t.company_id = #{companyId}
      ) s
        LEFT JOIN hs_clean_room_task s1 ON s.task_id = s1.id
    WHERE
      s1.clean_status IN ( 1, 4 )
    ORDER BY
      s1.room_no ASC,
      s1.update_time ASC

  </select>

  <!--查找最大的清扫次数-->
  <select id="maxTimes" resultType="java.lang.Integer">
    select max(clean_times) from hs_clean_room_check where task_id = #{taskId}
  </select>

  <select id="cleanStart" resultType="com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomCheck">
    select
    id id,
    start_clean startClean
    from  hs_clean_room_check
    WHERE
	  task_id = #{taskId}
	  AND (ISNULL( end_clean ) or end_clean ='')
	  and deleted = 0
	  order by id desc
    limit 1
  </select>

  <!--查询出最后清扫完成的，且未检查-->
  <select id="unchecked" resultType="java.lang.Integer">
SELECT
	id
FROM
	hs_clean_room_check
WHERE
	task_id = #{taskId}
	and end_clean is not null
	AND (ISNULL( start_check ) or start_check ='')
	AND (isnull( end_check )or start_check ='')
  </select>

  <!--检查中 查询最新的id-->
  <select id="checking" resultType="java.lang.Integer">
SELECT
	id
FROM
	hs_clean_room_check
WHERE
	task_id = #{taskId}
	order by clean_times desc
	limit 0,1
  </select>

  <!--查询出 用来判断是否展示全部检查项-->
  <select id="show" resultType="java.lang.String">
SELECT
IF
	( max( clean_times ) > 1, 'true', 'false' )
FROM
	hs_clean_room_check
	where task_id = #{taskId}
  </select>

  <!--查询任务总耗时-->
  <select id="totalTime" resultType="java.lang.Integer">
    	select sum(total_time) totalTime from hs_clean_room_check where task_id = #{taskId} and deleted = 0
  </select>

  <!--查询最后一次清扫完成id-->
  <select id="getId" resultType="java.lang.Integer">
   SELECT id FROM hs_clean_room_check
   WHERE !ISNULL( end_clean ) AND task_id = #{taskId} and deleted = 0 ORDER BY clean_times DESC LIMIT 0,1
  </select>

  <!--根据任务id得到清扫检查详细情况-->
  <select id="checkByTaskId" resultType="com.shands.mod.dao.model.res.clean.CheckByTaskIdRes">
    SELECT
  CONCAT_WS( ' 至 ', DATE_FORMAT( start_clean, '%H:%i:%S' ), DATE_FORMAT( end_clean, '%H:%i:%S' ) ) cleanTime,
	CONCAT_WS( ' 至 ',  DATE_FORMAT( start_check, '%H:%i:%S' ),  DATE_FORMAT( end_check, '%H:%i:%S' ) ) checkTime,
	check_result checkResult
FROM
	hs_clean_room_check
WHERE
	task_id = #{taskId}
	AND company_id = #{companyId}
	AND deleted = 0
  </select>
  <select id="queryCleanTime" resultType="com.shands.mod.dao.model.res.clean.CleanRecordTimesRes">
    SELECT
      task_id taskId,
      GROUP_CONCAT(CONCAT_WS( '-', DATE_FORMAT(start_clean, '%H:%i'), DATE_FORMAT(end_clean,'%H:%i'))) cleanTime,
      SUM(total_time) totalTime
    FROM
      hs_clean_room_check
    <where>
      deleted = 0 and end_clean is not null
      <if test="companyId != null">
        and company_id = #{companyId}
      </if>
      <if test="day != null">
        and DATE_FORMAT(create_time, '%Y-%m-%d') >= DATE_FORMAT(#{day}, '%Y-%m-%d')
      </if>

    </where>
    GROUP BY task_id

  </select>
</mapper>