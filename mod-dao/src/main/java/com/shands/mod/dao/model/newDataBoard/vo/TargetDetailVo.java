package com.shands.mod.dao.model.newDataBoard.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class TargetDetailVo {
  @ApiModelProperty("总经理id")
  private List<TargetDetailListVo> targetDetailListVoList;

  @ApiModelProperty("事业部第一名酒店名称")
  private String deptFirstHotelName;

  @ApiModelProperty("事业部第一名得分")
  private String deptFirstScore;

  @ApiModelProperty("前一名酒店名称")
  private String frontHotelName;

  @ApiModelProperty("前一名得分")
  private String frontHotelScore;

  @ApiModelProperty("后一名酒店名称")
  private String afterHotelName;

  @ApiModelProperty("后一名得分")
  private String afterHotelScore;

  @ApiModelProperty("本酒店排行")
  private Integer rank;

  @ApiModelProperty("本酒店得分")
  private String score;
}
