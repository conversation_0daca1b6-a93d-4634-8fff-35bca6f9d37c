package com.shands.mod.dao.model.req;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 支付req
 */
@Data
public class UnorderChannelReq {

  /** 应用调用方渠道标识 */
  @NotBlank(message = "渠道标识不能为空")
  private String apply_channel;

  /** 应用调用方随机字符串 */
  @NotBlank(message = "随机字符串不能为空")
  private String apply_nonce_str;

  /** 应用调用方商户订单号 */
  @NotBlank(message = "商户订单号不能为空")
  private String apply_trade_no;

  /** 商品简单描述 具体参考 https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=4_2*/
  @NotBlank(message = "商品简单描述不能为空")
  private String body;

  /** 商品详情 非必填*/
  private String detail;

  /** 附加数据 非必填 在查询API和支付通知中原样返回，可作为自定义参数使用 */
  private String attach;

  /** 订单总金额 单位：分 */
  @Min(value = 1,message = "订单金额不能为空且大于0")
  private Long total_fee;

  /** 交易类型 JSAPI -JSAPI支付 NATIVE -Native支付 APP -APP支付 */
  @NotBlank(message = "交易类型不能为空")
  private String trade_type;

  /** 商品ID 非必填 trade_type=NATIVE时，此参数必传。此参数为二维码中包含的商品ID，商户自行定义 */
  private String product_id;

  /** 指定支付方式 非必填 no_credit--指定不能使用信用卡支付 */
  private String limit_pay;

  /** 用户标识 非必填 trade_type=JSAPI时（即JSAPI支付），此参数必传，此参数为微信用户在商户对应appid下的唯一标识 */
  private String openid;
}
