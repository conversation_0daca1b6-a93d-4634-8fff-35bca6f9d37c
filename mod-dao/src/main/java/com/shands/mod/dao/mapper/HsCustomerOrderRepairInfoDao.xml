<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.HsCustomerOrderRepairInfoDao">

    <resultMap type="com.shands.mod.dao.model.v0701.pojo.HsCustomerOrderRepairInfo" id="HsCustomerOrderRepairInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="customerOrderId" column="customer_order_id" jdbcType="INTEGER"/>
        <result property="serviceContent" column="service_content" jdbcType="VARCHAR"/>
        <result property="appointmentTime" column="appointment_time" jdbcType="TIMESTAMP"/>
        <result property="servicePic" column="service_pic" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="HsCustomerOrderRepairInfoMap">
        select
          id, customer_order_id, service_content, appointment_time, service_pic, version, deleted, create_user, create_time, update_user, update_time, remark
        from hs_customer_order_repair_info
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="HsCustomerOrderRepairInfoMap">
        select
          id, customer_order_id, service_content, appointment_time, service_pic, version, deleted, create_user, create_time, update_user, update_time, remark
        from hs_customer_order_repair_info
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="HsCustomerOrderRepairInfoMap">
        select
          id, customer_order_id, service_content, appointment_time, service_pic, version, deleted, create_user, create_time, update_user, update_time, remark
        from hs_customer_order_repair_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="customerOrderId != null">
                and customer_order_id = #{customerOrderId}
            </if>
            <if test="serviceContent != null and serviceContent != ''">
                and service_content = #{serviceContent}
            </if>
            <if test="appointmentTime != null">
                and appointment_time = #{appointmentTime}
            </if>
            <if test="servicePic != null and servicePic != ''">
                and service_pic = #{servicePic}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into hs_customer_order_repair_info(customer_order_id, service_content, appointment_time, service_pic, version, deleted, create_user, create_time, update_user, update_time, remark, expend_id)
        values (#{customerOrderId}, #{serviceContent}, #{appointmentTime}, #{servicePic}, #{version}, #{deleted}, #{createUser}, #{createTime}, #{updateUser}, #{updateTime}, #{remark}, #{expendId})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update hs_customer_order_repair_info
        <set>
            <if test="customerOrderId != null">
                customer_order_id = #{customerOrderId},
            </if>
            <if test="serviceContent != null and serviceContent != ''">
                service_content = #{serviceContent},
            </if>
            <if test="appointmentTime != null">
                appointment_time = #{appointmentTime},
            </if>
            <if test="servicePic != null and servicePic != ''">
                service_pic = #{servicePic},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from hs_customer_order_repair_info where id = #{id}
    </delete>

    <!--根据订单编号查询明细-->
    <select id="qurInfoByOrderId" resultType="com.shands.mod.dao.model.v0701.vo.OrderQurRepairVo" parameterType="int">
        select
          service_content as serviceContent,
          date_format(appointment_time, '%Y-%m-%d %H:%i:%s') AS appointmentTime,
          service_pic as servicePic
        from hs_customer_order_repair_info
        where customer_order_id = #{orderId}
    </select>

    <!--根据订单编号查询明细-->
    <select id="qurOrderInfoForManager" resultType="com.shands.mod.dao.model.v0701.vo.OrderQurRepairInfoVo" parameterType="int">
       select
            oo.id as orderId,
            date_format(oo.create_time, '%Y-%m-%d %H:%i:%s') AS orderTime,
            oo.customer_name as customerName,
            oo.customer_phone as customerPhone,
            oo.service_type as serviceType,
            oo.service_name as serviceName,
            oo.manager_state as orderState,
            oo.order_notes as orderNotes,
            oo.room_num as roomNum,
            oo.hotel_name as hotelName,
            oo.sn as sn,
            oo.remark as remark,
            oo.arrange as arrange,
            oo.work_id as workId,
            oo.service_id as serviceId,
            date_format(cc.appointment_time, '%Y-%m-%d %H:%i:%s') AS appointmentTime,
            cc.service_content as serviceContent,
            cc.expend_id as expendId,
            cc.service_pic as servicePic
          from
              hs_customer_order_n oo inner join hs_customer_order_repair_info cc
          on oo.id = cc.customer_order_id
          where oo.id = #{orderId}
      </select>

    <!--根据订单编号查询明细-->
    <select id="qurOrderInfoForCustomer" resultType="com.shands.mod.dao.model.v0701.vo.OrderQurRepairInfoVo" parameterType="int">
         select
            oo.id as orderId,
            date_format(oo.create_time, '%Y-%m-%d %H:%i:%s') AS orderTime,
            oo.customer_name as customerName,
            oo.customer_phone as customerPhone,
            oo.service_type as serviceType,
            oo.service_name as serviceName,
            <if test="qurType != null and qurType == 1">
              oo.order_state as orderState,
            </if>
            <if test="qurType != null and qurType == 2">
              oo.manager_state as orderState,
            </if>
            oo.order_notes as orderNotes,
            oo.room_num as roomNum,
            oo.hotel_name as hotelName,
            oo.sn as sn,
            oo.remark as remark,
            oo.arrange as arrange,
            oo.company_id as companyId,
            oo.group_id as groupId,
            oo.eval as eval,
            IF (oo.manager_state > 5, oo.update_time, NULL ) AS updateTime,
            date_format(cc.appointment_time, '%Y-%m-%d %H:%i:%s') AS appointmentTime,
            cc.service_content as serviceContent,
            cc.service_pic as servicePic
          from
              hs_customer_order_n oo inner join hs_customer_order_repair_info cc
          on oo.id = cc.customer_order_id
          where oo.id = #{orderId}
      </select>

</mapper>