package com.shands.mod.dao.model.sales.tool.dto;

import lombok.Data;


/**
 * @Description:
 * @Author: wj
 * @Date: 2024/8/16 16:19
 */
@Data
public class BetterwoodOrderStatusMsg {

  // 订单主编号
  private String orderMainNo;

  // 订单主ID
  private String orderMainId;

  // 订单状态: 0 - 待付款, 1 - 已预订, 2 - 已完成, 3 - 已取消, 4 - 已入住, 5 - 未使用
  private Integer status;

  // 推送的时间戳
  private Long timeStamp;

  // 支付状态: 0 - 未付款, 1 - 已付款, 2 - 付款中, 3 - 退款中, 4 - 已退款
  private Integer payStatus;

  // 渠道类型: 13 - 德胧生态APP
  private Integer channelType;

  // 支付方式:0预付 1到店付 2现付或预付
  private Integer paymentType;

}
