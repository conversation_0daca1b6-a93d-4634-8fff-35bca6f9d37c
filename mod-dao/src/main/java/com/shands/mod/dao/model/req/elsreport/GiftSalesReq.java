package com.shands.mod.dao.model.req.elsreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("本单位部门礼包销售接口请求参数")
public class GiftSalesReq {

  @ApiModelProperty("时间参数")
  private String dateStr;
  @ApiModelProperty("时间维度")
  private String rankDateEnum;
  @ApiModelProperty("酒店code")
  private String hotelCode;

  /**
   * 部门名称
   */
  @ApiModelProperty("部门名称")
  private String departmentName;
}
