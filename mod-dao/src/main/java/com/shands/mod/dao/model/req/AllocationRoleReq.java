package com.shands.mod.dao.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * <Description> 用户分配角色请求参数<br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/08/25 10:54 上午 <br>
 * @see com.shands.mod.dao.model.req <br>
 */
@ApiModel(value = "用户分配角色请求参数")
public class AllocationRoleReq {

  @ApiModelProperty(value = "用户id")
  private Integer userId;

  @ApiModelProperty(value = "角色id集合")
  private List<Integer> roleIds;

  private Long userExtendId;

  public Long getUserExtendId() {
    return userExtendId;
  }

  public void setUserExtendId(Long userExtendId) {
    this.userExtendId = userExtendId;
  }

  public Integer getUserId() {
    return userId;
  }

  public void setUserId(Integer userId) {
    this.userId = userId;
  }

  public List<Integer> getRoleIds() {
    return roleIds;
  }

  public void setRoleIds(List<Integer> roleIds) {
    this.roleIds = roleIds;
  }
}
