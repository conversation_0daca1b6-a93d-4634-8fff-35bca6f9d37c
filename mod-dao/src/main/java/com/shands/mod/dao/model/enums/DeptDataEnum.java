package com.shands.mod.dao.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/11/3 19:08
 */
@Getter
@AllArgsConstructor
public enum DeptDataEnum {
  ROOM_ALL("官渠间夜（全酒店）"),
  ROOM_OWN("官渠间夜（本酒店）"),
  REVENUE_OWN("官渠营收（本酒店）"),
  REVENUE_ALL("官渠营收（全酒店）"),
  FACE_MEMBER("面对面会员发展"),
  ENTERPRISE_MEMBER("企业个人会员"),
  NEW_CONSUMPTION_MEMBER("新增消费会员"),
  CONSUMPTION_MEMBER("消费会员"),
  MEMBER_REVENUE("发展会员营收"),

  ATTRIBUTED_MEMBERS ("归属会员"),
  LOST_MEMBERS("流失会员"),
  NEW_APP_MEMBERS("新增APP会员"),
  CARD_SALES_MEMBERS("售卡会员"),

  // 法宝
  HOTEL_VOUCHER("酒店法宝"),
  APP_DISCOUNT_VOUCHER("APP立减券"),
  BREAKFAST_VOUCHER("早餐法宝"),
  UPGRADE_ROOM_VOUCHER("免费升房"),
  DELAY_CHECKOUT_VOUCHER("延迟退房"),

  // 百达卡
  BETTERWOOD_CARD_TOTAL("百达卡汇总"),
  QIHANG_CARD("启航卡"),
  MANLU_CARD("漫旅卡"),
  KAITUO_CARD("开拓卡")
  ;

  private final String name;
}
