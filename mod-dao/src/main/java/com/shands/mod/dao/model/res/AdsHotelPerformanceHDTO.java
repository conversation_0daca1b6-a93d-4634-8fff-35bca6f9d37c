package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 聚合类
 * @Author: wj
 * @Date: 2025/3/10 14:45
 */
@Data
@Builder
public class AdsHotelPerformanceHDTO {

  /**
   * 酒店名称
   */
  private String hotelName;

  /**
   * 员工姓名
   */
  private String employeeName;
  /**
   * 部门名称
   */
  private String employeeDept;
  /**
   * 归属会员数
   */
  private Long attributedMembers;

  /**
   * 流失会员数
   */
  private Long lostMembers;

  /**
   * 新增app会员数
   */
  private Long newAppMembers;

  /**
   * APP消费会员间夜数
   */
  private Double roomNightsN;

  /**
   * APP消费会员间房费
   */
  private Double roomAmt;

  /**
   * 售卡数量
   */
  private Long bdCardCnt;

  /**
   * 签约企业卡数
   */
  private Long enterpriseCards;

  /**
   * 奖金
   */
  private Double bonusAmt;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 物理百房消费间夜
   */
  private Double hundredRoomNightsN;

  /**
   * 百达卡销售金额
   */
  private Double memCardAmt;

  /**
   * 贡献间夜
   */
  private Double cardRoomNightsN;

  /**
   * 启航卡售卡数
   */
  private Long memCardOneN;

  /**
   * 启航卡预计奖金
   */
  private Double memCardOneBonusAmt;

  /**
   * 漫旅卡售卡数
   */
  private Long memCardSecondN;

  /**
   * 漫旅卡预计奖金
   */
  private Double memCardSecondBonusAmt;

  /**
   * 开拓卡售卡数
   */
  private Long memCardThirdN;

  /**
   * 开拓卡预计奖金
   */
  private Double memCardThirdBonusAmt;

  /**
   * 酒店代码
   */
  private String hotelCode;

  /**
   * 员工通宝ID
   */
  private String employeeId;

  /**
   * 券发放数量
   */
  private Long voucherSendCount;

  /**
   * 券核销数量
   */
  private Long voucherUseCount;

  // 字段名称到Field对象的映射
  private static Map<String, Field> fieldNameToFieldMap;

  static {
    fieldNameToFieldMap = new HashMap<>();
    try {
      // 获取类的所有字段
      Field[] fields = AdsHotelPerformanceHDTO.class.getDeclaredFields();
      for (Field field : fields) {
        fieldNameToFieldMap.put(field.getName(), field);
      }
    } catch (Exception e) {
      throw new RuntimeException("Error initializing field map", e);
    }
  }

  // 根据字段名称获取字段值
  public Object getValueByFieldName(String fieldName) {
    Field field = fieldNameToFieldMap.get(fieldName);
    if (field == null) {
      return null;
    }

    try {
      field.setAccessible(true); // 设置为可访问私有字段
      return field.get(this);
    } catch (IllegalAccessException e) {
      throw new RuntimeException("Error accessing field", e);
    }
  }

  public void setValueByFieldName(String fieldName, BigDecimal newVal) {

    try {
      Field field = fieldNameToFieldMap.get(fieldName);
      if (field == null) {
        return;
      }
      field.setAccessible(true); // 设置为可访问私有字段
      field.set(this, newVal);
    } catch (IllegalAccessException e) {
      throw new RuntimeException("Error update field", e);
    }
  }
}


