<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.CompanyMapper">
    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.Company">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="TAX_ID" jdbcType="VARCHAR" property="taxId"/>
        <result column="CITY_ID" jdbcType="VARCHAR" property="cityId"/>
        <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="TRADE" jdbcType="VARCHAR" property="trade"/>
        <result column="SMS_TOTAL" jdbcType="INTEGER" property="smsTotal"/>
        <result column="SMS_USED" jdbcType="INTEGER" property="smsUsed"/>
        <result column="PRODUCTION_ID" jdbcType="INTEGER" property="productionId"/>
        <result column="EXPIRATION" jdbcType="DATE" property="expiration"/>
        <result column="PMS_TYPE" jdbcType="VARCHAR" property="pmsType"/>
        <result column="ACTIVITY" jdbcType="TINYINT" property="activity"/>
        <result column="LNG_GD" jdbcType="VARCHAR" property="lngGd"/>
        <result column="LAT_GD" jdbcType="VARCHAR" property="latGd"/>
        <result column="RADIUS" jdbcType="INTEGER" property="radius"/>
        <result column="SITE_STATUS" jdbcType="TINYINT" property="siteStatus" />
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId"/>
        <result column="VERSION" jdbcType="INTEGER" property="version"/>
        <result column="DELETED" jdbcType="TINYINT" property="deleted"/>
        <result column="CREATE_USER" jdbcType="INTEGER" property="createUser"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_USER" jdbcType="INTEGER" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>

        <result column="picture" jdbcType="VARCHAR" property="picture"/>
      <result column="banner_pic" jdbcType="VARCHAR" property="bannerPic"/>
      <result column="is_show" jdbcType="INTEGER" property="isShow"/>
      <result column="back_pic" jdbcType="VARCHAR" property="backPic"/>

      <result column="logo" jdbcType="VARCHAR" property="logo"/>

      <result column="order_id" jdbcType="INTEGER" property="orderId"/>
      <result column="special_ticket" jdbcType="INTEGER" property="specialTicket"/>
      <result column="general_ticket" jdbcType="INTEGER" property="generalTicket" />
      <result column="invoice_titel" jdbcType="VARCHAR" property="invoiceTitel" />
      <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
      <result column="phone" jdbcType="VARCHAR" property="phone"/>
      <result column="weather_city_id" jdbcType="INTEGER" property="weatherCityId"/>
      <result column="member_channel" jdbcType="VARCHAR" property="memberChannel"/>
      <result column = "card_type" jdbcType="VARCHAR" property="cardType" />
      <result column = "card_level" jdbcType="VARCHAR" property="cardLevel" />
      <result column="in_sms" jdbcType="TINYINT" property="inSms"/>
      <result column="apply_channel" jdbcType="VARCHAR" property="applyChannel"/>
      <result column="map_coverage" jdbcType="VARCHAR" property="mapCoverage"/>
      <result column="coupon" jdbcType="INTEGER" property="coupon"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.shands.mod.dao.model.Company">
        <result column="SETTING" jdbcType="LONGVARCHAR" property="setting"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, NAME, TAX_ID, CITY_ID, ADDRESS, DESCRIPTION, TRADE, SMS_TOTAL, SMS_USED, PRODUCTION_ID, 
    EXPIRATION, PMS_TYPE, ACTIVITY,LNG_GD,LAT_GD,RADIUS,SITE_STATUS, GROUP_ID, VERSION, DELETED, CREATE_USER, CREATE_TIME,
    UPDATE_USER, UPDATE_TIME,picture,banner_pic,is_show,back_pic,logo,order_id,special_ticket,general_ticket,invoice_titel,tax_number,phone,weather_city_id,member_channel,
    card_type,card_level,in_sms,apply_channel,map_coverage,coupon
  </sql>
    <sql id="Blob_Column_List">
    SETTING
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
      ,
      <include refid="Blob_Column_List"/>
        from mod_company
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        DELETE
        FROM mod_company
        WHERE ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.shands.mod.dao.model.Company">
        INSERT INTO mod_company (ID, NAME, TAX_ID,
        CITY_ID, ADDRESS, DESCRIPTION,
        TRADE, SMS_TOTAL, SMS_USED,
        PRODUCTION_ID, EXPIRATION, PMS_TYPE,
        ACTIVITY,LNG_GD,LAT_GD, RADIUS,SITE_STATUS,GROUP_ID, VERSION,
        DELETED, CREATE_USER, CREATE_TIME,
        UPDATE_USER, UPDATE_TIME, SETTING,picture,banner_pic,is_show,back_pic,special_ticket,general_ticket,phone,weather_city_id,member_channel,
        card_type,card_level,in_sms)
        VALUES (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{taxId,jdbcType=VARCHAR},
        #{cityId,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
        #{description,jdbcType=VARCHAR},
        #{trade,jdbcType=VARCHAR}, #{smsTotal,jdbcType=INTEGER}, #{smsUsed,jdbcType=INTEGER},
        #{productionId,jdbcType=INTEGER}, #{expiration,jdbcType=DATE},
        #{pmsType,jdbcType=VARCHAR},
        #{activity,jdbcType=TINYINT},
        #{lngGd,jdbcType=VARCHAR},#{latGd,jdbcType=VARCHAR},#{radius,jdbcType=INTEGER},
        #{siteStatus,jdbcType=INTEGER},
        #{groupId,jdbcType=INTEGER}, #{version,jdbcType=INTEGER},
        #{deleted,jdbcType=TINYINT}, #{createUser,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{setting,jdbcType=LONGVARCHAR},
        #{picture},#{bannerPic},#{isShow},#{backPic},#{specialTicket},#{generalTicket},
        #{invoiceTitel},#{taxNumber},#{phone},#{weatherCityId},#{memberChannel},#{cardType},#{cardLevel},#{inSms}
      )
    </insert>
    <insert id="insertSelective" parameterType="com.shands.mod.dao.model.Company"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into mod_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="taxId != null">
                TAX_ID,
            </if>
            <if test="cityId != null">
                CITY_ID,
            </if>
            <if test="address != null">
                ADDRESS,
            </if>
            <if test="description != null">
                DESCRIPTION,
            </if>
            <if test="trade != null">
                TRADE,
            </if>
            <if test="smsTotal != null">
                SMS_TOTAL,
            </if>
            <if test="smsUsed != null">
                SMS_USED,
            </if>
            <if test="productionId != null">
                PRODUCTION_ID,
            </if>
            <if test="expiration != null">
                EXPIRATION,
            </if>
            <if test="pmsType != null">
                PMS_TYPE,
            </if>
            <if test="activity != null">
                ACTIVITY,
            </if>
            <if test="lngGd !=null">
              LNG_GD,
            </if>
            <if test="latGd !=null">
              LAT_GD,
            </if>
            <if test="radius !=null">
              RADIUS,
            </if>
            <if test="siteStatus !=null">
              SITE_STATUS,
            </if>
            <if test="groupId != null">
                GROUP_ID,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="deleted != null">
                DELETED,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="setting != null">
                SETTING,
            </if>

          <if test="picture != null">
            picture,
          </if>
          <if test="bannerPic != null">
            banner_pic,
          </if>
          <if test="isShow != null">
            is_show,
          </if>
          <if test="backPic !=null">
            back_pic,
          </if>
          <if test="specialTicket !=null">
            special_ticket,
          </if>
          <if test="generalTicket !=null">
            general_ticket,
          </if>
          <if test="invoiceTitel !=null">
            invoice_titel,
          </if>
          <if test="taxNumber !=null">
            tax_number,
          </if>
          <if test="phone !=null">
            phone,
          </if>
          <if test="weatherCityId !=null">
            weather_city_id,
          </if>
          <if test="memberChannel !=null">
            member_channel,
          </if>
          <if test="cardType !=null">
            card_type,
          </if>
          <if test="cardLevel !=null">
            card_level,
          </if>
          <if test="inSms !=null">
            in_sms,
          </if>
          <if test="applyChannel !=null">
            apply_channel,
          </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="taxId != null">
                #{taxId,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="trade != null">
                #{trade,jdbcType=VARCHAR},
            </if>
            <if test="smsTotal != null">
                #{smsTotal,jdbcType=INTEGER},
            </if>
            <if test="smsUsed != null">
                #{smsUsed,jdbcType=INTEGER},
            </if>
            <if test="productionId != null">
                #{productionId,jdbcType=INTEGER},
            </if>
            <if test="expiration != null">
                #{expiration,jdbcType=DATE},
            </if>
            <if test="pmsType != null">
                #{pmsType,jdbcType=VARCHAR},
            </if>
            <if test="activity != null">
                #{activity,jdbcType=TINYINT},
            </if>
            <if test="lngGd !=null">
                #{lngGd,jdbcType=VARCHAR},
            </if>
            <if test="latGd !=null">
                #{latGd,jdbcType=VARCHAR},
            </if>
            <if test="radius !=null">
                #{RADIUS,jdbcType=INTEGER},
            </if>
            <if test="siteStatus !=null">
                #{siteStatus,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="setting != null">
                #{setting,jdbcType=LONGVARCHAR},
            </if>
          <if test="picture != null">
            #{picture},
          </if>
          <if test="bannerPic != null">
            #{bannerPic},
          </if>
          <if test="isShow != null">
            #{isShow},
          </if>
          <if test="backPic != null">
            #{backPic},
          </if>
          <if test="specialTicket !=null">
            #{specialTicket},
          </if>
          <if test="generalTicket !=null">
            #{generalTicket},
          </if>
          <if test="invoiceTitel !=null">
            #{invoiceTitel},
          </if>
          <if test="taxNumber !=null">
            #{taxNumber},
          </if>
          <if test="phone !=null">
            #{phone},
          </if>
          <if test="weatherCityId !=null">
            #{weatherCityId,jdbcType=INTEGER},
          </if>
          <if test="memberChannel !=null">
            #{memberChannel,jdbcType=VARCHAR},
          </if>
          <if test="cardType !=null">
            #{cardType,jdbcType=VARCHAR},
          </if>
          <if test="cardLevel !=null">
            #{cardType,jdbcType=VARCHAR},
          </if>
          <if test="inSms !=null">
            #{inSms,jdbcType=TINYINT},
          </if>
          <if test="applyChannel !=null">
            #{applyChannel,jdbcType=VARCHAR},
          </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.Company">
        UPDATE mod_company
        <set>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="taxId != null">
                TAX_ID = #{taxId,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                CITY_ID = #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="trade != null">
                TRADE = #{trade,jdbcType=VARCHAR},
            </if>
            <if test="smsTotal != null">
                SMS_TOTAL = #{smsTotal,jdbcType=INTEGER},
            </if>
            <if test="smsUsed != null">
                SMS_USED = #{smsUsed,jdbcType=INTEGER},
            </if>
            <if test="productionId != null">
                PRODUCTION_ID = #{productionId,jdbcType=INTEGER},
            </if>
            <if test="expiration != null">
                EXPIRATION = #{expiration,jdbcType=DATE},
            </if>
            <if test="pmsType != null">
                PMS_TYPE = #{pmsType,jdbcType=VARCHAR},
            </if>
            <if test="activity != null">
                ACTIVITY = #{activity,jdbcType=TINYINT},
            </if>
            <if test="lngGd !=null">
                LNG_GD=#{lngGd,jdbcType=VARCHAR},
            </if>
            <if test="latGd !=null">
                LAT_GD=#{latGd,jdbcType=VARCHAR},
            </if>
            <if test="radius !=null">
                RADIUS = #{radius,jdbcType=INTEGER},
            </if>
            <if test="siteStatus !=null">
                SITE_STATUS = #{siteStatus,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                GROUP_ID = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                DELETED = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="setting != null">
                SETTING = #{setting,jdbcType=LONGVARCHAR},
            </if>
          <if test="picture != null">
            picture = #{picture},
          </if>
          <if test="bannerPic != null">
            banner_pic=#{bannerPic},
          </if>
          <if test="isShow != null">
            is_show =#{isShow},
          </if>
          <if test="backPic != null">
            back_pic=#{backPic},
          </if>
          <if test="specialTicket != null">
            special_ticket =#{specialTicket},
          </if>
          <if test="generalTicket != null">
            general_ticket=#{generalTicket},
          </if>
          <if test="invoiceTitel !=null">
            invoice_titel = #{invoiceTitel},
          </if>
          <if test="taxNumber !=null">
           tax_number = #{taxNumber},
          </if>
          <if test="phone !=null">
            phone = #{phone},
          </if>
          <if test="weatherCityId !=null">
            weather_city_id = #{weatherCityId,jdbcType=INTEGER},
          </if>
          <if test="memberChannel !=null">
            member_channel = #{memberChannel,jdbcType=VARCHAR},
          </if>
          <if test="cardType !=null">
            card_type= #{cardType,jdbcType=VARCHAR},
          </if>
          <if test="cardLevel !=null">
            card_level= #{cardType,jdbcType=VARCHAR},
          </if>
          <if test="inSms !=null">
           in_sms = #{inSms,jdbcType=TINYINT},
          </if>
          <if test="applyChannel !=null">
            apply_channel =  #{applyChannel,jdbcType=VARCHAR},
          </if>
          <if test="coupon != null">
            coupon=#{coupon},
          </if>
        </set>
        WHERE ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.shands.mod.dao.model.Company">
        UPDATE mod_company
        SET NAME = #{name,jdbcType=VARCHAR},
        TAX_ID = #{taxId,jdbcType=VARCHAR},
        CITY_ID = #{cityId,jdbcType=VARCHAR},
        ADDRESS = #{address,jdbcType=VARCHAR},
        DESCRIPTION = #{description,jdbcType=VARCHAR},
        TRADE = #{trade,jdbcType=VARCHAR},
        SMS_TOTAL = #{smsTotal,jdbcType=INTEGER},
        SMS_USED = #{smsUsed,jdbcType=INTEGER},
        PRODUCTION_ID = #{productionId,jdbcType=INTEGER},
        EXPIRATION = #{expiration,jdbcType=DATE},
        PMS_TYPE = #{pmsType,jdbcType=VARCHAR},
        ACTIVITY = #{activity,jdbcType=TINYINT},
        LNG_GD = #{lngGd,jdbcType=VARCHAR},
        LAT_GD = #{latGd,jdbcType=VARCHAR},
        RADIUS = #{radius,jdbcType=INTEGER},
        SITE_STATUS = #{siteStatus,jdbcType=INTEGER},
        GROUP_ID = #{groupId,jdbcType=INTEGER},
        VERSION = #{version,jdbcType=INTEGER},
        DELETED = #{deleted,jdbcType=TINYINT},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        SETTING = #{setting,jdbcType=LONGVARCHAR},
        picture = #{picture},
        is_show=#{isShow},
        banner_pic=#{bannerPic},
        back_pic = #{backPic},
        invoice_titel = #{invoiceTitel},
        tax_number = #{taxNumber},
        phone = #{phone},
        weather_city_id = #{weatherCityId,jdbcType=INTEGER},
        member_channel = #{memberChannel,jdbcType=VARCHAR},
        card_level = #{cardLevel,jdbcType=VARCHAR},
        card_type = #{cardType,jdbcType=VARCHAR},
        in_sms = #{inSms,jdbcType=TINYINT}
        WHERE ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.Company">
        UPDATE mod_company
        SET NAME = #{name,jdbcType=VARCHAR},
        TAX_ID = #{taxId,jdbcType=VARCHAR},
        CITY_ID = #{cityId,jdbcType=VARCHAR},
        ADDRESS = #{address,jdbcType=VARCHAR},
        DESCRIPTION = #{description,jdbcType=VARCHAR},
        TRADE = #{trade,jdbcType=VARCHAR},
        SMS_TOTAL = #{smsTotal,jdbcType=INTEGER},
        SMS_USED = #{smsUsed,jdbcType=INTEGER},
        PRODUCTION_ID = #{productionId,jdbcType=INTEGER},
        EXPIRATION = #{expiration,jdbcType=DATE},
        PMS_TYPE = #{pmsType,jdbcType=VARCHAR},
        ACTIVITY = #{activity,jdbcType=TINYINT},
        LNG_GD = #{lngGd,jdbcType=VARCHAR},
        LAT_GD = #{latGd,jdbcType=VARCHAR},
        RADIUS = #{radius,jdbcType=INTEGER},
        SITE_STATUS = #{siteStatus,jdbcType=INTEGER},
        GROUP_ID = #{groupId,jdbcType=INTEGER},
        VERSION = #{version,jdbcType=INTEGER},
        DELETED = #{deleted,jdbcType=TINYINT},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        picture = #{picture},
        banner_pic=#{bannerPic},
        is_show =#{isShow},
        back_pic = #{backPic},
        special_ticket = #{specialTicket},
        general_ticket = #{generalTicket},
        invoice_titel = #{invoiceTitel},
        tax_number = #{taxNumber},
        phone = #{phone},
        weather_city_id = #{weatherCityId,jdbcType=INTEGER},
        member_channel = #{memberChannel,jdbcType=VARCHAR},
        card_level = #{cardLevel,jdbcType=VARCHAR},
        card_type = #{cardType,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=INTEGER}
    </update>

    <!-- 自定义SQL -->
    <resultMap id="getAllById" type="com.shands.mod.dao.model.req.CompanyEditInfoResAndReq">
        <result column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="TAX_ID" property="taxId"/>
        <result column="CITY_ID" property="cityId"/>
        <result column="ADDRESS" property="address"/>
        <result column="SMS_TOTAL" property="smsTotal"/>
        <result column="SMS_USED" property="smsUsed"/>
        <result column="PRODUCTION_ID" property="productionId"/>
        <result column="EXPIRATION" property="expiration"/>
        <result column="SERVICE_NAME" property="serviceName"/>
        <result column="LNG_GD"  property="lngGd"/>
        <result column="LAT_GD" property="latGd"/>
        <result column="RADIUS"  property="radius"/>
        <result column="SITE_STATUS" property="siteStatus" />
      <result column="phone" property="phone" />
      <result column="logo" property="logo" />
      <result column="CODE" property="code" />
      <result column="member_channel" property="memberChannel" />
      <result column="card_level" property="cardLevel" />
      <result column="card_type" property="cardType" />
      <result column="coupon" property="coupon"/>
    </resultMap>
    <select id="getAllById" resultMap="getAllById">
        SELECT mc.ID,
        mc.NAME,
        mc.TAX_ID,
        mc.CITY_ID,
        mc.ADDRESS,
        mc.SMS_TOTAL,
        mc.SMS_USED,
        mc.PRODUCTION_ID,
        mc.EXPIRATION,
        mc.LNG_GD,mc.LAT_GD,mc.RADIUS,mc.SITE_STATUS,
        mp.NAME SERVICE_NAME,
        mc.logo,
        mc.phone,
        mc.member_channel,
        mc.card_level,
        mc.card_type,
        hpg.CODE,
        mc.coupon
        FROM mod_company mc
        LEFT JOIN mod_production mp ON mc.PRODUCTION_ID = mp.ID
        LEFT JOIN hs_pms_greencloud hpg ON mc.ID = hpg.COMPANY_ID
        WHERE mc.ID = #{id}
    </select>
    <update id="updateInfoById">
        UPDATE mod_company mc
        SET NAME = #{model.name},
        CITY_ID = #{model.cityId},
        ADDRESS = #{model.address},
        UPDATE_TIME = NOW(),
        UPDATE_USER = #{param1.userId},
        LNG_GD = #{model.lngGd},
        LAT_GD = #{model.latGd},
        RADIUS = #{model.radius},
        SITE_STATUS = #{model.siteStatus},
        logo = #{model.logo},
        phone = #{model.phone},
        member_channel = #{model.memberChannel},
        card_type = #{model.cardType},
        card_level = #{model.cardLevel}
        WHERE ID = #{model.id}
    </update>
    <insert id="addChildCompany" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mod_company (NAME, CITY_ID, ADDRESS,member_channel,card_type,card_level, GROUP_ID, ACTIVITY, CREATE_TIME, CREATE_USER)
        VALUE
        (#{child.name,jdbcType=VARCHAR},
        #{child.cityId,jdbcType=INTEGER},
        #{child.address,jdbcType=VARCHAR},
        #{child.memberChannel,jdbcType=VARCHAR},
        #{child.cardType,jdbcType=VARCHAR},
        #{child.cardLevel,jdbcType=VARCHAR},
        #{child.groupId,jdbcType=INTEGER},
        1, now(), #{child.userId})
    </insert>
    <update id="updateCompany">
        UPDATE mod_company
        SET NAME = #{child.name},
        CITY_ID = #{child.cityId},
        ADDRESS= #{child.address},
        DESCRIPTION =#{child.desc},
        member_channel = #{child.memberChannel},
        card_type = #{child.cardType},
        card_level = #{child.cardLevel},
        UPDATE_USER = #{child.userId},
        UPDATE_TIME = now()
        WHERE ID = #{child.id}
    </update>
    <resultMap id="getInfoList" type="com.shands.mod.dao.model.res.CompanyGetChildInfoListRes">
        <result column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="ADMIN_NAME" property="adminName"/>
        <result column="MOBILE" property="phoneNum"/>
        <result column="EMAIL" property="email"/>
        <result column="PRODUCTION_NAME" property="productionName"/>
        <result column="EXPIRATION" property="expiration"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CREATE_NAME" property="createName"/>
        <result column="ACTIVITY" property="status"/>
        <result column="in_sms" property="inSms"/>
    </resultMap>
    <select id="getInfoList" resultMap="getInfoList">
        SELECT mc.ID,mc.NAME,mp.NAME PRODUCTION_NAME,mc.EXPIRATION,mc.UPDATE_TIME,mu.NAME
        CREATE_NAME,ACTIVITY,mus.NAME ADMIN_NAME,mus.EMAIL, mus.MOBILE,mc.in_sms
        FROM mod_company mc
        LEFT JOIN mod_production mp ON mc.PRODUCTION_ID = mp.ID
        LEFT JOIN mod_user mu ON mc.CREATE_USER = mu.ID
        left join mod_user_extend mue on mc.ID = mue.COMPANY_ID and (mue.ROLES REGEXP 'group_admin' or mue.ROLES REGEXP 'hotel_admin')
        left join mod_user mus on mue.USER_ID = mus.ID
        WHERE mc.GROUP_ID=#{model.groupId} AND mc.ID!=#{model.groupId}
        <if test="model.name != null and model.name != ''">
            AND mc.NAME LIKE concat('%',#{model.name},'%')
        </if>
        <if test="model.productionVersion != null">
            AND mp.ID = #{model.productionVersion}
        </if>
        <if test="model.status != null ">
            AND mc.ACTIVITY = #{model.status}
        </if>
    </select>
    <resultMap id="GetUserInfoMap" type="com.shands.mod.dao.model.res.CompanyGetUserInfoRes">
        <result column="NAME" property="name"/>
        <result column="ID" property="userId"/>
        <result column="EMAIL" property="email"/>
    </resultMap>
    <select id="getUserInfo" resultMap="GetUserInfoMap">
        SELECT ID, NAME, EMAIL
        FROM mod_user mu
        WHERE mu.MOBILE = #{phone}
    </select>
    <resultMap id="GetCustomerListMap" type="com.shands.mod.dao.model.res.CustomerGetListRes">
        <result column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="PRODUCTION_NAME" property="production"/>
        <result column="ADMIN_NAME" property="groupContact"/>
        <result column="CREATE_TIME" property="createDate"/>
        <result column="CREATE_NAME" property="createName"/>
        <result column="ACTIVITY" property="status"/>
    </resultMap>
    <select id="getCustomerList" resultMap="GetCustomerListMap">
        SELECT mc.ID ,mc.NAME,mp.NAME PRODUCTION_NAME,mu.NAME
        ADMIN_NAME,mc.CREATE_TIME,mc.ACTIVITY,u.NAME CREATE_NAME
        FROM mod_company mc
        LEFT JOIN mod_production mp ON mc.PRODUCTION_ID = mp.ID
        LEFT JOIN mod_user_extend mue ON mue.COMPANY_ID = mc.ID AND (mue.ROLES REGEXP 'group_admin' or mue.ROLES REGEXP 'hotel_admin')
        LEFT JOIN mod_user mu ON mu.ID = mue.USER_ID
        LEFT JOIN mod_user u ON u.id = mc.CREATE_USER
        <where>
            (mc.ID = mc.GROUP_ID or mc.GROUP_ID is null)
            and mc.DELETED = 0 and mc.ID != 1
            <if test="model.name != null and model.name != ''">
                AND mc.NAME LIKE concat('%',#{model.name},'%')
            </if>
            <if test="model.status != null">
                AND mc.ACTIVITY = #{model.status}
            </if>
            <if test="model.productionVersion != null">
                AND mp.ID = #{model.productionVersion}
            </if>
        </where>
    </select>
    <insert id="addGroup" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mod_company
        (NAME, CITY_ID, ADDRESS, DESCRIPTION, ACTIVITY, CREATE_USER, CREATE_TIME)
        VALUE
        (#{model.name}, #{model.cityId}, #{model.address}, #{model.desc}, 1, #{model.userId}, NOW())
    </insert>
    <update id="switchStatus">
        UPDATE mod_company mc
        SET ACTIVITY = if(ACTIVITY = 1, 0, 1),
        UPDATE_TIME = now(),
        UPDATE_USER = #{userId}
        WHERE ID = #{companyId}
        AND mc.DELETED = 0
    </update>
    <resultMap id="GetInfoMap" type="com.shands.mod.dao.model.res.CustomerGetInfoRes">
        <result column="COMPANY_NAME" property="name"/>
        <result column="CITY_ID" property="cityId"/>
        <result column="DESCRIPTION" property="desc"/>
        <result column="ADDRESS" property="address"/>
        <result column="NAME" property="groupContact"/>
        <result column="EMAIL" property="email"/>
        <result column="MOBILE" property="number"/>
      <result column="member_channel" property="memberChannel"/>
      <result column="card_type" property="cardType"/>
      <result column="card_level" property="cardLevel"/>
    </resultMap>
    <select id="getInfo" resultMap="GetInfoMap">
        SELECT mc.NAME COMPANY_NAME,
        mc.CITY_ID,
        mc.DESCRIPTION,
        mc.ADDRESS,
        mu.NAME,
        mu.EMAIL,
        mu.MOBILE,
        mc.member_channel,
        mc.card_type,
        mc.card_level
        FROM mod_company mc
        LEFT JOIN mod_user_extend mue
        ON mue.COMPANY_ID = mc.ID AND (mue.ROLES REGEXP 'group_admin' or mue.ROLES REGEXP 'hotel_admin') AND mue.DELETED = 0
        LEFT JOIN mod_user mu ON mu.ID = mue.USER_ID
        WHERE mc.ID = #{companyId}
    </select>
    <select id="getGroupIds" resultType="com.shands.mod.dao.model.res.CustomerGetGroupIdsRes">
    SELECT ID, NAME
    FROM mod_company
    WHERE GROUP_ID = ID
       OR GROUP_ID IS NULL
  </select>
    <update id="updateAdminByCompanyId">
        UPDATE mod_user_extend
        SET ROLES = NULL,
        DELETED = 1
        WHERE COMPANY_ID = #{companyId}
        AND (ROLES REGEXP 'hotel_admin' or ROLES REGEXP 'group_admin');
    </update>
    <select id="getCompanySetting" resultType="java.lang.String">
        SELECT SETTING
        FROM mod_company
        WHERE ID = #{companyId}
    </select>
    <update id="updateSMSById" parameterType="java.util.Map">
        UPDATE mod_company
        <if test="smsTotal !=null">
            SET SMS_TOTAL = SMS_TOTAL + #{amout}
        </if>
        <if test="smsTotal ==null">
            SET SMS_TOTAL = 0 + #{amout}
        </if>
        WHERE
        id = #{companyId}
    </update>
    <update id="updateDateM" parameterType="java.util.Map">
        UPDATE mod_company
        <if test="expiration !=null">
            SET EXPIRATION = DATE_ADD( EXPIRATION, INTERVAL #{amout} MONTH ),
        </if>
        <if test="expiration ==null">
            SET EXPIRATION = DATE_ADD(now(), INTERVAL #{amout} MONTH ),
        </if>
        PRODUCTION_ID = #{productiodId}
        WHERE
        id = #{companyId}
    </update>
    <update id="updateDateY" parameterType="java.util.Map">
        UPDATE mod_company
        <if test="expiration !=null">
            SET EXPIRATION = DATE_ADD( EXPIRATION, INTERVAL #{amout} YEAR ),
        </if>
        <if test="expiration ==null">
            SET EXPIRATION = DATE_ADD(now(), INTERVAL #{amout} YEAR ),
        </if>
        PRODUCTION_ID = #{productiodId}
        WHERE
        id = #{companyId}
    </update>
    <update id="addSmsUsed">
        UPDATE mod_company
        SET SMS_USED = SMS_USED + 1
        WHERE id = #{companyId,jdbcType=INTEGER}
    </update>
    <select id="findCompanyNameByCompanyId" resultType="java.lang.String">
        SELECT NAME
        FROM mod_company mc
        WHERE ID = #{companyId}
    </select>

  <!--根据传来的经纬度 计算到酒店的距离-->
  <select id="getDistance" parameterType="com.shands.mod.dao.model.req.DistanceReq" resultType="com.shands.mod.dao.model.res.CompanyDistance">
    SELECT
        a.radius,
	      ROUND(
	      6378.138 * 2 * ASIN(
	      SQRT(
	      POW(
	      SIN( ( #{latGd} * PI( ) / 180 - a.lat_gd * PI( ) / 180 ) / 2 ),
	      2
	      ) + COS( #{latGd} * PI( ) / 180 ) * COS( a.lat_gd * PI( ) / 180 ) * POW(
	      SIN( ( #{lngGd} * PI( ) / 180 - a.lng_gd * PI( ) / 180 ) / 2 ),
	      2
	      )
	      )
	      ) * 1000
	      ) AS distance
    FROM
	      mod_company a
    WHERE
	      id =#{companyId}
  </select>
    <select id="getAllCompany" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mod_company
        where DELETED = 0 and is_show = 1 order by order_id
    </select>

  <select id="getBreakfasts" resultType="com.shands.mod.dao.model.v0701.vo.BreakfastRemindVo">
    SELECT
      pp.hotel_code as companyCode,
      pp.hotel_name as companyName,
      pp.hotel_id as companyId,
      ff.population as population,
      ff.content as content
    FROM
      mod_hotel_info pp
      INNER JOIN mod_breakfast_warn_config ff ON pp.hotel_id = ff.company_id
    WHERE
      pp.hotel_status = 1
    and ff.is_open = 1
    and ff.content is not null
  </select>
  <!--得到发票是否开关-->
  <select id="getInvoice" resultType="com.shands.mod.dao.model.Company">
    select special_ticket specialTicket,
      general_ticket generalTicket from mod_company where id=#{companyId}
  </select>
  <!--获得企业开票信息-->
  <select id="getTaxNumber" resultType="com.shands.mod.dao.model.Company">
    select invoice_titel invoiceTitel,
    tax_number taxNumber
    from mod_company where id = #{companyId}
  </select>

  <select id="weatherForCompany" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select <include refid="Base_Column_List"/>
    from mod_company where DELETED = 0 and is_show = 1
    <if test="companyId !=null">
      and id = #{companyId}
    </if>
  </select>

  <select id="selectForOnlie" resultType="java.lang.String">
    select mm.hotel_code CODE
    from mod_hotel_info mm
    where mm.hotel_status = 1 and mm.in_sms_status = 1 and mm.miniprogram_status = 1
  </select>

  <select id="selectByHotelCode" parameterType="java.lang.String" resultType="com.shands.mod.dao.model.v0701.vo.CheckInCompanyVo">
    select
           hotel_id as companyId,
           hotel_name as hotelName,
           hotel_code as hotelCode,
           url_sheme as urlSheme
    from mod_hotel_info where hotel_code = #{hotelCode}
  </select>
  <select id="selectCompanyAndPms" resultType="com.shands.mod.dao.model.v0701.vo.CompanyPmsVo">
    SELECT t1.id companyId,t1.name,t.code,t.HOTELID hotelId,t1.CITY_ID cityId,t1.ADDRESS address,t1.PICTURE picture,
           t1.DESCRIPTION,t1.TRADE,t1.SMS_TOTAL,t1.SMS_USED,t1.PRODUCTION_ID,t1.EXPIRATION,t1.PMS_TYPE pmsType,t1.ACTIVITY,
           t1.LNG_GD lngGd,t1.LAT_GD latGd,t1.RADIUS,t1.SITE_STATUS siteStatus,t1.GROUP_ID groupId,t1.VERSION,t1.DELETED,
           t1.CREATE_USER createUser,t1.CREATE_TIME createTime,t1.UPDATE_USER updateUser,t1.UPDATE_TIME updateTime,
           t1.banner_pic bannerPic,t1.is_show isShow,t1.back_pic backPic,t1.logo,t1.order_id orderId,t1.special_ticket specialTicket,
           t1.general_ticket generalTicket,t1.invoice_titel invoiceTitel,t1.tax_number taxNumber,t1.phone,t1.weather_city_id weatherCityId,
           t1.member_channel memberChannel,t1.card_type cardType,t1.card_level cardLevel,t1.in_sms inSms,t1.apply_channel applyChannel,
           t1.map_coverage mapCoverage,t.api,t1.coupon,t.brand,t.URL_SHEME urlSheme
    from hs_pms_greencloud t
        LEFT JOIN mod_company t1 on t.company_id = t1.id
    <where>
      t.HOTELID is not null and t1.IS_SHOW ='1'
      <if test="companyId !=null">
        and t.company_id = #{companyId}
      </if>
      <if test="name !=null and name != ''">
        and t.name = #{name}
      </if>
      <if test="code !=null and code != ''">
        and t.code = #{code}
      </if>
    </where>
  </select>
</mapper>