package com.shands.mod.dao.model.newDataBoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("总经理指标详情 出参")
public class GmIndicatorsDetailsVo {
  //面对面会员发展完成率
  @ApiModelProperty("面对面会员当前完成值")
  private Integer memberpCurrentComplete;
  @ApiModelProperty("面对面会员目标值")
  private Integer memberTarget;
  @ApiModelProperty("面对面会员排名数量")
  private Integer memberRankNum;
  @ApiModelProperty("面对面会员当前排名")
  private Integer memberCurrentRank;
  @ApiModelProperty("面对面会员完成率")
  private Integer memberCompletion;
  //企业会员发展完成率
  @ApiModelProperty("企业会员当前完成值")
  private Integer enterpriseCurrentComplete;
  @ApiModelProperty("企业会员目标值")
  private Integer enterpriseTarget;
  @ApiModelProperty("企业会员排名数量")
  private Integer enterpriseRankNum;
  @ApiModelProperty("企业会员当前排名")
  private Integer enterpriseCurrentRank;
  @ApiModelProperty("企业会员完成率")
  private Integer enterpriseCompletion;
  //官渠入住间夜占比
  @ApiModelProperty("官渠入住间夜占比")
  private Integer nightsProportion;
  @ApiModelProperty("官渠入住间夜占比目标值")
  private Integer nightsTarget;
  @ApiModelProperty("官渠入住间夜占比排名数量")
  private Integer nightsRankNum;
  @ApiModelProperty("官渠入住间夜占比当前排名")
  private Integer nightsCurrentRank;
  //新增点评分
  @ApiModelProperty("新增点评平均分")
  private Integer reviewAverage;
  @ApiModelProperty("新增点评目标值")
  private Integer reviewTarget;
  @ApiModelProperty("新增点评排名数量")
  private Integer reviewRankNum;
  @ApiModelProperty("新增点评当前排名")
  private Integer reviewCurrentRank;
  //新增单房网评量占比
  @ApiModelProperty("新增单房网评量占比")
  private Integer singleRoomProportion;
  @ApiModelProperty("新增单房网评量目标值")
  private Integer singleRoomTarget;
  @ApiModelProperty("新增单房网评量排名数量")
  private Integer singleRoomRankNum;
  @ApiModelProperty("新增单房网评量当前排名")
  private Integer singleRoomCurrentRank;
  //营业收入
  @ApiModelProperty("营业收入当前完成值")
  private Integer operatingIncomeCurrentComplete;
  @ApiModelProperty("营业收入目标值")
  private Integer operatingIncomeTarget;
  @ApiModelProperty("营业收入排名数量")
  private Integer operatingIncomeRankNum;
  @ApiModelProperty("营业收入当前排名")
  private Integer operatingIncomeCurrentRank;
  @ApiModelProperty("营业收入完成率")
  private Integer operatingIncomeCompletion;
  //统采率
  @ApiModelProperty("统采率当前完成值")
  private Integer collectCurrentComplete;
  @ApiModelProperty("统采率目标值")
  private Integer collectTarget;
  @ApiModelProperty("统采率排名数量")
  private Integer collectRankNum;
  @ApiModelProperty("统采率当前排名")
  private Integer collectCurrentRank;
  @ApiModelProperty("统采率完成率")
  private Integer collectCompletion;
  //筹备完成情况
  @ApiModelProperty("筹备完成情况分数")
  private Integer preparationsScore;
  @ApiModelProperty("筹备完成目标值")
  private Integer preparationsTarget;
}
