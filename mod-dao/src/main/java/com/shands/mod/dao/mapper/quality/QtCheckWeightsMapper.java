package com.shands.mod.dao.mapper.quality;

import com.shands.mod.dao.model.quality.bo.AddCheckWeightsBo;
import com.shands.mod.dao.model.quality.po.QtCheckWeights;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 检查表维度权重配置信息表(QtCheckWeights)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-16 15:41:29
 */
public interface QtCheckWeightsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    QtCheckWeights queryById(Integer id);

    /**
     * 统计总行数
     *
     * @param qtCheckWeights 查询条件
     * @return 总行数
     */
    long count(QtCheckWeights qtCheckWeights);

    /**
     * 新增数据
     *
     * @param qtCheckWeights 实例对象
     * @return 影响行数
     */
    int insert(QtCheckWeights qtCheckWeights);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<QtCheckWeights> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<QtCheckWeights> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<QtCheckWeights> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<QtCheckWeights> entities);

    /**
     * 修改数据
     *
     * @param qtCheckWeights 实例对象
     * @return 影响行数
     */
    int update(QtCheckWeights qtCheckWeights);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    int deleteByCheckId(Integer checkId);

    List<AddCheckWeightsBo> selectDataByCheckId(Integer checkId);

}
