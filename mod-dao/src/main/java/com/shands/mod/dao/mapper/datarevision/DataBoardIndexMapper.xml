<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.datarevision.DataBoardIndexMapper">


  <select id="findHomeDataBoardByAds" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
SELECT
    biz_date as bizDate,
    DATE_FORMAT(biz_date,'%m%d') as formatDate,
    hotel_code as hotelCode,
    <if test="hotelType != null and hotelType == 'HOTEL'">
      IFNULL(SUM(total_amt),0) as totalAmt,
      IFNULL(SUM(room_amt),0) as roomAmt,
      IFNULL(SUM(catering_amt),0) as cateringAmt,
      IFNULL(SUM(other_amt),0) as otherAmt,
      IFNULL(SUM(mem_rooms_amt),0) as memberRoomRevenue,
      IFNULL(SUM(gw_room_revenue),0) as gwRoomRevenue,
    </if>
    <if test="hotelType != null and hotelType == 'BLOC'">
      IFNULL(CONVERT(SUM(total_amt)/10000,DECIMAL(12,2)),0) as totalAmt,
      IFNULL(CONVERT(SUM(room_amt)/10000,DECIMAL(12,2)),0) AS roomAmt,
      IFNULL(CONVERT(SUM(catering_amt)/10000,DECIMAL(12,2)),0) AS cateringAmt,
      IFNULL(CONVERT(SUM(other_amt)/10000,DECIMAL(12,2)),0) AS otherAmt,
      IFNULL(CONVERT(SUM(mem_rooms_amt)/10000,DECIMAL(12,2)),0) as memberRoomRevenue,
      IFNULL(CONVERT(SUM(gw_room_revenue)/10000,DECIMAL(12,2)),0) as gwRoomRevenue,
    </if>
    IFNULL(CONVERT((SUM(room_nights)/SUM(room_num)*100),DECIMAL(12,2)),0) as investOCC,
    IFNULL(CONVERT((SUM(room_nights)/SUM(sale_room_n)*100),DECIMAL(12,2)),0) as dayOCC,
    IFNULL(CONVERT((SUM(room_amt)/SUM(room_nights)),DECIMAL(12,2)),0) as dayADR,
    IFNULL(CONVERT((SUM(room_amt)/SUM(room_nights)),DECIMAL(12,2)),0) investARD,
    IFNULL(CONVERT((SUM(room_amt)/SUM(sale_room_n)),DECIMAL(12,2)),0) dayRevPAR,
    IFNULL(CONVERT((SUM(room_amt)/SUM(room_num)),DECIMAL(12,2)),0) investRevParMetrics,
    IFNULL(CONVERT((SUM(total_mem_f2f_n)/SUM(room_num)*100),DECIMAL(12,2)),0) faceToFaceRate,
    IFNULL(SUM(mem_develope_n),0) as memberF2F,
    IFNULL(SUM(ent_member_num),0) as entMemberNum,
    IFNULL(SUM(app_download_n),0) as appDownloadN,
    IFNULL(SUM(gift_sale),0) as giftSale,
    IFNULL(SUM(channel_web_n),0) as channelWebN,
    IFNULL(SUM(bdx_room),0) as bdxRoom,
    IFNULL(SUM(sq_room),0) as sqRoom,
    IFNULL(SUM(excitation_order_room),0) as excitationOrderRoom,
    IFNULL(SUM(excitation_other_order_room),0) as excitationOtherOrderRoom,
    IFNULL(SUM(hotel_active_emp_n),0) as hotelActiveEmpN,
    IFNULL(CONVERT((SUM(hotel_active_emp_n)/SUM(hotel_emp_n)*100),DECIMAL(12,2)),0) as activeRate,
    IFNULL(CONVERT((SUM(channel_web_n)/SUM(room_nights_n)*100),DECIMAL(12,2)),0) as roomNightLeaseRate,
    IFNULL(CONVERT((SUM(bdx_room)/SUM(room_nights)*100),DECIMAL(12,2)),0) as bdxRoomNightLeaseRate,
    IFNULL(CONVERT((SUM(bdx_room)/SUM(room_num)*100),DECIMAL(12,2)),0) as bdxRoomNightVolumeRate,
    IFNULL(CONVERT((SUM(channel_web_n)/SUM(room_num)*100),DECIMAL(12,2)),0) as roomNightVolumeRate,
    IFNULL(CONVERT((SUM(sq_room)/SUM(room_nights)*100),DECIMAL(12,2)),0) as sqRoomNightLeaseRate,
    IFNULL(CONVERT((SUM(sq_room)/SUM(room_num)*100),DECIMAL(12,2)),0) as sqRoomNightVolumeRate,
    IFNULL(CONVERT((SUM(excitation_other_order_room)/SUM(room_num)*100),DECIMAL(12,2)),0) as motivateOtherRate,
    IFNULL(SUM(total_mem_develope_n),0) as totalMemDevelopeN,
    IFNULL(SUM(repurchase_peo),0) as repurchasePeo,
    IFNULL(SUM(first_purchase_peo),0) as firstPurchasePeo,
    IFNULL(SUM(magic_weapon),0) as magicWeapon,
    IFNULL(SUM(magic_write_off),0) as magicWriteOff,
    IFNULL(SUM(mem_consume_n),0) as memConsumeAmt,
    IFNULL(SUM(mem_develope_n),0) as memDevelopeN,
    IFNULL(SUM(room_valid_num),0) as roomValidNum,
    IFNULL(SUM(enterprise_member),0) as enterpriseMember,
    IFNULL(SUM(produce_time_value),0) as produceTimeValue,
    IFNULL(SUM(consume_time_value),0) as consumeTimeValue,
    IFNULL(SUM(consumer_new_member),0) as consumerNewMember,
    IFNULL(SUM(ota_online_room_night),0) as otaOnlineRoomNight,
    IFNULL(SUM(member_first_consume_n),0) as consumerMember,
    IFNULL(CONVERT((SUM(ota_online_room_night)/(SUM(ota_online_room_night)+SUM(ota_offline_room_night))*100),DECIMAL(12,2)),0) as otaOnlineRate,
    IFNULL(CONVERT((SUM(ctrip_online_room_night)/(SUM(ctrip_online_room_night)+SUM(ctrip_offline_room_night))*100),DECIMAL(12,2)),0) as ctripOnlineRate,
    IFNULL(CONVERT((SUM(meituan_online_room_night)/(SUM(meituan_online_room_night)+SUM(meituan_offline_room_night))*100),DECIMAL(12,2)),0) as meituanOnlineRate,
    IFNULL(CONVERT((SUM(feizhu_online_room_night)/(SUM(feizhu_online_room_night)+SUM(feizhu_offline_room_night))*100),DECIMAL(12,2)),0) as feizhuOnlineRate,
    IFNULL(CONVERT((SUM(other_online_room_night)/(SUM(other_online_room_night)+SUM(other_offline_room_night))*100),DECIMAL(12,2)),0) as otherOnlineRate,
    IFNULL(CONVERT((SUM(mem_room_nights_n)/SUM(room_num)*100),DECIMAL(12, 2)), 0) as memberNightContributeRate

    from ads_app_trade_revenue_d where biz_date between #{startDate} and #{endDate}
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and biz_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and cooperation_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    <if test="hotelType == 'BLOC'">-->
<!--      and is_control = 1-->
<!--    </if>-->
    <if test="type != null and type == 'YES'">
      GROUP BY biz_date ORDER BY biz_date
    </if>

  </select>

  <select id="queryCommentDataDetailForDataBoard" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
    SELECT
    biz_date as bizDate,
    DATE_FORMAT(biz_date,'%m%d') as formatDate,
    hotel_code as hotelCode,
    IFNULL(CONVERT((SUM(cm_valid_score)/SUM(cm_valid_comment_n)),DECIMAL(12,2)),0) as currdayScoreN,
    IFNULL(SUM(comment_n),0) as currdayCommentN,
    IFNULL(SUM(opinion_n),0) as currdayOpinionN,
    IFNULL(SUM(comment_opinion_n),0) as currdayCommentOpinionN,
    Round(IFNULL(avg(bdx_page_score), 0), 2) 'bdx',
    Round(IFNULL(avg(ctrip_page_score), 0), 2) 'xieCheng',
    Round(IFNULL(avg(meituan_page_score), 0), 2) 'meiTuan'
    from ads_compete_hotel_reputation_d where biz_date between #{startDate} and #{endDate}
    and hotel_status = '在营'
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and management_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="type != null and type == 'YES'">
      GROUP BY biz_date ORDER BY biz_date
    </if>
  </select>

  <select id="queryCommentDataForDataBoard" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
    SELECT
    IFNULL(CONVERT((SUM(cm_valid_score)/SUM(cm_valid_comment_n)),DECIMAL(12,2)),0) as currdayScoreN,
    IFNULL(SUM(comment_n),0) as currdayCommentN,
    IFNULL(SUM(opinion_n),0) as currdayOpinionN,
    IFNULL(SUM(comment_opinion_n),0) as currdayCommentOpinionN,
    Round(IFNULL(avg(bdx_page_score), 0), 2) 'bdx',
    Round(IFNULL(avg(ctrip_page_score), 0), 2) 'xieCheng',
    Round(IFNULL(avg(meituan_page_score), 0), 2) 'meiTuan'
    from ads_compete_hotel_reputation_d where biz_date between #{startDate} and #{endDate}
    and hotel_status = '在营'
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and management_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="queryMemberDataDetailForDataBoard" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
    SELECT
    biz_date as bizDate,
    DATE_FORMAT(biz_date,'%m%d') as formatDate,
    hotel_code as hotelCode,
    IFNULL(MAX(total_mem_n),0) as totalMemDevelopeN,
    IFNULL(SUM(magic_write_off_n),0) as magicWriteOff,
    IFNULL(SUM(mem_new_consume_n),0) as consumerNewMember,
    IFNULL(SUM(mem_consume_n),0) as consumerMember,
    IFNULL(SUM(time_value_product),0) as produceTimeValue,
    IFNULL(SUM(time_value_consume),0) as consumeTimeValue,
    IFNULL(SUM(bdx_check_in_n),0) as bdxCheckInNum,
    IFNULL(SUM(bdx_check_in_mem_n),0) as memberCheckInAsCustomerNum,
    IFNULL(SUM(magic_mem_n),0) as checkInWithMatchedMagicMemberNum,
    IFNULL(SUM(magic_n),0) as checkInWithMatchedMagicNum,
    IFNULL(SUM(magic_user_n),0) as magicWeaponUsageMemberNum
    from ads_mem_rights_implement_d where biz_date between #{startDate} and #{endDate}
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and management_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="type != null and type == 'YES'">
      GROUP BY biz_date ORDER BY biz_date
    </if>
  </select>

  <select id="queryMemberData" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
    SELECT
    IFNULL(MAX(total_mem_n),0) as totalMemDevelopeN,
    IFNULL(SUM(magic_write_off_n),0) as magicWriteOff,
    IFNULL(SUM(mem_new_consume_n),0) as consumerNewMember,
    IFNULL(SUM(mem_consume_n),0) as consumerMember,
    IFNULL(SUM(time_value_product),0) as produceTimeValue,
    IFNULL(SUM(time_value_consume),0) as consumeTimeValue,
    IFNULL(SUM(bdx_check_in_n),0) as bdxCheckInNum,
    IFNULL(SUM(bdx_check_in_mem_n),0) as memberCheckInAsCustomerNum,
    IFNULL(SUM(magic_mem_n),0) as checkInWithMatchedMagicMemberNum,
    IFNULL(SUM(magic_n),0) as checkInWithMatchedMagicNum,
    IFNULL(SUM(magic_user_n),0) as magicWeaponUsageMemberNum
    from ads_mem_rights_implement_d where biz_date between #{startDate} and #{endDate}
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>


  <select id="findHomeDataBoardByrate" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
    SELECT
    biz_date as bizDate,
    DATE_FORMAT(biz_date,'%m%d') as formatDate,
    hotel_code as hotelCode,
    <if test="hotelType != null and hotelType == 'HOTEL'">
      IFNULL(CONVERT((SUM(material_amt)),DECIMAL(12,2)),0) as 'supplyChainMoney',
    </if>
    <if test="hotelType != null and hotelType == 'BLOC'">
      IFNULL(CONVERT((SUM(material_amt)/10000),DECIMAL(12,2)),0) as 'supplyChainMoney',
    </if>

      IFNULL(CONVERT(((SUM(stock_amt)+SUM(purchase_amt))/SUM(std_consume_amt) * 100) * IF(SUM(music_amt)>0,1,0)
                         * IF(SUM(fragrance_amt)>0,1,0),DECIMAL(12,2)),0) as 'uniformRecoveryRate'
    from ads_scm_uniform_purchase_rate_d where biz_date between #{startDate} and #{endDate}
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and hotel_brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and hotel_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and manage_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    <if test="hotelType == 'BLOC'">-->
<!--      and is_control = 1-->
<!--    </if>-->
    <if test="type != null and type == 'YES'">
      GROUP BY biz_date ORDER BY biz_date
    </if>
  </select>

  <select id="findHomeDataBoardByFinishRate" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo">
    SELECT
    month_date as bizDate,
    SUBSTRING(month_date,6) as formatDate,
    hotel_code as hotelCode,
    IFNULL(CONVERT((SUM(total_amt)/SUM(total_target)*100),DECIMAL(12,2)),0) as totalAmtFinishRate,
    IFNULL(CONVERT((SUM(room_amt)/SUM(room_target)*100),DECIMAL(12,2)),0) as roomAmtFinishRate,
    IFNULL(CONVERT((SUM(catering_amt)/SUM(catering_target)*100),DECIMAL(12,2)),0) as cateringAmtFinishRate
    from dws_fina_budget_finish_m where month_date between #{startDate} and #{endDate}
    <if test="codes != null and codes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and biz_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and cooperation_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    <if test="hotelType == 'BLOC'">-->
<!--      and is_control = 1-->
<!--    </if>-->
    <if test="type != null and type == 'YES'">
      GROUP BY month_date ORDER BY month_date
    </if>
  </select>



  <select id="findHomeDataBlocBoardByrateV2" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    hotel_department_code as departmentCode,
    hotel_name as hotelName,
    hotel_department as departmentName,
    biz_date as bizDate,
    <if test="codeType == 'hotelSupplyChainMoney' or codeType == 'blocSupplyChainMoney' ">
      IFNULL(CONVERT((SUM(material_amt)/10000),DECIMAL(12,2)),0) as 'value',
    </if>
    <if test="codeType == 'hotelUniformRecoveryRate' or codeType == 'blocUniformRecoveryRate' ">
      IFNULL(CONVERT(((SUM(stock_amt)+SUM(purchase_amt))/SUM(std_consume_amt) * 100) * IF(SUM(music_amt)>0,1,0)
      * IF(SUM(fragrance_amt)>0,1,0),DECIMAL(12,2)),0) as 'value',
    </if>
    DATE_FORMAT(biz_date,'%m%d') as formatDate

    from ads_scm_uniform_purchase_rate_d where biz_date between #{startDate} and #{endDate}
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and hotel_brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and hotel_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and manage_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <!--     and is_control = 1 -->
    <if test="groupType != null and groupType == 'HOTEL'">
      GROUP BY hotel_code order by 'value' desc
    </if>
    <if test="groupType != null and groupType == 'DIVISION'">
      GROUP BY hotel_department_code
    </if>
  </select>

  <select id="findHomeDataBlocBoardByAdsV2" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    biz_department_code as departmentCode,
    hotel_name as hotelName,
    hotel_biz_department as departmentName,
    <if test="codeType == 'blocGrossRevenue' or codeType == 'hotelGrossRevenue' ">
      IFNULL(CONVERT(SUM(total_amt)/10000,DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocRoomRevenue' or codeType == 'hotelRoomRevenue' ">
      IFNULL(CONVERT(SUM(room_amt)/10000,DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocCateringRevenue' or codeType == 'hotelCateringRevenue' ">
      IFNULL(CONVERT(SUM(catering_amt)/10000,DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocOtherRevenue' or codeType == 'hotelOtherRevenue' ">
      IFNULL(CONVERT(SUM(other_amt)/10000,DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocInvestOCC' or codeType == 'hotelInvestOCC' ">
      IFNULL(CONVERT((SUM(room_nights_n)/SUM(room_num)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocOperationOCC' or codeType == 'hotelOperationOCC' ">
      IFNULL(CONVERT((SUM(room_nights)/SUM(sale_room_n)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocOperationADR' or codeType == 'hotelOperationADR' ">
      IFNULL(CONVERT((SUM(comprehen_amt)/SUM(room_nights_n)),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocInvestADR' or codeType == 'hotelInvestADR' ">
      IFNULL(CONVERT((SUM(room_amt)/SUM(room_nights_n)),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocOperationRevPAR' or codeType == 'hotelOperationRevPAR' ">
      IFNULL(CONVERT((SUM(comprehen_amt)/SUM(sale_room_n)),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocInvestRevPAR' or codeType == 'hotelInvestRevPAR' ">
      IFNULL(CONVERT((SUM(room_amt)/SUM(room_num)),DECIMAL(12,2)),0) as 'value'
    </if>
<!--    <if test="codeType == 'blocFaceToFaceRate' or codeType == 'hotelFaceToFaceRate' ">-->
<!--      IFNULL(CONVERT((SUM(total_mem_f2f_n)/SUM(room_num) * 100),DECIMAL(12,2)),0) as 'value'-->
<!--    </if>-->
    <if test="codeType == 'blocFaceToFace' or codeType == 'hotelFaceToFace' ">
      IFNULL(SUM(total_mem_f2f_n),0) as 'value'
    </if>
    <if test="codeType == 'blocMemberNightContributeRate' or codeType == 'hotelMemberNightContributeRate'">
        IFNULL(CONVERT((SUM(mem_room_nights_n)/SUM(room_num)), DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocMemberRoomRevenue'">
        IFNULL(CONVERT(SUM(mem_rooms_amt)/10000,DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'hotelMemberRoomRevenue'">
      IFNULL(SUM(mem_rooms_amt),0) as 'value'
    </if>
    <if test="codeType == 'blocCorporateMember' or codeType == 'hotelCorporateMember' ">
      IFNULL(SUM(ent_member_num),0) as 'value'
    </if>
    <if test="codeType == 'blocAppDownloads' or codeType == 'hotelAppDownloads' ">
      IFNULL(SUM(app_download_n),0) as 'value'
    </if>
    <if test="codeType == 'blocGiftBagSales' or codeType == 'hotelGiftBagSales' ">
      IFNULL(SUM(gift_sale),0) as 'value'
    </if>
    <if test="codeType == 'blocRoomNightNum' or codeType == 'hotelRoomNightNum' ">
      IFNULL(SUM(channel_web_n),0) as 'value'
     </if>
    <if test="codeType == 'blocBdxRoomNightNum' or codeType == 'hotelBdxRoomNightNum' ">
      IFNULL(SUM(bdx_room),0) as 'value'
    </if>
    <if test="codeType == 'blocSqRoomNightNum' or codeType == 'hotelSqRoomNightNum' ">
      IFNULL(SUM(sq_room),0) as 'value'
    </if>
    <if test="codeType == 'blocIncentiveOrder' or codeType == 'hotelIncentiveOrder' ">
      IFNULL(SUM(excitation_order_room),0) as 'value'
    </if>
    <if test="codeType == 'blocMotivateOther' or codeType == 'hotelMotivateOther' ">
      IFNULL(SUM(excitation_other_order_room),0) as 'value'
    </if>
    <if test="codeType == 'blocActivate' or codeType == 'hotelActivate' ">
      IFNULL(SUM(hotel_active_emp_n),0) as 'value'
    </if>
    <if test="codeType == 'blocActivationRate' or codeType == 'hotelActivationRate' ">
      IFNULL(CONVERT((SUM(hotel_active_emp_n)/SUM(hotel_emp_n)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocRoomNightLeaseRate' or codeType == 'hotelRoomNightLeaseRate' ">
      IFNULL(CONVERT((SUM(channel_web_n)/SUM(room_nights_n)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocBdxRoomNightLeaseRate' or codeType == 'hotelBdxRoomNightLeaseRate' ">
      IFNULL(CONVERT((SUM(bdx_room)/SUM(room_nights)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocBdxRoomNightVolumeRate' or codeType == 'hotelBdxRoomNightVolumeRate' ">
      IFNULL(CONVERT((SUM(bdx_room)/SUM(room_num)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocRoomNightVolumeRate' or codeType == 'hotelRoomNightVolumeRate' ">
      IFNULL(CONVERT((SUM(channel_web_n)/SUM(room_num)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocSqRoomNightLeaseRate' or codeType == 'hotelSqRoomNightLeaseRate' ">
      IFNULL(CONVERT((SUM(sq_room)/SUM(room_nights)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocSqRoomNightVolumeRate' or codeType == 'hotelSqRoomNightVolumeRate' ">
      IFNULL(CONVERT((SUM(sq_room)/SUM(room_num)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocMotivateOtherRate' or codeType == 'hotelMotivateOtherRate' ">
      IFNULL(CONVERT((SUM(excitation_other_order_room)/SUM(room_num)*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocMemberScale' or codeType == 'hotelMemberScale' ">
      IFNULL(SUM(total_mem_develope_n),0) as 'value'
    </if>
    <if test="codeType == 'roomValidNum' ">
      IFNULL(SUM(room_valid_num),0) as 'value'
    </if>

    <if test="codeType == 'blocGWRoomRevenue' or codeType == 'hotelGWRoomRevenue' ">
      IFNULL(SUM(gw_room_revenue),0) as 'value'
    </if>
    <if test="codeType == 'hotelCorporateMemberNum' ">
      IFNULL(SUM(enterprise_member),0) as 'value'
    </if>
    <if test="codeType == 'blocConsumerMemberAddScale' or codeType == 'hotelConsumerMemberAddScale' ">
      IFNULL(SUM(consumer_new_member),0) as 'value'
    </if>
    <if test="codeType == 'blocConsumerMemberScale' or codeType == 'hotelConsumerMemberScale' ">
      IFNULL(SUM(consumer_member),0) as 'value'
    </if>
    <if test="codeType == 'blocProduceTimeValue' or codeType == 'hotelProduceTimeValue' ">
      IFNULL(SUM(produce_time_value),0) as 'value'
    </if>
    <if test="codeType == 'blocConsumeTimeValue' or codeType == 'hotelConsumeTimeValue' ">
      IFNULL(SUM(consume_time_value),0) as 'value'
    </if>
    <if test="codeType == 'blocOTAOnlineRoomNightNum' or codeType == 'hotelOTAOnlineRoomNightNum' ">
      IFNULL(SUM(ota_online_room_night),0) as 'value'
    </if>
    <if test="codeType == 'blocOTAOnlineRate' or codeType == 'hotelOTAOnlineRate' ">
      IFNULL(CONVERT((SUM(ota_online_room_night)/(SUM(ota_online_room_night)+SUM(ota_offline_room_night))*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocCtripOnlineRate' or codeType == 'hotelCtripOnlineRate' ">
      IFNULL(CONVERT((SUM(ctrip_online_room_night)/(SUM(ctrip_online_room_night)+SUM(ctrip_offline_room_night))*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocMeituanOnlineRate' or codeType == 'hotelMeituanOnlineRate' ">
      IFNULL(CONVERT((SUM(meituan_online_room_night)/(SUM(meituan_online_room_night)+SUM(meituan_offline_room_night))*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocTaobaoOnlineRate' or codeType == 'blocTaobaoOnlineRate' ">
      IFNULL(CONVERT((SUM(feizhu_online_room_night)/(SUM(feizhu_online_room_night)+SUM(feizhu_offline_room_night))*100),DECIMAL(12,2)),0) as 'value'
    </if>
    <if test="codeType == 'blocOtherOTAOnlineRate' or codeType == 'hotelOtherOTAOnlineRate' ">
      IFNULL(CONVERT((SUM(other_online_room_night)/(SUM(other_online_room_night)+SUM(other_offline_room_night))*100),DECIMAL(12,2)),0) as 'value'
    </if>
    from ads_app_trade_revenue_d where biz_date between #{startDate} and #{endDate}
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and biz_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and cooperation_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <!--     and is_control = 1 -->
    <if test="groupType != null and groupType == 'HOTEL'">
      GROUP BY hotel_code order by 'value' desc
    </if>
    <if test="groupType != null and groupType == 'DIVISION'">
      GROUP BY biz_department_code
    </if>
  </select>

  <select id="queryDataBoardMemberData" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    hotel_name as hotelName,
    department_code as departmentCode,
    department_name as departmentName,
    <if test="codeType == 'blocMemberScale' or codeType == 'hotelBdxCheckInNum' ">
      IFNULL(MAX(total_mem_n),0) as 'value'
    </if>
    <if test="codeType == 'blocMagicWriteOff' or codeType == 'hotelMemberCheckInAsCustomerNum' ">
      IFNULL(SUM(magic_write_off_n),0) as 'value'
    </if>
    <if test="codeType == 'blocConsumerMemberAddScale' or codeType == 'hotelCheckInWithMatchedMagicMemberNum' ">
      IFNULL(SUM(mem_new_consume_n),0) as 'value'
    </if>
    <if test="codeType == 'blocConsumerMemberScale' or codeType == 'hotelCheckInWithMatchedMagicNum' ">
      IFNULL(SUM(mem_consume_n),0) as 'value'
    </if>
    <if test="codeType == 'blocProduceTimeValue' or codeType == 'hotelMagicWeaponUsageMemberNum' ">
      IFNULL(SUM(time_value_product),0) as 'value'
    </if>
    <if test="codeType == 'blocConsumeTimeValue' or codeType == 'hotelGrossRevenue' ">
      IFNULL(SUM(time_value_consume),0) as 'value'
    </if>
    <if test="codeType == 'blocBdxCheckInNum' or codeType == 'hotelRoomRevenue' ">
      IFNULL(SUM(bdx_check_in_n),0) as 'value'
    </if>
    <if test="codeType == 'blocMemberCheckInAsCustomerNum' or codeType == 'hotelCateringRevenue' ">
      IFNULL(SUM(bdx_check_in_mem_n),0) as 'value'
    </if>
    <if test="codeType == 'blocCheckInWithMatchedMagicMemberNum' or codeType == 'hotelOtherRevenue' ">
      IFNULL(SUM(magic_mem_n),0) as 'value'
    </if>
    <if test="codeType == 'blocCheckInWithMatchedMagicNum' or codeType == 'hotelInvestOCC' ">
      IFNULL(SUM(magic_n),0) as 'value'
    </if>
    <if test="codeType == 'blocMagicWeaponUsageMemberNum' or codeType == 'hotelOperationOCC' ">
      IFNULL(SUM(magic_user_n),0) as 'value'
    </if>
    from ads_mem_rights_implement_d where biz_date between #{startDate} and #{endDate}
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and cooperation_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <if test="groupType != null and groupType == 'HOTEL'">
      GROUP BY hotel_code order by 'value' desc
    </if>
    <if test="groupType != null and groupType == 'DIVISION'">
      GROUP BY department_code
    </if>
  </select>

  <select id="findHomeDataBlocBoardByCommentV2" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    biz_department_code as departmentCode,
    hotel_name as hotelName,
    hotel_biz_department as departmentName,
    biz_date as bizDate,
    <if test="codeType == 'blocAdditionalCommentAverageScore' or codeType == 'hotelAdditionalCommentAverageScore' ">
      IFNULL(CONVERT((SUM(currday_score_n)/SUM(currday_comment_n)),DECIMAL(12,2)),0) as 'value',
    </if>
    <if test="codeType == 'blocAdditionalCommentVolume' or codeType == 'hotelAdditionalCommentVolume' ">
      IFNULL(SUM(currday_valid_comment_n),0) as 'value',
    </if>
    <if test="codeType == 'blocBdxPagePoints' or codeType == 'hotelBdxPagePoints' ">
      Round(IFNULL(avg(CASE WHEN ota_type = '百达星系' THEN total_sorce end), 0), 2) as 'value',
    </if>
    <if test="codeType == 'blocXcPagePoints' or codeType == 'hotelXcPagePoints' ">
      Round(IFNULL(avg(CASE WHEN ota_type = '携程' THEN total_sorce end), 0),2)  as 'value',
    </if>
    <if test="codeType == 'blocMtPagePoints' or codeType == 'hotelMtPagePoints' ">
      Round(IFNULL(avg(CASE WHEN ota_type = '美团' THEN total_sorce end), 0),2) as  'value',
    </if>
    <if test="codeType == 'blocSqPagePoints' or codeType == 'hotelSqPagePoints' ">
      Round(IFNULL(avg(CASE WHEN ota_type = '官网' THEN total_sorce end), 0), 2) as 'value',
    </if>
    DATE_FORMAT(biz_date,'%m%d') as formatDate
    from mod_comment where biz_date between #{startDate} and #{endDate}
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and biz_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and management_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <!--     and is_control = 1 -->
    <if test="groupType != null and groupType == 'HOTEL'">
      GROUP BY hotel_code order by 'value' desc
    </if>
    <if test="groupType != null and groupType == 'DIVISION'">
      GROUP BY biz_department_code
    </if>
  </select>

  <select id="queryDataBoardMemberDetail" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    hotel_name as hotelName,
    department_code as departmentCode,
    department_name as departmentName,
    biz_date as bizDate,
    <if test="codeType == 'blocAdditionalCommentAverageScore' or codeType == 'hotelAdditionalCommentAverageScore' ">
      IFNULL(CONVERT((SUM(cm_valid_score)/SUM(cm_valid_comment_n)),DECIMAL(12,2)),0) as 'value',
    </if>
    <if test="codeType == 'blocNewCommentOpinionNum' or codeType == 'hotelNewCommentOpinionNum' ">
      IFNULL(SUM(comment_opinion_n),0) as 'value',
    </if>
    <if test="codeType == 'blocAdditionalCommentVolume' or codeType == 'hotelAdditionalCommentVolume' ">
      IFNULL(SUM(comment_n),0) as 'value',
    </if>
    <if test="codeType == 'blocNewOpinionNum' or codeType == 'hotelNewOpinionNum' ">
    IFNULL(SUM(opinion_n),0) as 'value',
    </if>
    <if test="codeType == 'blocBdxPagePoints' or codeType == 'hotelBdxPagePoints' ">
      Round(IFNULL(avg(bdx_page_score), 0), 2) as 'value',
    </if>
    <if test="codeType == 'blocXcPagePoints' or codeType == 'hotelXcPagePoints' ">
      Round(IFNULL(avg(ctrip_page_score), 0),2)  as 'value',
    </if>
    <if test="codeType == 'blocMtPagePoints' or codeType == 'hotelMtPagePoints' ">
      Round(IFNULL(avg(meituan_page_score), 0),2) as  'value',
    </if>
    DATE_FORMAT(biz_date,'%m%d') as formatDate
    from ads_compete_hotel_reputation_d where biz_date between #{startDate} and #{endDate}
    and hotel_status = '在营'
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and management_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <!--     and is_control = 1 -->
    <if test="groupType != null and groupType == 'HOTEL'">
      GROUP BY hotel_code order by 'value' desc
    </if>
    <if test="groupType != null and groupType == 'DIVISION'">
      GROUP BY department_name
    </if>
  </select>

  <select id="findHomeDataBlocBoardByFinishRateV2" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    biz_department_code as departmentCode,
    hotel_name as hotelName,
    hotel_biz_department as departmentName,
    month_date as bizDate,

    <if test="codeType == 'blocGrossRevenueBudget' or codeType == 'hotelGrossRevenueBudget' ">
      IFNULL(CONVERT((SUM(total_amt)/SUM(total_target)*100),DECIMAL(12,2)),0) as 'value',
    </if>
    <if test="codeType == 'blocRoomRevenueBudget' or codeType == 'hotelRoomRevenueBudget' ">
      IFNULL(CONVERT((SUM(room_amt)/SUM(room_target)*100),DECIMAL(12,2)),0) as 'value',
    </if>
    <if test="codeType == 'blocCateringRevenueBudget' or codeType == 'hotelCateringRevenueBudget' ">
      IFNULL(CONVERT((SUM(catering_amt)/SUM(catering_target)*100),DECIMAL(12,2)),0) as 'value',
    </if>
    SUBSTRING(month_date,6) as formatDate
    from dws_fina_budget_finish_m where month_date between #{startDate} and #{endDate}
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and biz_department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and cooperation_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <!--     and is_control = 1 -->
    <if test="groupType != null and groupType == 'HOTEL'">
      GROUP BY hotel_code order by 'value' desc
    </if>
    <if test="groupType != null and groupType == 'DIVISION'">
      GROUP BY biz_department_code
    </if>
  </select>


  <select id="findConsumerMemberV2" resultType="com.shands.mod.dao.model.newDataBoard.vo.DataInfoVo">
    SELECT
    hotel_code as hotelCode,
    department_code as departmentCode,
    hotel_name as hotelName,
    department_name as departmentName,
    IFNULL(SUM(consumer_member),0) as 'value',
    IFNULL(SUM(contrast_consumer_member),0) as 'contrastValue'

    from ads_mem_rights_implement_d where time_type =#{timeType}
      and start_time &gt;= #{startDate} and end_time &lt;= #{endDate}
    <if test="hotelCodes != null and hotelCodes.size > 0">
      and hotel_code in
      <foreach item="item" index="index" collection="hotelCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="brandCodes != null and brandCodes.size > 0">
      and brand_code in
      <foreach item="item" index="index" collection="brandCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="divisionCodes != null and divisionCodes.size > 0">
      and department_code in
      <foreach item="item" index="index" collection="divisionCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="managementCodes != null and managementCodes.size > 0">
      and manage_code in
      <foreach item="item" index="index" collection="managementCodes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and hotel_code != '000001'
    <!--     and is_control = 1 -->
      GROUP BY hotel_code order by 'value' desc
  </select>

  <select id="queryBlocTotalAmt" resultType="BigDecimal">
    SELECT IFNULL(SUM(total_amt),0) AS totalAmt FROM ads_app_trade_revenue_d
    <where>
      <if test="startDate != null and startDate != ''">
        AND #{startDate} &lt;= biz_date
      </if>
      <if test="endDate != null and endDate != ''">
        AND biz_date &lt;= #{endDate}
      </if>
    </where>
  </select>

  <select id="queryBlocMemberF2F" resultType="Long">
    SELECT IFNULL(SUM(total_mem_f2f_n),0) AS totalAmt FROM ads_app_trade_revenue_d
    <where>
      <if test="startDate != null and startDate != ''">
        AND #{startDate} &lt;= biz_date
      </if>
      <if test="endDate != null and endDate != ''">
        AND biz_date &lt;= #{endDate}
      </if>
    </where>
  </select>

  <select id="queryMemContributeRateOfMonth" resultType="BigDecimal">
    SELECT IFNULL(CONVERT((SUM(mem_room_nights_n)/SUM(room_num)*100),DECIMAL(12, 2)), 0) as memberNightContributeRate
    FROM ads_app_trade_revenue_d WHERE biz_date BETWEEN #{startDate} AND #{endDate}
    GROUP BY DATE_FORMAT(biz_date,'%m') ORDER BY DATE_FORMAT(biz_date,'%m')
  </select>

  <select id="queryRoomNight" resultType="com.shands.mod.dao.model.newDataBoard.po.RoomNightItem">
    SELECT
        IFNULL(SUM(room_nights_n),0) as roomNight,
        IFNULL(SUM(channel_web_n),0) as channelWebRoomNight,
        IFNULL(SUM(ota_offline_room_night + ota_online_room_night),0) as otaRoomNight,
        IFNULL(SUM(room_nights_n),0) - IFNULL(SUM(ota_offline_room_night + ota_online_room_night),0) - IFNULL(SUM(channel_web_n),0) as offlineRoomNight
    FROM ads_app_trade_revenue_d
    <where>
      <if test="startDate != null and startDate != ''">
        AND #{startDate} &lt;= biz_date
      </if>
      <if test="endDate != null and endDate != ''">
        AND biz_date &lt;= #{endDate}
      </if>
    </where>
  </select>

  <select id="queryCityNum" resultType="String">
    SELECT DISTINCT city FROM ads_app_trade_revenue_d
    WHERE hotel_status !='QUIT' AND biz_date = #{bizDate}
  </select>

  <select id="findMaxDate" resultType="Date">
    SELECT MAX(biz_date) FROM ads_app_trade_revenue_d
  </select>
</mapper>