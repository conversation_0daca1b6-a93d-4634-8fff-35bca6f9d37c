package com.shands.mod.dao.model.fw.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShipKindEnum {
  SHIP(1, "客船"),
  CAR(2, "摆渡车"),
  ;

  private int code;

  private String type;

  public static ShipKindEnum intToEnum(int i) {
    switch (i) {
      case 1:
        return SHIP;
      case 2:
        return CAR;
    }
    return null;
  }

  public static ShipKindEnum typeToEnum(String type) {
    switch (type) {
      case "客船":
        return SHIP;
      case "摆渡车":
        return CAR;
    }
    return null;
  }
}
