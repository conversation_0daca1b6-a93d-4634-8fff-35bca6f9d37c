package com.shands.mod.dao.mapper.workorder;

import com.shands.mod.dao.model.workorder.po.NwDictInfo;
import java.util.List;
import com.shands.mod.dao.model.workorder.vo.NwDictInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * 工单配置字段详情表(NwDictInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-05 16:07:38
 */
public interface NwDictInfoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NwDictInfo queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param nwDictInfo 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<NwDictInfo> queryAllByLimit(NwDictInfo nwDictInfo);

    /**
     * 统计总行数
     *
     * @param nwDictInfo 查询条件
     * @return 总行数
     */
    long count(NwDictInfo nwDictInfo);

    /**
     * 新增数据
     *
     * @param nwDictInfo 实例对象
     * @return 影响行数
     */
    int insert(NwDictInfo nwDictInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<NwDictInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<NwDictInfo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<NwDictInfo> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<NwDictInfo> entities);

    /**
     * 修改数据
     *
     * @param nwDictInfo 实例对象
     * @return 影响行数
     */
    int update(NwDictInfo nwDictInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    int deleteByDictId(@Param("dictId") Integer dictId);

    List<NwDictInfoVo> selectByDictId(@Param("dictId") Integer dictId);
}

