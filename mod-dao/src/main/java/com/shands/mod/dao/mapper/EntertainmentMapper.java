package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.Entertainment;
import com.shands.mod.dao.model.EntertainmentExample;
import com.shands.mod.dao.model.EntertainmentWithBLOBs;
import com.shands.mod.dao.model.res.hs.atlas.ContentRes;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EntertainmentMapper {

  int countByExample(EntertainmentExample example);

  int deleteByExample(EntertainmentExample example);

  int deleteByPrimaryKey(Integer id);

  int insert(EntertainmentWithBLOBs record);

  int insertSelective(EntertainmentWithBLOBs record);

  List<EntertainmentWithBLOBs> selectByExampleWithBLOBs(EntertainmentExample example);

  List<Entertainment> selectByExample(EntertainmentExample example);

  EntertainmentWithBLOBs selectByPrimaryKey(Integer id);

  int updateByExampleSelective(@Param("record") EntertainmentWithBLOBs record,
      @Param("example") EntertainmentExample example);

  int updateByExampleWithBLOBs(@Param("record") EntertainmentWithBLOBs record,
      @Param("example") EntertainmentExample example);

  int updateByExample(@Param("record") Entertainment record,
      @Param("example") EntertainmentExample example);

  int updateByPrimaryKeySelective(EntertainmentWithBLOBs record);

  int updateByPrimaryKeyWithBLOBs(EntertainmentWithBLOBs record);

  int updateByPrimaryKey(Entertainment record);

  int insertBatch(List<EntertainmentWithBLOBs> list);

  int deleteByCompanyId(@Param("companyId") Integer companyId);

  List<ContentRes> getByLabel(@Param("companyId") Integer companyId);

  List<String> getNameById(List<String> id);
}