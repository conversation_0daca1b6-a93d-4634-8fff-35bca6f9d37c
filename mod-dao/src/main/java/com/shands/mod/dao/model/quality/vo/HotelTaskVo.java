package com.shands.mod.dao.model.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/6/21
 **/
@Data
@ApiModel("查询符合条件的酒店任务")
public class HotelTaskVo {

  @ApiModelProperty("检查表id")
  private Integer checkId;

  @ApiModelProperty("任务表id")
  private Integer taskId;

  @ApiModelProperty("任务表状态")
  private String taskFinishStatus;
}