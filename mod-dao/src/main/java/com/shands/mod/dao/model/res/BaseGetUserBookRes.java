package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class BaseGetUserBookRes {

  @ApiModelProperty("id")
  private Integer id;
  /** 名字 */
  @ApiModelProperty("名字")
  private String name;
  /** 手机号 */
  @ApiModelProperty("手机号")
  private String number;
  /** 0:无特殊,1:空闲,2:忙碌 */
  @ApiModelProperty("0:无特殊,1:空闲,2:忙碌")
  private Integer status;
  /** 部门id */
  @ApiModelProperty("部门id")
  private Integer deptId;
  /** 是否在线 */
  @ApiModelProperty("是否在线")
  private boolean online;
}
