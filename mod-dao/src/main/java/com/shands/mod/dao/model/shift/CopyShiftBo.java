package com.shands.mod.dao.model.shift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/7/18 10:06
 */
@Data
public class CopyShiftBo {

  @ApiModelProperty(value = "来源开始日期")
  @NotNull(message = "fromStartDate不能为空")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date fromStartDate;

  @ApiModelProperty(value = "来源结束日期")
  @NotNull(message = "fromEndDate不能为空")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date fromEndDate;

  @ApiModelProperty(value = "粘贴开始日期")
  @NotNull(message = "toStartDate不能为空")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date toStartDate;

  @ApiModelProperty(value = "粘贴结束日期")
  @NotNull(message = "toEndDate不能为空")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date toEndDate;

  @ApiModelProperty(value = "服务类型，CLEAN-保洁、DELIVER-送物、REPAIR-维修")
  @NotNull(message = "serviceType不能为空")
  private String serviceType;
}
