package com.shands.mod.dao.model.res.hs.hotel;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class FoodConfigByServiceIdRes {

  /**
   * 开始时间
   */
  private String startTime;
  /**
   * 结束时间
   */
  private String endTime;

  /**
   * 分类id
   */
  private Integer foodConfigId;
  /**
   * 送餐内容
   */
  private String roomContent;
  /**
   * 餐厅id
   */
  private Integer restaurantId;
  /**
   * 准备时间
   */
  private Integer readyTime;
  /**
   * 部门id
   */
  private Integer departmentId;
  /**
   * 餐厅名称
   */
  private String restaurantName;
  /**
   * 部门名称
   */
  private String departmentName;
  /**
   * 排序
   */
  private Integer num;

  //服务时间 时间段格式
  private String serviceTime;

  private List<ServiceTimeRes> serviceTimes;

  @ApiModelProperty("菜本code")
  private String noteCode;

  @ApiModelProperty("菜本名称")
  private String noteName;

  @ApiModelProperty("营业点Id")
  private String outletId;

  @ApiModelProperty("营业点code")
  private String outletCode;

  @ApiModelProperty("营业点名称")
  private String outletName;

  @ApiModelProperty("服务费 %")
  private BigDecimal 	serviceCharge;

  @ApiModelProperty("会员折扣是否开启  1开启 0关闭")
  private Integer memberDiscount;
  /**
   * user_id
   */
  private String userId;

  /**
   * copy_user_id
   */
  private String copyUserId;

  private String userName;
}
