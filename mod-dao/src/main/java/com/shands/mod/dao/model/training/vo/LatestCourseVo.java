package com.shands.mod.dao.model.training.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel("最新课程 返参")
public class LatestCourseVo {

  @ApiModelProperty("课程名称")
  private String name;

  @ApiModelProperty("课程类型（视频，pdf，图文）")
  private String type;

  @ApiModelProperty("文件（视频，pdf，图文）")
  private String fileUrl;

  @ApiModelProperty("封面")
  private String imageUrl;

  @ApiModelProperty("学习数量")
  private Integer learningNum;

  @ApiModelProperty("角色code")
  private String roleCode;

  @ApiModelProperty("合集或单课的id")
  private Integer tempId;

  @ApiModelProperty("课程区分 单课和合集")
  private String  courseDistinguish;

  @ApiModelProperty("是否对所有员工全部可见")
  private Integer ifStaff;

  @ApiModelProperty("收藏时间")
  private Date createTime;

  @ApiModelProperty("标签名称")
  private String labelName;

  @ApiModelProperty("类型（集团或酒店）")
  private String configType;

  @ApiModelProperty("品牌code")
  private String brandCode;

  @ApiModelProperty("事业部code")
  private String divisionCode;

  @ApiModelProperty("酒店ids")
  private String hotelIds;
}
