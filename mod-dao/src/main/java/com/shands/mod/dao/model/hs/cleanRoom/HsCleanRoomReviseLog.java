package com.shands.mod.dao.model.hs.cleanRoom;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 工作量调整操作日志(HsCleanRoomReviseLog)实体类
 *
 * <AUTHOR>
 * @since 2021-07-05 16:19:37
 */
@Data
public class HsCleanRoomReviseLog implements Serializable {
    private static final long serialVersionUID = -11899633272535483L;
    /**
    * 表主键
    */
    private Integer id;
    /**
    * 工作量调整表主键（hs_clean_room_revise）
    */
    private String reviseId;
    /**
    * 操作类型
    */
    private String operateType;
    /**
    * 操作内容
    */
    private String operateContent;
    /**
    * 手机号
    */
    private String mobile;
    /**
    * 操作时间
    */
    private Date createTime;
    /**
    * 操作人
    */
    private Integer createUser;
    /**
    * 酒店id
    */
    private Integer companyId;
    /**
    * 删除标记
    */
    private Integer deleted;

    /**
     * 操作人姓名
     */
    private String operateUser;
}