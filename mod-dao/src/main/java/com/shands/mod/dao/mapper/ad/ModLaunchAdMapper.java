package com.shands.mod.dao.mapper.ad;

import com.shands.mod.dao.model.ad.ModLaunchAd;
import com.shands.mod.dao.model.ad.ModLaunchAdExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface ModLaunchAdMapper {
    long countByExample(ModLaunchAdExample example);

    int deleteByExample(ModLaunchAdExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ModLaunchAd record);

    int insertSelective(ModLaunchAd record);

    List<ModLaunchAd> selectByExampleWithRowbounds(ModLaunchAdExample example, RowBounds rowBounds);

    List<ModLaunchAd> selectByExample(ModLaunchAdExample example);

    ModLaunchAd selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ModLaunchAd record, @Param("example") ModLaunchAdExample example);

    int updateByExample(@Param("record") ModLaunchAd record, @Param("example") ModLaunchAdExample example);

    int updateByPrimaryKeySelective(ModLaunchAd record);

    int updateByPrimaryKey(ModLaunchAd record);

    void batchInsert(@Param("items") List<ModLaunchAd> items);
}