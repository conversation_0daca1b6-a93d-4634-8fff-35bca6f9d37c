package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.HsHotelServiceExtend;
import com.shands.mod.dao.model.hs.HsHotelServiceExtendRes;
import com.shands.mod.dao.model.res.hs.hotel.CleanAndRepairForWorkRes;
import com.shands.mod.dao.model.res.hs.hotel.ServiceContentRes;
import com.shands.mod.dao.model.res.hs.staff.ExtendRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HsHotelServiceExtendMapper {
  int deleteByPrimaryKey(Integer id);

  int insert(HsHotelServiceExtend record);
  int insertBatch(@Param("entities") List<HsHotelServiceExtend> records);

  int insertSelective(HsHotelServiceExtend record);

  HsHotelServiceExtend selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HsHotelServiceExtend record);

  int updateByPrimaryKey(HsHotelServiceExtend record);

  int updateDelete(@Param("hotelServiceExtendId") Integer hotelServiceExtendId);

  List<HsHotelServiceExtend> findByHotelServiceId(@Param("hotelServiceId") Integer hotelServiceId);

  List<HsHotelServiceExtend> findByHotelServiceIdAndCompanyId(@Param("hotelServiceId") Integer hotelServiceId,@Param("companyId") Integer companyId,@Param("status") Integer status);

  List<HsHotelServiceExtend> findByIds(@Param("hotelServiceExtendId") String hotelServiceExtendId);

  int deletedByCompanyId(@Param("companyId") Integer companyId,@Param("groupId") Integer groupId);

  int updateExtend(@Param("newName") String newName,@Param("oldName") String oldName,@Param("hotelServiceId")Integer hotelServiceId);

  List<HsHotelServiceExtend> allExtendByGroupId(@Param("companyId") Integer companyId,@Param("hotelServiceId") Integer hotelServiceId);

  List<HsHotelServiceExtendRes> extendByServiceId(HsHotelServiceExtend extend);

  List<HsHotelServiceExtendRes> findExtendsByList(@Param("extendIds")  List<String> extendIds);

  List<CleanAndRepairForWorkRes> extendByType(@Param("hotelServiceId") Integer hotelServiceId);

  List<ExtendRes> extendByCompany(@Param("companyId") Integer companyId,@Param("hotelServiceId") Integer hotelServiceId);

  Integer getDept(@Param("companyId") Integer companyId,@Param("extendId") Integer extendId,@Param("serviceType") String serviceType);

  List<ServiceContentRes> serviceContent(@Param("hotelServiceId") Integer hotelServiceId,@Param("companyId")Integer companyId);

  /**
   * 获得服务子项默认受理人
   * @param companyId
   * @param extendId
   * @param serviceType
   * @return
   */
  String getUser(@Param("companyId") Integer companyId,@Param("extendId") Integer extendId,@Param("serviceType") String serviceType);

  String getLastCode();


  int updateDeleteByCompanyIdAndServiceId(@Param("companyId") Integer companyId, @Param("hotelServiceId") Integer hotelServiceId);

  List<HsHotelServiceExtend> getByCode(@Param("companyId") Integer companyId, @Param("code") String code);

  List<HsHotelServiceExtend> getByName(@Param("companyId") Integer companyId, @Param("name") String name);

  List<HsHotelServiceExtend> getByServiceClassifyId(@Param("companyId") Integer companyId, @Param("serviceClassifyId") Long serviceClassifyId);

  int updateNameByServiceClassifyId(@Param("companyId") Integer companyId, @Param("serviceClassifyId") Long serviceClassifyId, @Param("serviceClassifyName") String serviceClassifyName);

  List<HsHotelServiceExtend> getByEmployeeTypeId(@Param("companyId") Integer companyId, @Param("employeeTypeId") Long employeeTypeId);

  int updateNameByEmployeeTypeId(@Param("companyId") Integer companyId, @Param("employeeTypeId") Long employeeTypeId, @Param("employeeTypeName") String employeeTypeName);
}
