package com.shands.mod.dao.mapper.datarevision;

import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.datarevision.vo.MemberMagicListVo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023-03-04
 */
public interface DeptDataMapper {

  /**
   * 查询最新数据日期
   * @return 最新日期
   */
  Date findMaxDate();

  /**
   * 查询会员消费相关数据
   * @param startTime 查询开始时间
   * @param endTime 查询结束时间
   * @param hotelCode 酒店code
   * @param type 数据类型
   * @return 会员消费数据列表
   */
  List<DeptDataVo> findDeptData(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("hotelCode") String hotelCode, @Param("type") String type);

  /**
   * 获取部门会员发展 && 法宝领取数量
   *
   * @param startTime 查询开始时间
   * @param endTime 查询结束时间
   * @param hotelCode 酒店code
   * @return 查询结果
   */
  List<MemberMagicListVo> queryDeptMemberMagicData(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("hotelCode") String hotelCode);

}