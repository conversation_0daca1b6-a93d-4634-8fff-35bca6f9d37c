package com.shands.mod.dao.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/09/25 2:36 下午 <br>
 * @see com.shands.mod.dao.model.req <br>
 */
@ApiModel("获取周边服务和交通信息请求参数")
public class PoiInfomationReq {

  @ApiModelProperty("关键词id")
  private List<Integer> keywordIds;

  public List<Integer> getKeywordIds() {
    return keywordIds;
  }

  public void setKeywordIds(List<Integer> keywordIds) {
    this.keywordIds = keywordIds;
  }
}
