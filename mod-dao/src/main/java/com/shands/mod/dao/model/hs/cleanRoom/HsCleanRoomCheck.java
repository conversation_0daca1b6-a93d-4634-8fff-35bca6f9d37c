package com.shands.mod.dao.model.hs.cleanRoom;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 清扫检查表(HsCleanRoomCheck)实体类
 *
 * <AUTHOR>
 * @since 2021-07-05 16:19:37
 */
@Data
public class HsCleanRoomCheck implements Serializable {

  private static final long serialVersionUID = -85683111179638112L;
  /**
   * 表主键
   */
  private Integer id;
  /**
   * 清扫任务表主键（hs_clean_room_task）
   */
  private Integer taskId;
  /**
   * 清扫次数
   */
  private Integer cleanTimes;
  /**
   * 开始清扫时间
   */
  private Date startClean;
  /**
   * 完成清扫时间
   */
  private Date endClean;
  /**
   * 开始检查时间
   */
  private Date startCheck;
  /**
   * 完成检查时间
   */
  private Date endCheck;
  /**
   * 清扫耗时
   */
  private String totalTime;
  /**
   * 清扫人员
   */
  private Integer cleanUser;
  /**
   * 检查人员
   */
  private Integer checkUser;
  /**
   * 酒店id
   */
  private Integer companyId;
  /**
   * 删除标记
   */
  private Integer deleted;
  /**
   * 创建时间
   */
  private Date createTime;
  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 处理结果
   */
  private String checkResult;

}