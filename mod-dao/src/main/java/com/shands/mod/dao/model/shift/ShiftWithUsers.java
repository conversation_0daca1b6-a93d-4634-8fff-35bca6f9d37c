package com.shands.mod.dao.model.shift;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * 班次及其排班员工
 * <AUTHOR>
 * @Date 2023/7/12 14:36
 */
@Data
public class ShiftWithUsers {

  @ApiModelProperty(value = "班次id")
  @NotNull(message = "shiftId不能为空")
  Integer shiftId;

  @ApiModelProperty(value = "班次名称")
  String shiftName;

  @ApiModelProperty(value = "已选择的人员信息列表")
  @Size(min = 1, message = "selectedUserInfoList不能为空且至少有一个元素")
  List<ModBaseUser> selectedUserInfoList;

  @ApiModelProperty(value = "已选择的人员id列表")
  List<Integer> selectedUserIdList;

  @ApiModelProperty(value = "班次下可排班人员列表")
  List<ModBaseUser> availableShiftUserList;
}
