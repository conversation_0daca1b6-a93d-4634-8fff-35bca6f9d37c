package com.shands.mod.dao.mapper.training;

import com.shands.mod.dao.model.training.bo.LearningBo;
import com.shands.mod.dao.model.training.bo.StatisticsBo;
import com.shands.mod.dao.model.training.po.ModCourseUser;
import com.shands.mod.dao.model.training.vo.LastLearnVo;
import com.shands.mod.dao.model.training.vo.StatisticsDetailsVo;
import java.awt.print.Pageable;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 课程学员表(ModCourseUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-17 15:09:48
 */
public interface ModCourseUserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ModCourseUser queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param modCourseUser 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<ModCourseUser> queryAllByLimit(ModCourseUser modCourseUser, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param modCourseUser 查询条件
     * @return 总行数
     */
    long count(ModCourseUser modCourseUser);

    /**
     * 新增数据
     *
     * @param modCourseUser 实例对象
     * @return 影响行数
     */
    int insert(ModCourseUser modCourseUser);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ModCourseUser> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ModCourseUser> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ModCourseUser> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ModCourseUser> entities);

    /**
     * 修改数据
     *
     * @param modCourseUser 实例对象
     * @return 影响行数
     */
    int update(ModCourseUser modCourseUser);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<StatisticsDetailsVo> getStudySituationList(@Param("statisticsBo") StatisticsBo statisticsBo);

    /**
     * 根据userid查询是否进行过学习
     */
    ModCourseUser selectCourseUserByUserId(@Param("learningBo") LearningBo learningBo);

    /**
     * 根据用户 查询本合集里面最后学习的课程
     */
    LastLearnVo selectLastCourseByUserId(@Param("userId") Integer userId,@Param("collectionId") Integer collectionId);

    int selectUserLearningCount(@Param("tempId") Integer tempId, @Param("moduleType") String moduleType);
}

