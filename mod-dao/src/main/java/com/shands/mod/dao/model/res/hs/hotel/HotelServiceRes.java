package com.shands.mod.dao.model.res.hs.hotel;

import com.shands.mod.dao.model.res.hs.pay.PayTypeRes;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class HotelServiceRes {

  /**
   * 数据库id
   */
  private Integer id;
  /**
   * 服务类型id
   */
  private Integer serviceCode;

  private String serviceEunm;
  /**
   * 服务类型名称
   */
  private String serviceType;
  /**
   * 默认受理部门
   */
  private Integer acceptDept;
  /**
   * 受理部门名称
   */
  private String deptName;
  /**
   * 协助部门
   */
  private Integer assistDept;
  /**
   * 默认总台审核方式
   */
  private Boolean auditing;
  /**
   * 默认派单方式
   */
  private Integer arrange;
  /**
   * 派单方式中文
   */
  private String arrangeString;
  /**
   * 开始时间
   */
  private String startTime;
  /**
   * 结束时间
   */
  private String endTime;
  /**
   * 状态
   */
  private Boolean status;

  /**
   * 灰色图片
   */
  private String greyPic;

  /**
   * 彩色图片
   */
  private String colourPic;

  /**
   * 服务类型区分
   */
  private int platformFlag;

  /**
   * 服务子项类型:1-服务内容,2-物品管理
   */
  private Integer serviceExtendType;

  /**
   * 悦选的店铺地址
   */
  private String shopUrl;
  /**
   * 支付方式0 线上支付，1.线下支付(手工挂房账或直接支付)
   */
  private Integer payType;

  /**
   * 备注信息
   */
  private String remark;

  /**
   * 支付方式详情
   */
  private PayTypeRes payTypeRes = new PayTypeRes();

  /**
   * 支付方式 String格式
   */
  private String payTypeList;

  /**
   * 是否收费
   */
  private Boolean charge;

  /**
   * 是否拥有子项
   */
  private Boolean isHave;
  /**
   * 温馨提示
   */
  private String prompt;

  /**
   * 服务电话
   */
  private String phone;

  /**
   * 餐饮类型
   */
  private List<FoodConfigByServiceIdRes> foodConfig = new ArrayList<>();

  /**
   * 抄送人  duplicate_people
   */
  private String duplicatePeople;
  /**
   * copy_people
   */
  private String copyPeople;

  /**
   * 受理人 user_id
   */
  private String userId;

  /**
   * 返回给前端的受理人 copy_user_id
   */
  private String copyUserId;

  private String mapType;

  private String mapUrl;

  /**
   * 寄存需员工确认 开启open，关闭off consign_staff_affirm SwitchEnum
   */
  private String consignStaffAffirm;


  private String shiftType;
}
