package com.shands.mod.dao.model.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;
/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum PlaceTypeEnum {
    /**
     * 公共区域
     */
    PUBLIC(0, "公共区域"),

    /**
     * 房间
     */
    ROOM(1, "房间");



    private final Integer code;
    private final String desc;

    public static PlaceTypeEnum getEnumByCode(Integer code) {
        for (PlaceTypeEnum e : PlaceTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

  public static PlaceTypeEnum getEnumByDesc(String desc) {
    for (PlaceTypeEnum e : PlaceTypeEnum.values()) {
      if (e.desc.equals(desc)) {
        return e;
      }
    }
    return null;
  }
} 