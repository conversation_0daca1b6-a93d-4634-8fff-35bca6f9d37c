package com.shands.mod.dao.mapper.fw;

import com.shands.mod.dao.model.fw.ActivityRecommend;
import com.shands.mod.dao.model.fw.ActivityRecommendExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityRecommendMapper {
    int countByExample(ActivityRecommendExample example);

    int deleteByExample(ActivityRecommendExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ActivityRecommend record);

    int insertSelective(ActivityRecommend record);

    List<ActivityRecommend> selectByExample(ActivityRecommendExample example);

    ActivityRecommend selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ActivityRecommend record, @Param("example") ActivityRecommendExample example);

    int updateByExample(@Param("record") ActivityRecommend record, @Param("example") ActivityRecommendExample example);

    int updateByPrimaryKeySelective(ActivityRecommend record);

    int updateByPrimaryKey(ActivityRecommend record);

    int insertBatch(@Param("list") List<ActivityRecommend> activityRecommendList);

    int heatIncrease(@Param("id") Integer activityRecommendId);
}