<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.humanresources.ModEmailRecordMapper">

    <resultMap type="com.shands.mod.dao.model.humanresources.ModEmailRecord" id="ModEmailRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="fileId" column="file_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="hotelName" column="hotel_name" jdbcType="VARCHAR"/>
        <result property="divisionName" column="division_name" jdbcType="VARCHAR"/>
        <result property="sendStatus" column="send_status" jdbcType="VARCHAR"/>
        <result property="emailUrl" column="email_url" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="preparedBy" column="prepared_by" jdbcType="VARCHAR"/>
        <result property="mailBox" column="mail_box" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="month" column="month" jdbcType="VARCHAR"/>
        <result property="ifDelete" column="if_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="hotelCode" column="hotel_code" jdbcType="VARCHAR"/>
        <result property="totalRemittance" column="total_remittance" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ModEmailRecordMap">
        select
        id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year,month, if_delete, create_time, update_time, create_user, update_user, version, remark        from mod_email_record
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ModEmailRecordMap">
        select
          id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year,month, if_delete, create_time, update_time, create_user, update_user, version, remark
        from mod_email_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="fileId != null">
              and file_id = #{fileId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="hotelName != null and hotelName != ''">
                and hotel_name = #{hotelName}
            </if>
            <if test="divisionName != null and divisionName != ''">
                and division_name = #{divisionName}
            </if>
            <if test="sendStatus != null and sendStatus != ''">
                and send_status = #{sendStatus}
            </if>
            <if test="emailUrl != null and emailUrl != ''">
                and email_url = #{emailUrl}
            </if>
            <if test="sendTime != null">
                and send_time = #{sendTime}
            </if>
            <if test="preparedBy != null and preparedBy != ''">
              and prepared_by = #{preparedBy}
            </if>
            <if test="mailBox != null and mailBox != ''">
              and mail_box = #{mailBox}
            </if>
            <if test="year != null and year != ''">
                and year = #{year}
            </if>
            <if test="month != null and month != ''">
                and month = #{month}
            </if>
            <if test="ifDelete != null">
                and if_delete = #{ifDelete}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

  <!--查询指定行数据-->
  <select id="queryAll" resultMap="ModEmailRecordMap">
    select
    id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year,month, if_delete, create_time, update_time, create_user, update_user, version, remark, hotel_code, total_remittance
    from mod_email_record
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="fileId != null">
        and file_id = #{fileId}
      </if>
      <if test="name != null and name != ''">
        and name = #{name}
      </if>
      <if test="email != null and email != ''">
        and email = #{email}
      </if>
      <if test="hotelName != null and hotelName != ''">
        and hotel_name = #{hotelName}
      </if>
      <if test="hotelCode != null and hotelCode != ''">
        and hotel_code = #{hotelCode}
      </if>
      <if test="divisionName != null and divisionName != ''">
        and division_name = #{divisionName}
      </if>
      <if test="sendStatus != null and sendStatus != ''">
        and send_status = #{sendStatus}
      </if>
      <if test="emailUrl != null and emailUrl != ''">
        and email_url = #{emailUrl}
      </if>
      <if test="sendTime != null">
        and send_time = #{sendTime}
      </if>
      <if test="preparedBy != null and preparedBy != ''">
        and prepared_by = #{preparedBy}
      </if>
      <if test="mailBox != null and mailBox != ''">
        and mail_box = #{mailBox}
      </if>
      <if test="year != null and year != ''">
        and year = #{year}
      </if>
      <if test="month != null and month != ''">
        and month = #{month}
      </if>
      <if test="ifDelete != null">
        and if_delete = #{ifDelete}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="version != null">
        and version = #{version}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark}
      </if>
    </where>

  </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mod_email_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="fileId != null">
              and file_id = #{fileId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="hotelName != null and hotelName != ''">
                and hotel_name = #{hotelName}
            </if>
            <if test="divisionName != null and divisionName != ''">
                and division_name = #{divisionName}
            </if>
            <if test="sendStatus != null and sendStatus != ''">
                and send_status = #{sendStatus}
            </if>
            <if test="emailUrl != null and emailUrl != ''">
                and email_url = #{emailUrl}
            </if>
            <if test="sendTime != null">
                and send_time = #{sendTime}
            </if>
            <if test="preparedBy != null and preparedBy != ''">
              and prepared_by = #{preparedBy}
            </if>
            <if test="mailBox != null and mailBox != ''">
              and mail_box = #{mailBox}
            </if>
            <if test="year != null and year != ''">
                and year = #{year}
            </if>
            <if test="month != null and month != ''">
                and month = #{month}
            </if>
            <if test="ifDelete != null">
                and if_delete = #{ifDelete}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

  <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into mod_email_record(id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year ,month,if_delete, create_time, update_time, create_user, update_user, version, remark, hotel_code, total_remittance)
        values (#{id},#{fileId}, #{name}, #{email}, #{hotelName}, #{divisionName}, #{sendStatus}, #{emailUrl}, #{sendTime},#{preparedBy},#{mailBox},#{year},#{month}, #{ifDelete}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{version}, #{remark}, #{hotelCode}, #{totalRemittance})
    </insert>

    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into mod_email_record(id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year ,month, if_delete, create_time, update_time, create_user, update_user, version, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.id},#{entity.fileId}, #{entity.name}, #{entity.email}, #{entity.hotelName}, #{entity.divisionName}, #{entity.sendStatus}, #{entity.emailUrl}, #{entity.sendTime},#{entity.preparedBy},#{entity.mailBox}, #{entity.year},#{entity.month}, #{entity.ifDelete}, #{entity.createTime}, #{entity.updateTime}, #{entity.createUser}, #{entity.updateUser}, #{entity.version}, #{entity.remark})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into mod_email_record(id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year ,month, if_delete, create_time, update_time, create_user, update_user, version, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.fileId}, #{entity.name}, #{entity.email}, #{entity.hotelName}, #{entity.divisionName}, #{entity.sendStatus}, #{entity.emailUrl}, #{entity.sendTime},#{entity.preparedBy},#{entity.mailBox},#{entity.year},#{entity.month}, #{entity.ifDelete}, #{entity.createTime}, #{entity.updateTime}, #{entity.createUser}, #{entity.updateUser}, #{entity.version}, #{entity.remark})
        </foreach>
        on duplicate key update
        id = values(id),
        file_id = values(file_id),
        name = values(name),
        email = values(email),
        hotel_name = values(hotel_name),
        division_name = values(division_name),
        send_status = values(send_status),
        email_url = values(email_url),
        send_time = values(send_time),
        prepared_by = values(prepared_by),
        mail_box = values(mail_box),
        year = values(year),
        month = values(month),
        if_delete = values(if_delete),
        create_time = values(create_time),
        update_time = values(update_time),
        create_user = values(create_user),
        update_user = values(update_user),
        version = values(version),
        remark = values(remark)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mod_email_record
        <set>
            <if test="id != null">
                id = #{id},
            </if>
            <if test="fileId != null">
                file_id = #{fileId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="hotelName != null and hotelName != ''">
                hotel_name = #{hotelName},
            </if>
            <if test="divisionName != null and divisionName != ''">
                division_name = #{divisionName},
            </if>
            <if test="sendStatus != null and sendStatus != ''">
                send_status = #{sendStatus},
            </if>
            <if test="emailUrl != null and emailUrl != ''">
                email_url = #{emailUrl},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime},
            </if>
            <if test="preparedBy != null and preparedBy != ''">
                prepared_by = #{preparedBy},
            </if>
            <if test="mailBox != null and mailBox != ''">
                mail_box = #{mailBox},
            </if>
            <if test="year != null and year != ''">
              year = #{year},
            </if>
            <if test="month != null and month != ''">
              month = #{month},
            </if>
            <if test="ifDelete != null">
                if_delete = #{ifDelete},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="hotelCode != null and hotelCode != ''">
              hotel_code = #{hotelCode},
            </if>
            <if test="totalRemittance != null and totalRemittance != ''">
              total_remittance = #{totalRemittance},
            </if>
        </set>
        where id = #{id}
    </update>
  <update id="updateStatus">
    UPDATE mod_email_record SET send_status = #{sendStatus} WHERE file_id = #{id};
  </update>

  <!--通过主键删除-->
    <delete id="deleteById">
        delete from mod_email_record where id = #{id}
    </delete>

  <select id="queryModEmailRecord" resultType="com.shands.mod.dao.model.humanresources.vo.SendRecordsVo">
    SELECT
      id,
      name,
      email,
      hotel_name hotelName,
      division_name divisionName,
      send_status sendStatus,
      email_url mailUrl,
      send_time sendTime
    FROM
      mod_email_record
    <where>
        if_delete = 0 and create_user = #{userId}
      <if test="emailRecordBo.fileId != null and emailRecordBo.fileId != ''">
        AND file_id = #{emailRecordBo.fileId}
      </if>
      <if test="emailRecordBo.sendStatus != null and emailRecordBo.sendStatus != ''">
        AND send_status = #{emailRecordBo.sendStatus}
      </if>
      <if test="emailRecordBo.name != null and emailRecordBo.name != ''">
        AND `name` LIKE CONCAT('%', #{emailRecordBo.name}, '%')
      </if>
      <if test="emailRecordBo.hotelName != null and emailRecordBo.hotelName != ''">
        AND hotel_name LIKE CONCAT('%', #{emailRecordBo.hotelName}, '%')
      </if>
    </where>
    order by create_time desc
  </select>

  <select id="queryByFileId" resultMap="ModEmailRecordMap">
    SELECT
    id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year,month, if_delete, create_time, update_time, create_user, update_user, version, remark
    FROM
    mod_email_record
    <where>
      if_delete = 0 and create_user = #{userId}
      <if test="fileId != null">
        AND file_id = #{fileId}
      </if>
    </where>
    order by create_time desc
  </select>

  <select id="queryByIds" resultMap="ModEmailRecordMap">
    SELECT
    id,file_id, name, email, hotel_name, division_name, send_status, email_url, send_time,prepared_by,mail_box,year,month, if_delete, create_time, update_time, create_user, update_user, version, remark
    FROM
      mod_email_record
    where if_delete = 0
    and id in
    <foreach collection="modEmailIdList" separator="," open="("  close=")" item="id">
      #{id}
    </foreach>
  </select>

  <update id="updateStatusByIds">
    UPDATE mod_email_record SET send_status = #{sendStatus}
    <where>
      file_id in
      <foreach collection="idList" item="id" open="(" close=")" separator="," >
        #{id}
      </foreach>
    </where>
  </update>
</mapper>

