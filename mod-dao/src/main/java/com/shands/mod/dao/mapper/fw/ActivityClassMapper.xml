<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.fw.ActivityClassMapper">
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.fw.ActivityClass">
    <!--@mbg.generated-->
    <!--@Table fw_activity_class-->
    <id column="ID" jdbcType="INTEGER" property="id"/>
    <result column="CLASS_NAME" jdbcType="VARCHAR" property="className"/>
    <result column="COMPANY" jdbcType="INTEGER" property="company"/>
    <result column="DELETE" jdbcType="BOOLEAN" property="delete"/>
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="CREATE_USER" jdbcType="INTEGER" property="createUser"/>
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="UPDATE_USER" jdbcType="INTEGER" property="updateUser"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CLASS_NAME, COMPANY, `DELETE`, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    SELECT
    <include refid="Base_Column_List"/>
    FROM fw_activity_class
    WHERE ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    DELETE
    FROM fw_activity_class
    WHERE ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id"
    parameterType="com.shands.mod.dao.model.fw.ActivityClass" useGeneratedKeys="true">
    <!--@mbg.generated-->
    INSERT INTO fw_activity_class (CLASS_NAME, COMPANY, `DELETE`,
                                   CREATE_TIME, CREATE_USER, UPDATE_TIME,
                                   UPDATE_USER)
    VALUES (#{className,jdbcType=VARCHAR}, #{company,jdbcType=INTEGER}, #{delete,jdbcType=BOOLEAN},
            #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER},
            #{updateTime,jdbcType=TIMESTAMP},
            #{updateUser,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id"
    parameterType="com.shands.mod.dao.model.fw.ActivityClass" useGeneratedKeys="true">
    <!--@mbg.generated-->
    INSERT INTO fw_activity_class
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="className != null">
        CLASS_NAME,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="delete != null">
        `DELETE`,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createUser != null">
        CREATE_USER,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateUser != null">
        UPDATE_USER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="className != null">
        #{className,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=INTEGER},
      </if>
      <if test="delete != null">
        #{delete,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.shands.mod.dao.model.fw.ActivityClass">
    <!--@mbg.generated-->
    UPDATE fw_activity_class
    <set>
      <if test="className != null">
        CLASS_NAME = #{className,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        COMPANY = #{company,jdbcType=INTEGER},
      </if>
      <if test="delete != null">
        `DELETE` = #{delete,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        CREATE_USER = #{createUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      </if>
    </set>
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.fw.ActivityClass">
    <!--@mbg.generated-->
    UPDATE fw_activity_class
    SET CLASS_NAME  = #{className,jdbcType=VARCHAR},
        COMPANY     = #{company,jdbcType=INTEGER},
        `DELETE`    = #{delete,jdbcType=BOOLEAN},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER}
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertOrUpdate">
    INSERT INTO fw_activity_class
      (id, class_name, COMPANY, create_user)
      VALUE (#{req.id}, #{req.className}, #{companyId}, #{userId})
    ON DUPLICATE KEY UPDATE CLASS_NAME  = #{req.className},
                            UPDATE_USER = #{userId}
  </insert>
  <select id="findAll" resultMap="BaseResultMap">
    SELECT ID,
           CLASS_NAME
    FROM fw_activity_class fac
    WHERE COMPANY = #{companyId}
  </select>
</mapper>