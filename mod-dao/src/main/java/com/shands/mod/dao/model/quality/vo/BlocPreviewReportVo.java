package com.shands.mod.dao.model.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/17
 **/
@Data
@ApiModel("集团巡检报告出参")
public class BlocPreviewReportVo {

  @ApiModelProperty("预览报告部分出参")
  private TaskVo taskVo;

  @ApiModelProperty("各维度得分概览")
  private DimensionVo dimensionScores;

  @ApiModelProperty("部门得分概览列表")
  private List<DepartmentScoresVo> departmentScores;

  @ApiModelProperty("各区域得分概览")
  private List<RegionVo> regionScores;

  @ApiModelProperty("分类得分")
  private List<RegionScoresVo> groupScores;

  @ApiModelProperty("总结")
  private String summary;
}