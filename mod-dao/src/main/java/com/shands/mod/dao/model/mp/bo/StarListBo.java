package com.shands.mod.dao.model.mp.bo;

import com.shands.mod.dao.model.workorder.bo.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 工单配置字段详情表(MpAppraisalRecord)实体类
 *
 * <AUTHOR>
 * @since 2022-11-03 11:22:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ApiModel("评分记录详情参数")
public class StarListBo extends Page {

  @ApiModelProperty("userId")
  private Integer userId;

  @ApiModelProperty("考核code")
  private String taskCode;
}

