package com.shands.mod.dao.model.call;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * mod_call_record_log
 * <AUTHOR>
@Data
public class ModCallRecordLog implements Serializable {
    /**
     * 表主键
     */
    private Integer id;

    /**
     * 用户手机号
     */
    private String calledNumber;

    /**
     * 此次通话的唯一任务id
     */
    private String taskids;

    /**
     * 请求状态码
     */
    private String ack;

    /**
     * 状态码的描述
     */
    private String codeDes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 错误信息
     */
    private String errorsMessage;

    private static final long serialVersionUID = 1L;
}