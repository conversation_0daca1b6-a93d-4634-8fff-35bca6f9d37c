package com.shands.mod.dao.model.req.hs.plan;

import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-10-28
 * @description 计划卫生分类
 */

@Data
public class PlanClassifyReq {

  /**
   * 表主键
   */
  private Integer id;
  /**
   * 分类名称
   */
  private String name;
  /**
   * 分类编码
   */
  private String code;
  /**
   * 周期
   */
  private String period;
  /**
   * 排序字段
   */
  private Integer sort;
  /**
   * 酒店id
   */
  private Integer companyId;
  /**
   * 删除标记
   */
  private Integer deleted;
  /**
   * 创建时间
   */
  private Date createTime;
  /**
   * 创建人
   */
  private Integer createUser;
  /**
   * 修改时间
   */
  private Date updateTime;
  /**
   * 修改人
   */
  private Integer updateUser;
}
