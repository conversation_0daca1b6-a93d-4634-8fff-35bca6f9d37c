package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.ModVersions;
import com.shands.mod.dao.model.res.AppVersionsRes;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ModVersionsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ModVersions record);

    int insertSelective(ModVersions record);

    ModVersions selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ModVersions record);

    int updateByPrimaryKey(ModVersions record);

    List<AppVersionsRes> query(@Param("type") String type);

    AppVersionsRes max(@Param("type") String type);

}