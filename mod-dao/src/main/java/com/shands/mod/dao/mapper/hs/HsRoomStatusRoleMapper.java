package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.HsRoomStatusRole;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 房态操作时间权限控制(HsRoomStatusRole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-09-22 16:42:13
 */
public interface HsRoomStatusRoleMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    HsRoomStatusRole queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<HsRoomStatusRole> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param hsRoomStatusRole 实例对象
     * @return 对象列表
     */
    List<HsRoomStatusRole> queryAll(HsRoomStatusRole hsRoomStatusRole);

    /**
     * 新增数据
     *
     * @param hsRoomStatusRole 实例对象
     * @return 影响行数
     */
    int insert(HsRoomStatusRole hsRoomStatusRole);

    /**
     * 修改数据
     *
     * @param hsRoomStatusRole 实例对象
     * @return 影响行数
     */
    int update(HsRoomStatusRole hsRoomStatusRole);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);
	
	/**
     * 通过roleid查询房态操作权限
     *
     * @param list 列表
     * @return {@link List}<{@link HsRoomStatusRole}>
     */
    List<HsRoomStatusRole> queryByRoleIds(@Param("entities") List<String> list);

}