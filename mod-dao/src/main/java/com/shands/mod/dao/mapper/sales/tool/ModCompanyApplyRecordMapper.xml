<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.sales.tool.ModCompanyApplyRecordMapper">

    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ucId" column="uc_id" jdbcType="BIGINT"/>
            <result property="hotelCode" column="hotel_code" jdbcType="VARCHAR"/>
            <result property="bizLicenseImg" column="biz_license_img" jdbcType="VARCHAR"/>
            <result property="taxpayerId" column="taxpayer_id" jdbcType="VARCHAR"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="companyAddress" column="company_address" jdbcType="VARCHAR"/>
            <result property="linkman" column="linkman" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="emailSuffix" column="email_suffix" jdbcType="VARCHAR"/>
            <result property="applyRes" column="apply_res" jdbcType="TINYINT"/>
            <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uc_id,hotel_code,
        biz_license_img,taxpayer_id,company_name,
        company_address,linkman,mobile,
        email,email_suffix,apply_res,
        fail_reason,remark,create_time,
        update_time,del_flag
    </sql>
    <insert id="insertOne" parameterType="com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord">
        INSERT INTO mod_company_apply_record
        (uc_id,hotel_code,biz_license_img,taxpayer_id,company_name,company_address,linkman,mobile,email,email_suffix,apply_res,fail_reason,remark)
        VALUES
        (#{ucId,jdbcType=BIGINT},#{hotelCode,jdbcType=VARCHAR},#{bizLicenseImg,jdbcType=VARCHAR},
         #{taxpayerId,jdbcType=VARCHAR},#{companyName,jdbcType=VARCHAR},#{companyAddress,
         jdbcType=VARCHAR},#{linkman,jdbcType=VARCHAR},#{mobile,jdbcType=VARCHAR},#{email,jdbcType=VARCHAR},
         #{emailSuffix,jdbcType=VARCHAR},#{applyRes,jdbcType=TINYINT},#{failReason,jdbcType=VARCHAR},
         #{remark,jdbcType=VARCHAR})
    </insert>
  <select id="selectByUcIdAndHotelCode" resultMap ="BaseResultMap">
    SELECT
        *
    FROM
        mod_company_apply_record
    WHERE
        uc_id = #{ucId,jdbcType=BIGINT}
        AND hotel_code = #{hotelCode,jdbcType=VARCHAR}
        AND del_flag = 0
    ORDER BY create_time DESC
  </select>
</mapper>
