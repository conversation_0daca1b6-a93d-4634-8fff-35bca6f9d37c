package com.shands.mod.dao.model.req.hs.workorder;

import com.shands.mod.dao.model.req.hs.HsBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotNull;

/** 接单参数 */
@Data
@NoArgsConstructor
@ApiModel(value = "完成工单请求参数Dto")
public class WorkOrderCompleteReq extends HsBase {

  /** 工单id */
  @NotNull(message = "工单ID不能为空")
  @ApiModelProperty(value = "工单id")
  private Integer workOrderId;

  /** 完成时的一句话 */
  @ApiModelProperty(value = "完成时的一句话")
  private String finishedProcessing;
}
