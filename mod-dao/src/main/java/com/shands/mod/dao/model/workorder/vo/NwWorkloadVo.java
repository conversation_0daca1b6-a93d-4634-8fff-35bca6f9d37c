package com.shands.mod.dao.model.workorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@ApiModel("工作量统计")
public class NwWorkloadVo {

  @ApiModelProperty(value="酒店id",required = true)
  private int hotelId;

  @ApiModelProperty(value="酒店名称",required = true)
  private String hotelName;

  @ApiModelProperty(value="总数",required = true)
  private int total;

  @ApiModelProperty(value="完成数",required = true)
  private int completeNum;

  @ApiModelProperty(value="关闭数",required = true)
  private int closeNum;

  @ApiModelProperty(value="处理中数",required = true)
  private int processNum;

  @ApiModelProperty(value="未处理数",required = true)
  private int inProcessNum;

  @ApiModelProperty(value="超时数",required = true)
  private int outTimeNum;

  @ApiModelProperty(value="总时间（秒）",required = true)
  private long totalTime;

  @ApiModelProperty(value="平均时间（秒）",required = true)
  private long avgTime;

  @ApiModelProperty(value="总小时数",required = true)
  private long totalHour;

  @ApiModelProperty(value="总分钟数",required = true)
  private long totalMinune;

  @ApiModelProperty(value="平均小时数",required = true)
  private long avgHour;

  @ApiModelProperty(value="平均分钟数",required = true)
  private long avgMinune;
}
