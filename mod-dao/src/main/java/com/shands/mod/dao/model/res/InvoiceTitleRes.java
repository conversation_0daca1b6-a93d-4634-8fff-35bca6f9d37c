package com.shands.mod.dao.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

public class InvoiceTitleRes {

  private Integer id;

  private Integer customerId;

  private Byte type;

  private String workUnitName;

  private String taxNumber;

  private String bankName;

  private String bankAccount;

  private String workUnitAddress;

  private String companyPhone;

  private String realName;

  private Byte isDefault;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createTime;

  private String remark;

  private String qrCode;

  public String getQrCode() {
    return qrCode;
  }

  public void setQrCode(String qrCode) {
    this.qrCode = qrCode;
  }

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getCustomerId() {
    return customerId;
  }

  public void setCustomerId(Integer customerId) {
    this.customerId = customerId;
  }

  public Byte getType() {
    return type;
  }

  public void setType(Byte type) {
    this.type = type;
  }

  public String getWorkUnitName() {
    return workUnitName;
  }

  public void setWorkUnitName(String workUnitName) {
    this.workUnitName = workUnitName == null ? null : workUnitName.trim();
  }

  public String getTaxNumber() {
    return taxNumber;
  }

  public void setTaxNumber(String taxNumber) {
    this.taxNumber = taxNumber == null ? null : taxNumber.trim();
  }

  public String getBankName() {
    return bankName;
  }

  public void setBankName(String bankName) {
    this.bankName = bankName == null ? null : bankName.trim();
  }

  public String getBankAccount() {
    return bankAccount;
  }

  public void setBankAccount(String bankAccount) {
    this.bankAccount = bankAccount == null ? null : bankAccount.trim();
  }

  public String getWorkUnitAddress() {
    return workUnitAddress;
  }

  public void setWorkUnitAddress(String workUnitAddress) {
    this.workUnitAddress = workUnitAddress == null ? null : workUnitAddress.trim();
  }

  public String getCompanyPhone() {
    return companyPhone;
  }

  public void setCompanyPhone(String companyPhone) {
    this.companyPhone = companyPhone == null ? null : companyPhone.trim();
  }

  public String getRealName() {
    return realName;
  }

  public void setRealName(String realName) {
    this.realName = realName == null ? null : realName.trim();
  }

  public Byte getIsDefault() {
    return isDefault;
  }

  public void setIsDefault(Byte isDefault) {
    this.isDefault = isDefault;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark == null ? null : remark.trim();
  }

}
