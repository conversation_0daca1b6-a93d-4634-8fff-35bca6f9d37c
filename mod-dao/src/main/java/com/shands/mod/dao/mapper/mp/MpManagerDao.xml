<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.mp.MpManagerDao">


  <resultMap type="com.shands.mod.dao.model.mp.po.MpManager" id="MpManagerMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="userId" column="user_id" jdbcType="INTEGER"/>
    <result property="userName" column="user_name" jdbcType="VARCHAR"/>
    <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
    <result property="companyId" column="company_id" jdbcType="INTEGER"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>

    <!--查询单个-->
  <select id="queryById" resultMap="MpManagerMap">
    select
      id, user_id, user_name, company_name, company_id, update_time
    from mp_manager_list
    where id = #{id}
  </select>

    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.shands.mod.dao.model.mp.vo.ManageVo">
        select
          mm.user_id userId,mm.company_id companyId,mm.update_time updateTime,mu.MOBILE mobile,mh.hotel_code hotelCode,mh.hotel_name hotelName,mh.hotel_brand_code hotelBrandCode,mh.hotel_ownership_code hotelOwnershipcode,mh.hotel_cooperation_code hotelCooperationCode,mh.contract contract ,mh.star_flag starFlag
        from mp_manager_list mm
        left join mod_hotel_info mh on mm.company_id=mh.uc_company_id
        left join mod_user mu on mm.user_id=mu.uc_id
--         where mm.user_id=33815
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
      select count(1)
      from mp_manager_list
      <where>
        <if test="id != null">
          and id = #{id}
        </if>
        <if test="userId != null">
          and user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
          and user_name = #{userName}
        </if>
        <if test="companyName != null and companyName != ''">
          and company_name = #{companyName}
        </if>
        <if test="companyId != null">
          and company_id = #{companyId}
        </if>
        <if test="updateTime != null">
          and update_time = #{updateTime}
        </if>
      </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
      insert into mp_manager_list(user_id, user_name, company_name, company_id, update_time)
      values (#{userId}, #{userName}, #{companyName}, #{companyId}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
      insert into mp_manager_list(user_id, user_name, company_name, company_id, update_time)
      values
      <foreach collection="entities" item="entity" separator=",">
        (#{entity.userId}, #{entity.userName}, #{entity.companyName}, #{entity.companyId}, #{entity.updateTime})
      </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
      insert into mp_manager_list(user_id, user_name, company_name, company_id, update_time)
      values
      <foreach collection="entities" item="entity" separator=",">
        (#{entity.userId}, #{entity.userName}, #{entity.companyName}, #{entity.companyId}, #{entity.updateTime})
      </foreach>
      on duplicate key update
      user_id = values(user_id),
      user_name = values(user_name),
      company_name = values(company_name),
      company_id = values(company_id),
      update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
      update mp_manager_list
      <set>
        <if test="userId != null">
          user_id = #{userId},
        </if>
        <if test="userName != null and userName != ''">
          user_name = #{userName},
        </if>
        <if test="companyName != null and companyName != ''">
          company_name = #{companyName},
        </if>
        <if test="companyId != null">
          company_id = #{companyId},
        </if>
        <if test="updateTime != null">
          update_time = #{updateTime},
        </if>
      </set>
      where company_id = #{companyId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mp_manager_list where id = #{id}
    </delete>

  <select id="selectByManager" resultType="java.lang.Integer">
    select
    company_id
    from mp_manager_list
    <where>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
    </where>
  </select>

  <select id="selectByHotel" resultType="com.shands.mod.dao.model.mp.vo.ManageHotelVo">
    SELECT mhi.hotel_code hotelCode,mml.update_time updateTime,mhi.hotel_brand_code hotelBrandCode,mhi.hotel_cooperation_code hotelCooperationCode,mhi.hotel_ownership_code hotelOwnershipcode
    from mp_manager_list mml
    left join mod_hotel_info mhi on mml.company_id=mhi.uc_company_id
    <where>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
    </where>
  </select>

  <select id="selectHotelByGm" resultType="com.shands.mod.dao.model.newDataBoard.vo.HotelByGmVo">
SELECT hi.grade as hotelRating,hi.star_flag as ifStar, hi.hotel_code as hotelCode,hi.hotel_name as hotelName,hi.contract as hotelState from mp_manager_list mm LEFT JOIN mod_hotel_info hi on mm.company_id=hi.uc_company_id
WHERE mm.user_id=#{userId}
    <if test="divisionCode != null and divisionCode != ''">
      and hi.hotel_ownership_code=#{divisionCode}
    </if>
and hi.hotel_status=1
  </select>

  <select id="selectAllByUpdateTime" resultType="java.util.Date">
    select mml.update_time
    from mp_manager_list mml
    left join mod_hotel_info mhi on mhi.uc_company_id=mml.company_id
    where user_id = #{userId}
    and mhi.hotel_code = #{hotelCode}
  </select>

  <select id="selectManageList" resultType="com.shands.mod.dao.model.mp.vo.ManageListVo">
    SELECT
    *
    FROM
    (SELECT
    mml.user_name userName,
    mml.user_id userId,
    mu.MOBILE mobile,
    mhi.hotel_ownership_code deptCode,
    mhi.hotel_brand_code brandCode,
    mhi.hotel_name hotelName,
    mhi.hotel_code hotelCode,
    z.star
    FROM
    mp_manager_list mml
    LEFT JOIN mod_hotel_info mhi ON mml.company_id = mhi.uc_company_id
    LEFT JOIN (SELECT star, user_id from mp_star_log where id in  ( SELECT max(id) FROM mp_star_log GROUP BY user_id) ) z ON z.user_id = mml.user_id
    left join mod_user mu on mu.uc_id=mml.user_id
    <where>
      <if test="manageListBo.userName != null and manageListBo.userName !=''">
        and mml.user_name like concat('%',#{manageListBo.userName},'%')
      </if>
      <if test="manageListBo.brandCode != null and manageListBo.brandCode.size > 0">
        and mhi.hotel_brand_code in
        <foreach collection="manageListBo.brandCode" index="index" item="item" open="(" separator="," close=")">
           #{item}
        </foreach>
      </if>
      <if test="manageListBo.deptCode != null and manageListBo.deptCode.size > 0">
        and mhi.hotel_ownership_code in
        <foreach collection="manageListBo.deptCode" index="index" item="item" open="(" separator="," close=")">
           #{item}
        </foreach>
      </if>
    </where>) x
    LEFT JOIN (
    SELECT
    max(msr.high_star) highStar,
    min(msr.low_star) lowStar,
    mml.user_id
    FROM
    mp_manager_list mml
    LEFT JOIN mod_hotel_info mhi ON mml.company_id = mhi.uc_company_id
    LEFT JOIN mp_star_rule_hotel ms ON mhi.hotel_id = ms.hotel_id
    LEFT JOIN mp_star_rule msr ON ms.rule_id = msr.id
    GROUP BY mml.user_id
    ) z ON z.user_id = x.userId
  </select>

  <select id="selectManagerUcIdListByCompanyId" resultType="java.lang.Integer">
    SELECT user_id
    from mp_manager_list
    where company_id = #{companyId}
    order by id desc
  </select>
</mapper>

