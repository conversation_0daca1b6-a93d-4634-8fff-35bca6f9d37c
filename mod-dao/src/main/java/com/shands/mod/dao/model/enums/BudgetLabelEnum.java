package com.shands.mod.dao.model.enums;

import lombok.Getter;

/**
 * 预算完成情况标签枚举
 */
@Getter
public enum BudgetLabelEnum {

  INCOME_LABEL("已完成营业收入"),
  TARGET_INCOME_LABEL("目标营业收入"),
  COMPLETION_LABEL("当前完成"),
  CARD_LABEL("企业卡数"),
  MEMBER_LABEL("APP新增会员数"),
  DIFF_LABEL("时间进度完成差值"),
  TIME_LABEL("时间进度"),

  MONTH_TITLE("预算完成情况"),
  QUARTERLY_TITLE("预算完成情况");

  private final String value;

  BudgetLabelEnum(String value) {
    this.value = value;
  }
}