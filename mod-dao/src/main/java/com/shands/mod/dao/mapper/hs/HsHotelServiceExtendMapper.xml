<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.hs.HsHotelServiceExtendMapper">
    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
        <!--@mbg.generated-->
        <!--@Table hs_hotel_service_extend-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="HOTEL_SERVICE_ID" jdbcType="INTEGER" property="hotelServiceId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="STATUS" jdbcType="TINYINT" property="status" />
        <result column="ACCEPT_DEPT" jdbcType="INTEGER" property="acceptDept"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId"/>
        <result column="VERSION" jdbcType="INTEGER" property="version"/>
        <result column="DELETED" jdbcType="TINYINT" property="deleted"/>
        <result column="CREATE_USER" jdbcType="INTEGER" property="createUser"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_USER" jdbcType="INTEGER" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
      <result column="user_id" property="userId" jdbcType="VARCHAR"/>
      <result column="copy_user_id" property="copyUserId" jdbcType="VARCHAR"/>
      <result column="code" jdbcType="VARCHAR" property="code"/>
      <result column="source" jdbcType="VARCHAR" property="source"/>
      <result column="expected_service_time" jdbcType="INTEGER" property="expectedServiceTime"/>
      <result column="employee_type_id" jdbcType="BIGINT" property="employeeTypeId"/>
      <result column="is_show_bdw" jdbcType="VARCHAR" property="isShowBdw"/>
      <result column="is_required" jdbcType="BIGINT" property="isRequired"/>
      <result column="shift_type" jdbcType="VARCHAR" property="shiftType"/>
      <result column="service_restrictions" jdbcType="VARCHAR" property="serviceRestrictions"/>
      <result column="employee_type_name" jdbcType="VARCHAR" property="employeeTypeName"/>
      <result column="service_classify_id" jdbcType="BIGINT" property="serviceClassifyId"/>
      <result column="service_classify_name" jdbcType="VARCHAR" property="serviceClassifyName"/>

    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, HOTEL_SERVICE_ID, `NAME`,STATUS, COMPANY_ID, GROUP_ID, VERSION, DELETED, CREATE_USER,
        CREATE_TIME, UPDATE_USER, UPDATE_TIME,ACCEPT_DEPT,user_id,copy_user_id,code,source,
        expected_service_time,employee_type_id,employee_type_name,service_classify_id,service_classify_name,is_show_bdw,is_required,shift_type,service_restrictions
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from hs_hotel_service_extend
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from hs_hotel_service_extend
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateDeleteByCompanyIdAndServiceId">
      update hs_hotel_service_extend set DELETED = 1
      where COMPANY_ID = #{companyId,jdbcType=INTEGER}
      and HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER}
    </update>

  <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.shands.mod.dao.model.hs.HsHotelServiceExtend"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into hs_hotel_service_extend (HOTEL_SERVICE_ID, `NAME`,STATUS, COMPANY_ID,
        GROUP_ID, VERSION, DELETED,
        CREATE_USER, CREATE_TIME, UPDATE_USER,
        UPDATE_TIME,user_id,copy_user_id)
        values (#{hotelServiceId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
        #{status,jdbcType=INTEGER},
        #{companyId,jdbcType=INTEGER},
        #{groupId,jdbcType=INTEGER}, #{version,jdbcType=INTEGER},
        #{deleted,jdbcType=TINYINT},
        #{createUser,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP},#{userId,jdbcType=VARCHAR}, #{copyUserId,jdbcType=VARCHAR},)
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.shands.mod.dao.model.hs.HsHotelServiceExtend"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into hs_hotel_service_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotelServiceId != null">
                HOTEL_SERVICE_ID,
            </if>
            <if test="name != null">
                `NAME`,
            </if>
            <if test="status !=null">
                 STATUS,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="groupId != null">
                GROUP_ID,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="deleted != null">
                DELETED,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
          <if test="acceptDept != null">
            ACCEPT_DEPT,
          </if>
          <if test="userId !=null">
            user_id,
          </if>
          <if test="copyUserId !=null">
            copy_user_id,
          </if>
          <if test="code !=null">
            code,
          </if>
          <if test="source !=null">
            source,
          </if>
          <if test="expectedServiceTime !=null">
            expected_service_time,
          </if>
          <if test="employeeTypeId !=null">
            employee_type_id,
          </if>
          <if test="employeeTypeName !=null">
            employee_type_name,
          </if>
          <if test="serviceClassifyId !=null">
            service_classify_id,
          </if>
          <if test="serviceClassifyName !=null">
          service_classify_name,
          </if>
          <if test="isShowBdw !=null">
            is_show_bdw,
          </if>
          <if test="isRequired !=null">
            is_required,
          </if>
          <if test="shiftType !=null">
            shift_type,
          </if>
          <if test="serviceRestrictions !=null">
            service_restrictions,
          </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hotelServiceId != null">
                #{hotelServiceId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="status !=null">
               #{status,jdbcType=INTEGER},
             </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
          <if test="acceptDept != null">
            #{acceptDept,jdbcType=INTEGER},
          </if>
          <if test="userId !=null">
            #{userId,jdbcType=VARCHAR},
          </if>
          <if test="copyUserId !=null">
            #{copyUserId,jdbcType=VARCHAR},
          </if>
          <if test="code !=null">
            #{code,jdbcType=VARCHAR},
          </if>
          <if test="source !=null">
            #{source,jdbcType=VARCHAR},
          </if>
          <if test="expectedServiceTime != null">
            #{expectedServiceTime, jdbcType=INTEGER},
          </if>
          <if test="employeeTypeId != null">
            #{employeeTypeId, jdbcType=BIGINT},
          </if>
          <if test="employeeTypeName != null">
            #{employeeTypeName, jdbcType=VARCHAR},
          </if>
          <if test="serviceClassifyId != null">
            #{serviceClassifyId, jdbcType=BIGINT},
          </if>
          <if test="serviceClassifyName != null">
            #{serviceClassifyName, jdbcType=VARCHAR},
          </if>
          <if test="isShowBdw !=null">
            #{isShowBdw, jdbcType=INTEGER},
          </if>
          <if test="isRequired !=null">
            #{isRequired, jdbcType=INTEGER},
          </if>
          <if test="shiftType !=null">
            #{shiftType, jdbcType=INTEGER},
          </if>
          <if test="serviceRestrictions !=null">
            #{serviceRestrictions, jdbcType=INTEGER},
          </if>
        </trim>
    </insert>
  <insert id="insertBatch">
    insert into hs_hotel_service_extend (HOTEL_SERVICE_ID, `NAME`,STATUS, COMPANY_ID,
                                         GROUP_ID, VERSION, DELETED,
                                         CREATE_USER, CREATE_TIME, UPDATE_USER,
                                         UPDATE_TIME,user_id,copy_user_id,code,source,
    expected_service_time,employee_type_id,employee_type_name,service_classify_id,service_classify_name) values
    <foreach collection="entities" item="entity" separator=",">
        (#{entity.hotelServiceId,jdbcType=INTEGER}, #{entity.name,jdbcType=VARCHAR},
            #{entity.status,jdbcType=INTEGER},
            #{entity.companyId,jdbcType=INTEGER},
            #{entity.groupId,jdbcType=INTEGER}, #{entity.version,jdbcType=INTEGER},
            #{entity.deleted,jdbcType=TINYINT},
            #{entity.createUser,jdbcType=INTEGER}, #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.updateUser,jdbcType=INTEGER},
            #{entity.updateTime,jdbcType=TIMESTAMP},#{entity.userId,jdbcType=VARCHAR}, #{entity.copyUserId,jdbcType=VARCHAR},
            #{entity.code,jdbcType=VARCHAR},#{entity.source,jdbcType=VARCHAR},
            #{entity.expectedServiceTime,jdbcType=INTEGER},#{entity.employeeTypeId,jdbcType=BIGINT},#{entity.employeeTypeName,jdbcType=VARCHAR},
            #{entity.serviceClassifyId,jdbcType=BIGINT},#{entity.serviceClassifyName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective"
            parameterType="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
        <!--@mbg.generated-->
        update hs_hotel_service_extend
        <set>
            <if test="hotelServiceId != null">
                HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `NAME` = #{name,jdbcType=VARCHAR},
            </if>
          <if test="status !=null">
            STATUS=#{status,jdbcType=INTEGER},
          </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                GROUP_ID = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                DELETED = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=INTEGER},
            </if>
          <if test="source != null">
            source = #{source,jdbcType=VARCHAR},
          </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
          <if test="acceptDept != null">
            ACCEPT_DEPT = #{acceptDept,jdbcType=INTEGER},
          </if>
          <if test="userId !=null">
            user_id = #{userId,jdbcType=VARCHAR},
          </if>
          <if test="copyUserId !=null">
            copy_user_id = #{copyUserId,jdbcType=VARCHAR},
          </if>
          <if test="code !=null">
            code = #{code,jdbcType=VARCHAR},
          </if>
          <if test="expectedServiceTime != null">
            expected_service_time = #{expectedServiceTime, jdbcType=INTEGER},
          </if>
          <if test="employeeTypeId != null">
            employee_type_id = #{employeeTypeId, jdbcType=BIGINT},
          </if>
          <if test="employeeTypeName != null">
            employee_type_name = #{employeeTypeName, jdbcType=VARCHAR},
          </if>
          <if test="serviceClassifyId != null">
            service_classify_id = #{serviceClassifyId, jdbcType=BIGINT},
          </if>
          <if test="serviceClassifyName != null">
            service_classify_name = #{serviceClassifyName, jdbcType=VARCHAR},
          </if>
          <if test="isShowBdw !=null">
            is_show_bdw = #{isShowBdw, jdbcType=INTEGER},
          </if>
          <if test="isRequired !=null">
            is_required = #{isRequired, jdbcType=INTEGER},
          </if>
          <if test="serviceRestrictions !=null">
            service_restrictions =  #{serviceRestrictions, jdbcType=INTEGER},
          </if>
          <if test="shiftType !=null">
            shift_type =  #{shiftType, jdbcType=VARCHAR},
          </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
        <!--@mbg.generated-->
        update hs_hotel_service_extend
        set HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER},
        `NAME` = #{name,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=INTEGER},
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
        GROUP_ID = #{groupId,jdbcType=INTEGER},
        VERSION = #{version,jdbcType=INTEGER},
        DELETED = #{deleted,jdbcType=TINYINT},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateDelete">
        update hs_hotel_service_extend set DELETED = 1
        where ID = #{hotelServiceExtendId,jdbcType=INTEGER}
    </update>
    <select id="findByHotelServiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hs_hotel_service_extend
        where DELETED = 0
        and status = 1
        and HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER}
    </select>

  <select id="findByHotelServiceIdAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from hs_hotel_service_extend
    where DELETED = 0
    <if test="status != null">
      AND status = #{status,jdbcType=INTEGER}
    </if>
    and HOTEL_SERVICE_ID = #{hotelServiceId,jdbcType=INTEGER}
    and company_id = #{companyId,jdbcType=INTEGER}
  </select>


    <select id="findByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hs_hotel_service_extend
        where FIND_IN_SET(ID,#{hotelServiceExtendId,jdbcType=VARCHAR})
    </select>
  <!--集团根据 company删除-->
  <update id="deletedByCompanyId">
     update hs_hotel_service_extend set DELETED = 1
     where company_id=#{companyId}
  </update>
  <!--将集团的服务子项修改-->
  <update id="updateExtend">
    UPDATE hs_hotel_service_extend
    SET NAME = #{newName}
    WHERE
	  NAME = #{oldName}
	  AND HOTEL_SERVICE_ID = #{hotelServiceId}
	  AND GROUP_ID =COMPANY_ID
  </update>
    <update id="updateNameByServiceClassifyId">
        UPDATE hs_hotel_service_extend
        SET service_classify_name = #{serviceClassifyName}
        WHERE
        DELETED = 0
        AND COMPANY_ID = #{companyId}
        AND service_classify_id = #{serviceClassifyId}
    </update>
  <update id="updateNameByEmployeeTypeId">
    UPDATE hs_hotel_service_extend
    SET employee_type_name = #{employeeTypeName}
    WHERE
    DELETED = 0
    AND COMPANY_ID = #{companyId}
    AND employee_type_id = #{employeeTypeId}
  </update>
  <!--添加下属机构的时候 集团所有的服务子项-->
  <select id="allExtendByGroupId" parameterType="Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    hs_hotel_service_extend
    where COMPANY_ID = #{companyId}
    and hotel_service_id = #{hotelServiceId}
    and DELETED=0
  </select>
  <!--德胧生态  根据服务id获取服务子项-->
  <select id="extendByServiceId" parameterType="com.shands.mod.dao.model.hs.HsHotelServiceExtend" resultType="com.shands.mod.dao.model.hs.HsHotelServiceExtendRes">
    SELECT
	      id as id,
	      code as code,
	      HOTEL_SERVICE_ID as hotelServiceId,
	      source as source,
	      NAME as name,
	      STATUS as status,
        ACCEPT_DEPT as acceptDept,
	      UPDATE_USER as updateUser,
	      UPDATE_TIME as updateTime,
        user_id as userId,
        copy_user_id copyUserId,
        expected_service_time as expectedServiceTime,
        employee_type_id as employeeTypeId,
        employee_type_name as employeeTypeName,
        service_classify_id as serviceClassifyId,
        service_classify_name as serviceClassifyName,
        is_show_bdw as isShowBdw,
        is_required as isRequired,
        shift_type as shiftType,
        service_restrictions as serviceRestrictions
    FROM
	      hs_hotel_service_extend
    WHERE
	      HOTEL_SERVICE_ID = #{hotelServiceId}
	  AND DELETED = 0
	  AND COMPANY_ID =  #{companyId}
    order by id desc
  </select>
  <!--创建工单中，保洁，维修服务子项-->
  <select id="extendByType" resultType="com.shands.mod.dao.model.res.hs.hotel.CleanAndRepairForWorkRes">
    SELECT id, name
    FROM hs_hotel_service_extend
    WHERE HOTEL_SERVICE_ID = #{hotelServiceId}
      AND DELETED = 0
      AND STATUS = 1
  </select>

  <select id="findExtendsByList" parameterType="com.shands.mod.dao.model.hs.HsHotelServiceExtend" resultType="com.shands.mod.dao.model.hs.HsHotelServiceExtendRes">
    SELECT
    id as id,
    HOTEL_SERVICE_ID as hotelServiceId,
    NAME as name,
    STATUS as status,
    UPDATE_USER as updateUser,
    UPDATE_TIME as updateTime
    FROM
    hs_hotel_service_extend
    WHERE
      id
    IN
    <foreach item="item" collection="extendIds" separator="," open="(" close=")" index="">
      (#{item})
    </foreach>
    AND DELETED = 0
    order by id desc
  </select>

  <select id="extendByCompany" resultType="com.shands.mod.dao.model.res.hs.staff.ExtendRes">
    SELECT id id, id extendId,name extendName from hs_hotel_service_extend where COMPANY_ID = #{companyId} and HOTEL_SERVICE_ID = #{hotelServiceId}
  </select>

  <!--查询受理部门-->
  <select id="getDept" resultType="java.lang.Integer">
    SELECT
	a.ACCEPT_DEPT
FROM
	hs_hotel_service_extend a
	LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.id
WHERE
	a.COMPANY_ID = #{companyId}
	AND a.id = #{extendId}
	AND a.deleted = 0
	AND b.DELETED = 0
	AND b.SERVICE_TYPE = #{serviceType}
  </select>

  <!--服务子项，下单最多排序-->
  <select id="serviceContent" resultType="com.shands.mod.dao.model.res.hs.hotel.ServiceContentRes">
    SELECT
	hh.id id,
	hh.`NAME` name,
	hh.id extendId,
IF
	( ww.id > 0, ( count( 1 ) ), 0 ) AS cc
FROM
	hs_hotel_service_extend hh
	LEFT JOIN ( SELECT * FROM hs_work_order WHERE CREATE_TIME >= DATE_SUB( CURDATE( ), INTERVAL 90 DAY ) AND HOTEL_SERVICE_ID = #{hotelServiceId}) ww ON hh.id = ww.HOTEL_SERVICE_EXTEND_ID
WHERE
  hh.DELETED = 0 and
	hh.HOTEL_SERVICE_ID = #{hotelServiceId}
	and hh.`STATUS` =1
GROUP BY
	hh.id
ORDER BY
	cc DESC
  </select>

  <!--服务子项默认受理人-->
  <select id="getUser" resultType="java.lang.String">
    SELECT
	a.user_id
FROM
	hs_hotel_service_extend a
	LEFT JOIN hs_hotel_service b ON a.HOTEL_SERVICE_ID = b.id
WHERE
	a.COMPANY_ID = #{companyId}
	AND a.id = #{extendId}
	AND a.deleted = 0
	AND b.DELETED = 0
	AND b.SERVICE_TYPE = #{serviceType}
  </select>
  <select id="getLastCode" resultType="java.lang.String">
    SELECT
      code
    FROM
      hs_hotel_service_extend
    WHERE
      code like 'SE%' order by code desc limit 1;
  </select>
  <select id="getByCode" resultType="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
    select
    <include refid="Base_Column_List"/>
    from hs_hotel_service_extend
    where DELETED = 0
    and COMPANY_ID = #{companyId}
    and code = #{code}
  </select>

  <select id="getByName" resultType="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
    select
      id as id,
      code as code,
      HOTEL_SERVICE_ID as hotelServiceId,
      source as source,
      NAME as name,
      STATUS as status,
      ACCEPT_DEPT as acceptDept,
      UPDATE_USER as updateUser,
      UPDATE_TIME as updateTime,
      user_id as userId,
      copy_user_id copyUserId,
      expected_service_time as expectedServiceTime,
      employee_type_id as employeeTypeId,
      employee_type_name as employeeTypeName,
      service_classify_id as serviceClassifyId,
      service_classify_name as serviceClassifyName,
      is_show_bdw as isShowBdw,
      is_required as isRequired,
      shift_type as shiftType,
      service_restrictions as serviceRestrictions
    from hs_hotel_service_extend
    where DELETED = 0
    and COMPANY_ID = #{companyId}
    and name = #{name}
  </select>


  <select id="getByServiceClassifyId" resultType="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
    select
    <include refid="Base_Column_List"/>
    from hs_hotel_service_extend
    where DELETED = 0
    and COMPANY_ID = #{companyId}
    and service_classify_id = #{serviceClassifyId}
  </select>
  <select id="getByEmployeeTypeId"
          resultType="com.shands.mod.dao.model.hs.HsHotelServiceExtend">
    select
    <include refid="Base_Column_List"/>
    from hs_hotel_service_extend
    where DELETED = 0
    and COMPANY_ID = #{companyId}
    and employee_type_id = #{employeeTypeId}
  </select>

</mapper>