package com.shands.mod.dao.model.sales.tool.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 销售员订单详情
 * @Author: wj
 * @Date: 2024/8/13 17:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderInfoRes extends OrderListRes{

  @ApiModelProperty(value = "早餐数量")
  private Integer breakfastNum;

  @ApiModelProperty(value = "早餐名称（无早、单早、双早）")
  private String bkName;

  @ApiModelProperty(value = "预订人手机号")
  private String bookerPhone;

  @ApiModelProperty(value = "预订人手机区号")
  private String bookerPhoneAreaCode;

  @ApiModelProperty(value = "订单金额")
  private Integer totalPrice;

  @ApiModelProperty(value = "支付方式:0预付 1到店付 2现付或预付")
  private Integer paymentType;

  @ApiModelProperty(value = "支付方式标签")
  private String paymentTypeLabel;

  @ApiModelProperty(value = "付款状态: 0未付款 1已付款")
  private Integer payStatus;

  @ApiModelProperty(value = "付款状态标签")
  private String payStatusLabel;

  @ApiModelProperty(value = "销售员姓名")
  private String salesName;

  @ApiModelProperty(value = "间夜")
  private Integer nights;

  @ApiModelProperty(value = "备注")
  private String remark;

  @ApiModelProperty(value = "能否取消")
  private boolean cancelable;


}
