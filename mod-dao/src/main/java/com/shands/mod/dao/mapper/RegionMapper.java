package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.Region;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RegionMapper {
  int deleteByPrimaryKey(String id);

  int insert(Region record);

  int insertSelective(Region record);

  Region selectByPrimaryKey(String id);

  int updateByPrimaryKeySelective(Region record);

  int updateByPrimaryKey(Region record);

  /**
   * 通过pid查询地区
   *
   * @param pId 空为全部
   * @return
   */
  List<Region> listByPId(@Param("pId") String pId);
}
