package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.PackageWriteOffRecord;
import com.shands.mod.dao.model.PackageWriteOffRecordExample;
import com.shands.mod.dao.model.req.hs.report.GroupByName;
import com.shands.mod.dao.model.req.hs.report.PackageDeatilRes;
import com.shands.mod.dao.model.req.hs.report.StatisticsAppReq;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PackageWriteOffRecordMapper {
    int countByExample(PackageWriteOffRecordExample example);

    int deleteByExample(PackageWriteOffRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(PackageWriteOffRecord record);

    int insertSelective(PackageWriteOffRecord record);

    List<PackageWriteOffRecord> selectByExample(PackageWriteOffRecordExample example);

    PackageWriteOffRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") PackageWriteOffRecord record, @Param("example") PackageWriteOffRecordExample example);

    int updateByExample(@Param("record") PackageWriteOffRecord record, @Param("example") PackageWriteOffRecordExample example);

    int updateByPrimaryKeySelective(PackageWriteOffRecord record);

    int updateByPrimaryKey(PackageWriteOffRecord record);

    //包价核销记录
    List<GroupByName> groupByName(@Param("statisticsAppReq") StatisticsAppReq statisticsAppReq);

    //包价核销明细
     List<PackageDeatilRes> packageDetail(@Param("statisticsAppReq") StatisticsAppReq statisticsAppReq);

}