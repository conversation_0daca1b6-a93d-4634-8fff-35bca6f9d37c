package com.shands.mod.dao.model.workorder.enums;

/**
 * <AUTHOR>
 * @Description //TODO 流转名称
 * @date 2022/5/13 14:00
 * @Param
 * @return
 **/
public enum FlowRecordEnum {

  START("START", "开始"),

  CHANGE("CHANGE", "信息变更"),

  SYSTEMFLOW("SYSTEMFLOW", "系统流转"),

  FORWARD("FORWARD","转交他人"),

  ACCEPT("ACCEPT","同意接单"),

  REFUSE("REFUSE","拒绝接单"),

  TIMEOUT("TIMEOUT","超时接单"),
  
  NODE("NODE", "节点已处理"),

  CLOSED("CLOSED", "已关闭"),

  FINISHED("FINISHED", "已完成");

  FlowRecordEnum(String code, String detail) {
    this.code = code;
    this.detail = detail;
  }

  private String code;

  private String detail;

  public String getCode() {
    return code;
  }

  public String getDetail() {
    return detail;
  }
}
