package com.shands.mod.dao.mapper.fw;

import com.shands.mod.dao.model.fw.CarService;
import com.shands.mod.dao.model.req.fw.CarQueryReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CarServiceMapper {

  /**
   * delete by primary key
   *
   * @param id primaryKey
   * @return deleteCount
   */
  int deleteByPrimaryKey(Integer id);

  /**
   * insert record to table
   *
   * @param record the record
   * @return insert count
   */
  int insert(CarService record);

  /**
   * insert record to table selective
   *
   * @param record the record
   * @return insert count
   */
  int insertSelective(CarService record);

  /**
   * select by primary key
   *
   * @param id primary key
   * @return object by primary key
   */
  CarService selectByPrimaryKey(Integer id);

  /**
   * update record selective
   *
   * @param record the updated record
   * @return update count
   */
  int updateByPrimaryKeySelective(CarService record);

  /**
   * update record
   *
   * @param record the updated record
   * @return update count
   */
  int updateByPrimaryKey(CarService record);

  List<CarService> All(@Param("carQueryReq") CarQueryReq carQueryReq);
}
