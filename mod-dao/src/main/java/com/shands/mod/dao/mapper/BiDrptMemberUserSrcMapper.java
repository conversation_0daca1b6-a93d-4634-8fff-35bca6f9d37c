package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.req.elsreport.MemberDevelopRankReq;
import com.shands.mod.dao.model.res.elsreport.MemberDevelopRankRes;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * (BiDrptMemberUserSrc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-13 16:42:30
 */
public interface BiDrptMemberUserSrcMapper {

  List<MemberDevelopRankRes> selectListByData (@Param("memberDevelopRankReq") MemberDevelopRankReq memberDevelopRankReq);


  int findGroupTradeRevenueCount(@Param("bizDate") Date bizDate,@Param("codes") List<String> codes);

  /**
   * 查询最新数据日期
   * @return
   */
  Date findMaxDate();
}

