package com.shands.mod.dao.model.voucher.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("法宝模板基础信息")
public class BasicVoucherTemplateRes {

  /**
   * APP构建的虚拟模板id
   */
  private String templateId;

  /**
   * 模板名称
   */
  private String templateName;

  /**
   * 类型
   */
  private String ticketTypeEnum;

  /**
   * icon
   */
  private String imageUrl;

  /**
   * 使用规则
   */
  private String useRule;

  /**
   * 详情
   */
  private String description;


  private String periodName;

  @ApiModelProperty(value = "抵扣金额(分)")
  private Integer worthPrice;


}
