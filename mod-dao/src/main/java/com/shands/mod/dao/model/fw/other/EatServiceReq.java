package com.shands.mod.dao.model.fw.other;

import com.shands.mod.dao.model.res.hs.sell.SellRes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 创建餐饮服务的请求参数 */
@Data
@NoArgsConstructor
@ApiModel
public class EatServiceReq {

  /** 用餐人数 */
  @ApiModelProperty(value = "用餐人数")
  private Integer eatingPeople;

  /** 用餐方式 堂食 送到房间 */
  @ApiModelProperty(value = "用餐方式")
  private String diningStyle;

  /** 具体物品 */
  @ApiModelProperty(value = "具体物品")
  private List<SellRes> resList;

}
