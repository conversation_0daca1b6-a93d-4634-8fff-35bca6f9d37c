package com.shands.mod.dao.model.workorder.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("发送MQ请求")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendWorkTaskBo {

  @NotBlank(message = "员工id")
  private Integer userId;

  @ApiModelProperty("消息标题")
  private String title;

  @ApiModelProperty("消息内容")
  private String content;

  @ApiModelProperty("消息跳转类型")
  private String messageType;

  @ApiModelProperty("跳转地址")
  private String url;

  @ApiModelProperty("工单ID或节点ID")
  private Integer workId;

  @ApiModelProperty("事件")
  private String type;

  @ApiModelProperty("工单ID")
  private Integer workMessageId;

  @ApiModelProperty("超时时间")
  private Integer time;
}
