package com.shands.mod.dao.model.quality.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("添加维度权重配置请求参数")
@Data
public class AddCheckWeightsBo {

  /**
   * 维度类型id
   */
  @ApiModelProperty("维度类型id")
  private Integer dimensionTypeId;
  /**
   * 权重类型id
   */

  @ApiModelProperty("权重类型type")
  private String weightsType;
  /**
   * 权重值
   */
  @ApiModelProperty("权重值")
  private String weightsValue;
  /**
   * 核心标识
   */
  @ApiModelProperty("核心标识")
  private Boolean ifCore;

  @ApiModelProperty("维度名称")
  private String  dimensionTypeName;
}
