package com.shands.mod.dao.model.v0701.dto;

import com.shands.mod.dao.model.res.hs.crm.PracticeRes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-05-23
 * @description 菜品dto
 */

@Data
public class PosPluDto {

  @ApiModelProperty(value = "id")
  private Integer pluId;

  @ApiModelProperty(value = "菜项代码")
  private String pluCode;

  @ApiModelProperty(value = "菜项名称")
  private String pluName;

  @ApiModelProperty(value = "价格")
  private BigDecimal price;

  @ApiModelProperty(value = "图片地址")
  private String pluPhotoUrl;

  @ApiModelProperty(value = "单位")
  private String unit;

  @ApiModelProperty("数量")
  private Integer num;

  @ApiModelProperty("数据报表项")
  private String toCode;

  @ApiModelProperty("分类code")
  private String sortCode;

  @ApiModelProperty("菜本code")
  private String noteCode;

  @ApiModelProperty("菜品做法")
  private PracticeRes practiceRes;

  @ApiModelProperty("否打折菜 T：是  F：否")
  private String discountFlag;

  private String specs;
  private List<String> specIds;
}
