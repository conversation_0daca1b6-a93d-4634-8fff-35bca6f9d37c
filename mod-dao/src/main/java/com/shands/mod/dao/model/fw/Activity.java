package com.shands.mod.dao.model.fw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动表
 *
 * <AUTHOR>
 */
@ApiModel(value = "com-shands-mod-dao-model-fw-Activity")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Activity {

  @ApiModelProperty(value = "")
  private Integer id;

  /** 活动编码 */
  @ApiModelProperty(value = "活动编码")
  private String no;

  /** 活动名 */
  @ApiModelProperty(value = "活动名")
  private String activityName;

  /** 活动类型id */
  @ApiModelProperty(value = "活动类型id,")
  private Integer activityClassId;

  /** 活动描述 */
  @ApiModelProperty(value = "活动描述")
  private String activityDesc;

  /** 活动地点 */
  @ApiModelProperty(value = "活动地点")
  private String location;

  /** 列表图 */
  @ApiModelProperty(value = "列表图")
  private String picUrl;

  /** 主图 */
  @ApiModelProperty(value = "主图")
  private String mainImg;

  /** 活动备注 */
  @ApiModelProperty(value = "活动备注")
  private String remark;

  @ApiModelProperty(value = "")
  private Integer company;

  @ApiModelProperty(value = "")
  private Boolean delete;

  /** 1.上架 2.下架 */
  @ApiModelProperty(value = "1.上架 2.下架")
  private Byte status;

  @ApiModelProperty(value = "")
  private LocalDateTime createTime;

  @ApiModelProperty(value = "")
  private Integer createUser;

  @ApiModelProperty(value = "")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "")
  private Integer updateUser;

  @ApiModelProperty(value = "年龄要求，区间用-隔开")
  private String requireAge;

  @ApiModelProperty(value = "不适宜人群")
  private String unsuitabilityMan;

  @ApiModelProperty(value = "活动标签,多个逗号隔开")
  private String activityTag;

  @ApiModelProperty(value = "是否需要预约 0-不需要 1-需要")
  private Integer isBook;

  @ApiModelProperty(value = "活动介绍")
  private String activityIntroduction;

  @ApiModelProperty(value = "预约须知")
  private String bookNotice;


  @ApiModelProperty(value = "排序")
  private Integer sort;

  @ApiModelProperty(value = "热度基数")
  private Integer heatBase;

  @ApiModelProperty(value = "热度数量")
  private Integer heatCount;

  @ApiModelProperty(value = "是否推荐活动 0-不推荐 1-推荐")
  private Integer isRecommend;

  @ApiModelProperty("预约方式 0-在线预约 1-电话预约")
  private Integer bookWay;

  @ApiModelProperty("预约电话")
  private String bookPhone;

  @ApiModelProperty("活动场次开始后是否可以继续预约 0-不可以 1-可以")
  private Integer startedBook;


}
