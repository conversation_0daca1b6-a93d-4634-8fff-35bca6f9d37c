package com.shands.mod.dao.model.res.hs.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("计划卫生房间信息")
public class PlanTaskByItemRes {

  @ApiModelProperty("清扫任务id")
  private Integer cleanTaskId;

  @ApiModelProperty("入住时间")
  private Date arrivalTime;

  @ApiModelProperty("退房时间")
  private Date leaveTime;

  @ApiModelProperty("房间号")
  private String roomNo;

  @ApiModelProperty("是否将离(T:是/F:否)")
  private String isDep;

  @ApiModelProperty("无法检查")
  private String notCheck;

  @ApiModelProperty("房态")
  private String roomSta;

  @ApiModelProperty(value = "是否会员(T:是/F:否)")
  private String isMember;

  @ApiModelProperty(value = "是否预抵(T:是/F:否)")
  private String isArr;

  @ApiModelProperty(value = "固定项目 OOO=维修 OOS=锁定 TMP=临时态")
  private String chgType;

  @ApiModelProperty(value = "F=散客 G=团队 H=消费帐户")
  private String rsvClass;

  @ApiModelProperty(value = "是否保密 0 不保密1 免打扰 2 免查询 3 免查询&免打扰  4 掩护电话")
  private String isSecret;

  private List<PlanTaskByItemForProgramRes> planTasks;

}
