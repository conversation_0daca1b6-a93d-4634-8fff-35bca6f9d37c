package com.shands.mod.dao.model.sales.tool.domain;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 企业申请记录
 * @TableName mod_company_apply_record
 */
@Data
public class ModCompanyApplyRecord implements Serializable {
  /**
   * 自增主键id
   */
  private Long id;

  /**
   * 申请人通宝id
   */
  private Long ucId;

  /**
   * 发展酒店code
   */
  private String hotelCode;

  /**
   * 营业执照url
   */
  private String bizLicenseImg;

  /**
   * 纳税人识别号
   */
  private String taxpayerId;

  /**
   * 企业名称
   */
  private String companyName;

  /**
   * 企业地址
   */
  private String companyAddress;

  /**
   * 企业联系人
   */
  private String linkman;

  /**
   * 企业联系人手机号
   */
  private String mobile;

  /**
   * 企业联系人邮箱
   */
  private String email;

  /**
   * 企业邮箱后缀
   */
  private String emailSuffix;

  /**
   * 申请结果（0:失败 1: 成功）
   */
  private Integer applyRes;

  /**
   * 失败原因
   */
  private String failReason;

  /**
   * 备注
   */
  private String remark;

  /**
   * 创建时间（申请时间）
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createTime;

  /**
   * 更新时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updateTime;

  /**
   * 逻辑删除标志（0:未删除 1:删除）
   */
  private Integer delFlag;

  private static final long serialVersionUID = 1L;
}