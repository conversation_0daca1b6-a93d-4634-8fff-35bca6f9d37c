<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.board.ModUserDataDetailsMapper">

    <resultMap type="com.shands.mod.dao.model.board.ModUserDataDetails" id="ModUserDataDetailsMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="boardId" column="board_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ModUserDataDetailsMap">
        select
          id, board_id, user_id, create_time, update_time
        from mod_user_data_details
        where id = #{id}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mod_user_data_details
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="boardId != null">
                and board_id = #{boardId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
             <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mod_user_data_details(board_id, user_id,sort, create_time, update_time)
        values (#{boardId}, #{userId},#{sort}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_user_data_details(board_id, user_id,sort,type, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.boardId}, #{entity.userId},#{entity.sort},#{entity.type}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_user_data_details(board_id, user_id,sort, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.boardId}, #{entity.userId},#{entity.sort}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        board_id = values(board_id),
        user_id = values(user_id),
         sort = values(sort),
        create_time = values(create_time),
        update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mod_user_data_details
        <set>
            <if test="boardId != null">
                board_id = #{boardId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
          <if test="sort != null">
            sort = #{sort},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mod_user_data_details where id = #{id}
    </delete>

  <select id="findPersonalDetailList" resultType="com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo">
    SELECT a.name as moduleName, a.code as moduleCode, a.id,a.desc_name as descName,b.sort,a.unit,
      (SELECT code from mod_new_data_board where id = a.p_id) as pCode
    from mod_new_data_board a
           LEFT JOIN mod_user_data_details b ON a.id = b.board_id
    where b.user_id = #{userId} and a.mechanism_type = #{type} ORDER BY sort;
  </select>

  <delete id="deleteByUserId">
    delete from mod_user_data_details where user_id = #{userId} and type = #{type}
  </delete>

  <select id="queryByUserIdAndType" resultMap="ModUserDataDetailsMap">
    select
      *
    from mod_user_data_details
    where user_id = #{userId} and type = #{type}
  </select>
</mapper>
