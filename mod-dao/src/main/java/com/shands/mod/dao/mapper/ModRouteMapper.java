package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.ModRoute;
import java.util.List;
import java.util.Map;

public interface ModRouteMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(ModRoute record);

  int insertSelective(ModRoute record);

  ModRoute selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(ModRoute record);

  int updateByPrimaryKey(ModRoute record);

  List<ModRoute> query();

  List<ModRoute> reason(Map map);

  List<ModRoute> invoiceRoute();
}