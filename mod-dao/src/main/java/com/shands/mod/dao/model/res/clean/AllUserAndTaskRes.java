package com.shands.mod.dao.model.res.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("清扫人员及任务")
public class AllUserAndTaskRes {

  @ApiModelProperty("清扫人名称")
  private String cleanUserName;

  @ApiModelProperty("清扫人")
  private Integer cleanUser;

  @ApiModelProperty("已分配**间")
  private Integer allotNum = 0;

  @ApiModelProperty("1加班 0正常班")
  private Integer overtime;

  @ApiModelProperty("房间")
  private List<Rooms> rooms;
}
