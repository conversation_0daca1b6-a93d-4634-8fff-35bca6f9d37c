package com.shands.mod.dao.model.training.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询课程合集详情列表 出参")
public class TrainingCenterDetailsVo {

  @ApiModelProperty("课程标题")
  private String name;

  @ApiModelProperty("课程url")
  private String fileUrl;

  @ApiModelProperty("封面")
  private String imageUrl;

  @ApiModelProperty("课程、合集id")
  private Integer tempId;

  @ApiModelProperty("学习人数")
  private Integer learningNum;

  @ApiModelProperty("排序")
  private Integer sort;

  @ApiModelProperty("课程类型")
  private String type;

  @ApiModelProperty("状态")
  private Integer status;

  @ApiModelProperty("分组类型")
  private String moduleType;

  @ApiModelProperty("课程创建时间")
  private String createTime;

}
