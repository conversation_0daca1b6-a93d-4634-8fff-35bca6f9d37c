package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @createDate 2020/12/22 下午3:17 <br>
 * @see com.shands.mod.dao.model.res <br>
 */
@ApiModel("活动订单二维码")
public class ActivityOrderQRcodeRes {

  @ApiModelProperty("活动订单id")
  private Integer activityOrderId;

  @ApiModelProperty("二维码类型:activityWritOff-活动核销， ")
  private String type;

  @ApiModelProperty("服务类型")
  private String serviceType;

  @ApiModelProperty("价格")
  private BigDecimal totalprice;


  public String getServiceType() {
    return serviceType;
  }

  public void setServiceType(String serviceType) {
    this.serviceType = serviceType;
  }

  public BigDecimal getTotalprice() {
    return totalprice;
  }

  public void setTotalprice(BigDecimal totalprice) {
    this.totalprice = totalprice;
  }

  public Integer getActivityOrderId() {
    return activityOrderId;
  }

  public void setActivityOrderId(Integer activityOrderId) {
    this.activityOrderId = activityOrderId;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }
}
