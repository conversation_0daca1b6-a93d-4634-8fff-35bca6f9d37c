<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.MessageMapper">
    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.Message">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="QUEUE_ID" jdbcType="INTEGER" property="queueId"/>
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="SEND_TYPE" jdbcType="VARCHAR" property="sendType"/>
        <result column="MESSAGE_TYPE" jdbcType="VARCHAR" property="messageType"/>
        <result column="MESSAGE_SUBTYPE" jdbcType="VARCHAR" property="messageSubtype"/>
        <result column="RECEIVER" jdbcType="VARCHAR" property="receiver"/>
        <result column="RECEIVER_USER" jdbcType="INTEGER" property="receiverUser"/>
        <result column="RECEIVER_TYPE" jdbcType="TINYINT" property="receiverType"/>
        <result column="NODE_ID" jdbcType="VARCHAR" property="nodeId"/>
        <result column="STATUS" jdbcType="TINYINT" property="status"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId"/>
        <result column="VERSION" jdbcType="INTEGER" property="version"/>
        <result column="DELETED" jdbcType="TINYINT" property="deleted"/>
        <result column="CREATE_USER" jdbcType="INTEGER" property="createUser"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_USER" jdbcType="INTEGER" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.shands.mod.dao.model.Message">
        <result column="CONTENT" jdbcType="LONGVARCHAR" property="content"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, QUEUE_ID, SEND_TIME, SEND_TYPE, MESSAGE_TYPE, MESSAGE_SUBTYPE, RECEIVER, RECEIVER_USER, RECEIVER_TYPE
    NODE_ID, STATUS, COMPANY_ID, GROUP_ID, VERSION, DELETED, CREATE_USER, CREATE_TIME, 
    UPDATE_USER, UPDATE_TIME
  </sql>
    <sql id="Blob_Column_List">
    CONTENT
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from mod_message
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from mod_message
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.shands.mod.dao.model.Message" keyProperty="id" useGeneratedKeys="true">
        insert into mod_message (ID, QUEUE_ID, SEND_TIME,
        SEND_TYPE, MESSAGE_TYPE, MESSAGE_SUBTYPE,
        RECEIVER, RECEIVER_USER, RECEIVER_TYPE, NODE_ID,
        STATUS, COMPANY_ID, GROUP_ID,
        VERSION, DELETED, CREATE_USER,
        CREATE_TIME, UPDATE_USER, UPDATE_TIME,
        CONTENT)
        values (#{id,jdbcType=INTEGER}, #{queueId,jdbcType=INTEGER}, #{sendTime,jdbcType=TIMESTAMP},
        #{sendType,jdbcType=VARCHAR}, #{messageType,jdbcType=VARCHAR}, #{messageSubtype,jdbcType=VARCHAR},
        #{receiver,jdbcType=VARCHAR}, #{receiverUser,jdbcType=INTEGER}, #{receiverType,jdbcType=TINYINT},
        #{nodeId,jdbcType=VARCHAR},
        #{status,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER},
        #{version,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createUser,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{content,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.shands.mod.dao.model.Message" useGeneratedKeys="true" keyColumn="id"
            keyProperty="id">
        insert into mod_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="queueId != null">
                QUEUE_ID,
            </if>
            <if test="sendTime != null">
                SEND_TIME,
            </if>
            <if test="sendType != null">
                SEND_TYPE,
            </if>
            <if test="messageType != null">
                MESSAGE_TYPE,
            </if>
            <if test="messageSubtype != null">
                MESSAGE_SUBTYPE,
            </if>
            <if test="receiver != null">
                RECEIVER,
            </if>
            <if test="receiverUser != null">
                RECEIVER_USER,
            </if>
            <if test="receiverType != null">
                RECEIVER_TYPE,
            </if>
            <if test="nodeId != null">
                NODE_ID,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="groupId != null">
                GROUP_ID,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="deleted != null">
                DELETED,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="content != null">
                CONTENT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="queueId != null">
                #{queueId,jdbcType=INTEGER},
            </if>
            <if test="sendTime != null">
                #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sendType != null">
                #{sendType,jdbcType=VARCHAR},
            </if>
            <if test="messageType != null">
                #{messageType,jdbcType=VARCHAR},
            </if>
            <if test="messageSubtype != null">
                #{messageSubtype,jdbcType=VARCHAR},
            </if>
            <if test="receiver != null">
                #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="receiverUser != null">
                #{receiverUser,jdbcType=INTEGER},
            </if>
            <if test="receiverType != null">
                #{receiverType,jdbcType=TINYINT},
            </if>
            <if test="nodeId != null">
                #{nodeId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.Message">
        update mod_message
        <set>
            <if test="queueId != null">
                QUEUE_ID = #{queueId,jdbcType=INTEGER},
            </if>
            <if test="sendTime != null">
                SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sendType != null">
                SEND_TYPE = #{sendType,jdbcType=VARCHAR},
            </if>
            <if test="messageType != null">
                MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR},
            </if>
            <if test="messageSubtype != null">
                MESSAGE_SUBTYPE = #{messageSubtype,jdbcType=VARCHAR},
            </if>
            <if test="receiver != null">
                RECEIVER = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="receiverUser != null">
                RECEIVER_USER = #{receiverUser,jdbcType=INTEGER},
            </if>
            <if test="receiverType != null">
                RECEIVER_TYPE= #{receiverType,jdbcType=TINYINT},
            </if>
            <if test="nodeId != null">
                NODE_ID = #{nodeId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=TINYINT},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                GROUP_ID = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                DELETED = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null">
                CONTENT = #{content,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.shands.mod.dao.model.Message">
        update mod_message
        set QUEUE_ID = #{queueId,jdbcType=INTEGER},
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
        SEND_TYPE = #{sendType,jdbcType=VARCHAR},
        MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR},
        MESSAGE_SUBTYPE = #{messageSubtype,jdbcType=VARCHAR},
        RECEIVER = #{receiver,jdbcType=VARCHAR},
        RECEIVER_USER = #{receiverUser,jdbcType=INTEGER},
        RECEIVER_TYPE = #{receiverType,jdbcType=TINYINT},
        NODE_ID = #{nodeId,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=TINYINT},
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
        GROUP_ID = #{groupId,jdbcType=INTEGER},
        VERSION = #{version,jdbcType=INTEGER},
        DELETED = #{deleted,jdbcType=TINYINT},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        CONTENT = #{content,jdbcType=LONGVARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.Message">
        update mod_message
        set QUEUE_ID = #{queueId,jdbcType=INTEGER},
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
        SEND_TYPE = #{sendType,jdbcType=VARCHAR},
        MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR},
        MESSAGE_SUBTYPE = #{messageSubtype,jdbcType=VARCHAR},
        RECEIVER = #{receiver,jdbcType=VARCHAR},
        RECEIVER_USER = #{receiverUser,jdbcType=INTEGER},
        RECEIVER_TYPE = #{receiverType,jdbcType=TINYINT},
        NODE_ID = #{nodeId,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=TINYINT},
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
        GROUP_ID = #{groupId,jdbcType=INTEGER},
        VERSION = #{version,jdbcType=INTEGER},
        DELETED = #{deleted,jdbcType=TINYINT},
        CREATE_USER = #{createUser,jdbcType=INTEGER},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <!-- 自定义SQL -->
    <select id="listByReceiver" parameterType="com.shands.mod.dao.model.Message" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from mod_message
        where DELETED=0 and RECEIVER_USER = #{receiverUser,jdbcType=INTEGER}
        <if test="companyId!=null">
            and COMPANY_ID = #{companyId,jdbcType=INTEGER}
        </if>
        <if test="queueId!=null">
            and QUEUE_ID = #{queueId,jdbcType=INTEGER}
        </if>
        <if test="sendType!=null and sendType!=''">
            and SEND_TYPE = #{sendType,jdbcType=VARCHAR}
        </if>
        <if test="messageType!=null and messageType!=''">
            and MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR}
        </if>
        <if test="messageSubtype!=null and messageSubtype!=''">
            and MESSAGE_SUBTYPE = #{messageSubtype,jdbcType=VARCHAR}
        </if>
        <if test="receiverType!=null and receiverType!=''">
            and RECEIVER_TYPE = #{receiverType,jdbcType=TINYINT}
        </if>
        <if test="status!=null">
            and STATUS = #{status,jdbcType=TINYINT}
        </if>
    </select>
</mapper>