package com.shands.mod.dao.model.mp.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 工单配置字段详情表(MpAppraisalRecord)实体类
 *
 * <AUTHOR>
 * @since 2022-11-03 11:22:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ApiModel("评分记录详情参数")
public class StarUpdateBo {

  @ApiModelProperty("方案编号")
  private String taskCode;

  @ApiModelProperty("userId")
  private Integer userId;

  @ApiModelProperty("星级")
  private Integer star;

  @ApiModelProperty("内容")
  private String remark;

  @ApiModelProperty("图片")
  private String picture;
}

