package com.shands.mod.dao.model.quality.vo.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("整改任务默认配置响应参数")
public class DefaultRecipientVo {

  @ApiModelProperty("列表参数")
  private List<DefaultRecipientListVo>  defaultRecipientListVoList;

  @ApiModelProperty("默认任务时长")
  private Integer defaultTaskTime;

  @ApiModelProperty("默认优先级")
  private String defaultPriority;

  @ApiModelProperty("整改人")
  private List<RectificationUserVo>  rectificationPeople;

  @ApiModelProperty("复查人")
  private List<RectificationUserVo> reviewPeople;
}
