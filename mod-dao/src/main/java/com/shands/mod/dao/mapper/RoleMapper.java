package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.Role;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RoleMapper {
  int deleteByPrimaryKey(Integer id);

  int insert(Role record);

  int insertSelective(Role record);

  Role selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(Role record);

  int updateByPrimaryKeyWithBLOBs(Role record);

  int updateByPrimaryKey(Role record);

  /**
   * 角色列表搜索
   *
   * @param data 查询条件
   * @return
   */
  List<Role> listByCompanyId(@Param("data") Map<String, Object> data);

  /**
   * 统计角色名是否重复
   *
   * @return
   */
  int countByName(Role record);
}
