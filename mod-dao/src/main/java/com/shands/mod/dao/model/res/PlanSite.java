package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/** <AUTHOR> */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class PlanSite {
  @ApiModelProperty("id")
  private Integer id;

  @ApiModelProperty("方案id")
  private Integer planId;

  @ApiModelProperty("父id")
  private Integer pId;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("排序")
  private Integer orderNumber;

  @ApiModelProperty("子站点")
  private List<PlanSite> children;

  @ApiModelProperty("站点节点列表")
  private List<SiteNode> siteNodes;

  public List<PlanSite> getChildren() {
    if (null == children) {
      children = new ArrayList<>();
    }
    return children;
  }

  public List<SiteNode> getSiteNodes() {
    if (null == siteNodes) {
      siteNodes = new ArrayList<>();
    }
    return siteNodes;
  }
}
