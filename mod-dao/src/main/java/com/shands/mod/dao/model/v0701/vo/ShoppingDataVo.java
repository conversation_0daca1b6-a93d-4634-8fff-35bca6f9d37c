package com.shands.mod.dao.model.v0701.vo;

import com.shands.mod.dao.model.hs.enums.PayTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/6/28
 * @desc 商品订单数据统计返回vo
*/
@Data
@ApiModel(value = "商品订单数据统计返回vo")
public class ShoppingDataVo {

  @ApiModelProperty(value = "POS单号")
  private String posNo;

  @ApiModelProperty(value = "订单号")
  private String orderNo;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "支付时间")
  private Date payTime;

  @ApiModelProperty(value = "支付方式")
  private String payType;

  @ApiModelProperty(value = "实际支付金额")
  private BigDecimal price;

  @ApiModelProperty(value = "营业点")
  private String placeName;

  @ApiModelProperty("交易号")
  private String transaction;

  @ApiModelProperty("到账时间")
  private String cancelOrderTime;

  @ApiModelProperty("订单状态")
  private String payStatus;


  public void setPayType(String payType) {
    String payTypeName = PayTypeEnum.getStateName(payType);
    this.payType = payTypeName;
  }
}
