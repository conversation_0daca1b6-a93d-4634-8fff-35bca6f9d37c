package com.shands.mod.dao.model.quality.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/25
 **/
@Data
@ApiModel("待办清单入参")
public class UpcomingListBo {

  @ApiModelProperty("任务表表id")
  private Integer taskId;

  @ApiModelProperty("酒店id")
  private Integer hotelId;

  @ApiModelProperty("清单状态")
  private String status;

  @ApiModelProperty("检查项")
  private String projectName;

  @ApiModelProperty(value = "页行数",required = true)
  private Integer pageNo;

  @ApiModelProperty(value = "页编码",required = true)
  private Integer pageSize;
}