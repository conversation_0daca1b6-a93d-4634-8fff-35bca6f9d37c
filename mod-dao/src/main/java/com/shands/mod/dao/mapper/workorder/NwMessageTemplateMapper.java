package com.shands.mod.dao.mapper.workorder;


import com.shands.mod.dao.model.workorder.po.NwMessageTemplate;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工单消息模板表(NwMessageTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-09 13:46:03
 */
public interface NwMessageTemplateMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NwMessageTemplate queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param nwMessageTemplate 查询条件
     * @return 对象列表
     */
    List<NwMessageTemplate> queryAllByLimit(NwMessageTemplate nwMessageTemplate);

    /**
     * 统计总行数
     *
     * @param nwMessageTemplate 查询条件
     * @return 总行数
     */
    long count(NwMessageTemplate nwMessageTemplate);

    /**
     * 新增数据
     *
     * @param nwMessageTemplate 实例对象
     * @return 影响行数
     */
    int insert(NwMessageTemplate nwMessageTemplate);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<NwMessageTemplate> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<NwMessageTemplate> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<NwMessageTemplate> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<NwMessageTemplate> entities);

    /**
     * 修改数据
     *
     * @param nwMessageTemplate 实例对象
     * @return 影响行数
     */
    int update(NwMessageTemplate nwMessageTemplate);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}

