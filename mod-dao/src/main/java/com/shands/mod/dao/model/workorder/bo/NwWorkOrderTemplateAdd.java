package com.shands.mod.dao.model.workorder.bo;

import com.shands.mod.dao.model.workorder.enums.PlatformEnum;
import com.shands.mod.dao.model.workorder.enums.TransferEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class NwWorkOrderTemplateAdd {

  @ApiModelProperty(value="id")
  private Integer id;

  @NotNull(message = "模版名称不能为空")
  @ApiModelProperty(value="模版名称",required = true)
  private String name;

  @NotNull(message = "业务类型不能为空")
  @ApiModelProperty(value="业务类型",required = true)
  private Integer serviceTypeId;

  @NotNull(message = "投放平台不能为空")
  @ApiModelProperty(value="投放平台",required = true)
  private PlatformEnum platform;

  @NotNull(message = "流转方式不能为空")
  @ApiModelProperty(value="流转方式",required = true)
  private TransferEnum transfer;

  @ApiModelProperty(value="受理机构",required = true)
  private String acceptHotel;

  @ApiModelProperty(value="模版描述")
  private String remark;

  @ApiModelProperty(value="配置字段")
  private List<NwTemplateDictAdd> dictVoList;

  @ApiModelProperty(value="触发器")
  private List<Integer> triggerList;

  @ApiModelProperty(value="业务节点")
  private List<NwWorkOrderNodeAdd> nwNodeVoList;

}
