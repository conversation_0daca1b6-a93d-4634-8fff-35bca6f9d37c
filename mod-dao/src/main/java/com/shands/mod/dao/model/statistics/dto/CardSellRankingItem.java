package com.shands.mod.dao.model.statistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工排行榜项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardSellRankingItem {
    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("员工名称")
    private String employeeName;

    @ApiModelProperty("百达卡数量")
    private Integer cardNum;

    @ApiModelProperty("百达卡奖金")
    private String cardBonus;
}