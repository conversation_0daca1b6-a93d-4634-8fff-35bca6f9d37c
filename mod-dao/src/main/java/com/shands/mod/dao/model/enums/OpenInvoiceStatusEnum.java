package com.shands.mod.dao.model.enums;

/**
 * 提交到税务云的开票状态枚举
 */
public enum OpenInvoiceStatusEnum {

  UNDERWAY(0,"开票中","UNDERWAY"),
  SUCCESS(1,"开票成功","SUCCESS"),
  FAIL(2,"开票失败","FAIL");
  private Integer code;
  private String desc;
  private String enName;

  OpenInvoiceStatusEnum(Integer code, String desc, String enName) {
    this.code = code;
    this.desc = desc;
    this.enName = enName;
  }

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public String getEnName() {
    return enName;
  }

  public void setEnName(String enName) {
    this.enName = enName;
  }
}
