package com.shands.mod.dao.model.quality.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@ApiModel("任务列表返回参数")
@Data
public class CheckTaskListVo {

  @ApiModelProperty("巡查表id")
  private Integer taskId;
  @ApiModelProperty("检查表id")
  private Integer checkId;

  @ApiModelProperty("任务标题")
  private String taskTitle;

  @ApiModelProperty("自检表标题")
  private String checkTitle;

  @ApiModelProperty("酒店名称")
  private String hotelName;

  @ApiModelProperty("任务完成状态")
  private String taskFinishStatus;

  @ApiModelProperty("任务开始时间")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date taskStartTime;

  @ApiModelProperty("任务结束时间")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date taskEndTime;

  @ApiModelProperty("创建人")
  private String createName;

  @ApiModelProperty("创建时间")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  @ApiModelProperty("酒店检查表任务类型")
  private String taskType;

  @ApiModelProperty("检查人")
  private String checkName;

  @ApiModelProperty("提交时间")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date taskFinishTime;
}
