<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.hs.HsHotelQrcodeMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.hs.HsHotelQrcode" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="ROOM_ID" property="roomId" jdbcType="VARCHAR" />
    <result column="QR_CODE" property="qrCode" jdbcType="VARCHAR" />
    <result column="QR_TYPE" property="qrType" jdbcType="TINYINT" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="GROUP_ID" property="groupId" jdbcType="INTEGER" />
    <result column="VERSION" property="version" jdbcType="INTEGER" />
    <result column="DELETED" property="deleted" jdbcType="TINYINT" />
    <result column="CREATE_USER" property="createUser" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, ROOM_ID, QR_CODE, QR_TYPE, COMPANY_ID, GROUP_ID, VERSION, DELETED, CREATE_USER, 
    CREATE_TIME, UPDATE_USER, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from hs_hotel_qrcode
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from hs_hotel_qrcode
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.hs.HsHotelQrcode" >
    insert into hs_hotel_qrcode (ID, ROOM_ID, QR_CODE, 
      QR_TYPE, COMPANY_ID, GROUP_ID, 
      VERSION, DELETED, CREATE_USER, 
      CREATE_TIME, UPDATE_USER, UPDATE_TIME
      )
    values (#{id,jdbcType=INTEGER}, #{roomId,jdbcType=VARCHAR}, #{qrCode,jdbcType=VARCHAR},
      #{qrType,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER}, 
      #{version,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createUser,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.hs.HsHotelQrcode" >
    insert into hs_hotel_qrcode
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="roomId != null" >
        ROOM_ID,
      </if>
      <if test="qrCode != null" >
        QR_CODE,
      </if>
      <if test="qrType != null" >
        QR_TYPE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="groupId != null" >
        GROUP_ID,
      </if>
      <if test="version != null" >
        VERSION,
      </if>
      <if test="deleted != null" >
        DELETED,
      </if>
      <if test="createUser != null" >
        CREATE_USER,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateUser != null" >
        UPDATE_USER,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="roomId != null" >
        #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="qrType != null" >
        #{qrType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null" >
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.hs.HsHotelQrcode" >
    update hs_hotel_qrcode
    <set >
      <if test="roomId != null" >
        ROOM_ID = #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        QR_CODE = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="qrType != null" >
        QR_TYPE = #{qrType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null" >
        GROUP_ID = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        VERSION = #{version,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        DELETED = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createUser != null" >
        CREATE_USER = #{createUser,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.hs.HsHotelQrcode" >
    update hs_hotel_qrcode
    set ROOM_ID = #{roomId,jdbcType=VARCHAR},
      QR_CODE = #{qrCode,jdbcType=VARCHAR},
      QR_TYPE = #{qrType,jdbcType=TINYINT},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      GROUP_ID = #{groupId,jdbcType=INTEGER},
      VERSION = #{version,jdbcType=INTEGER},
      DELETED = #{deleted,jdbcType=TINYINT},
      CREATE_USER = #{createUser,jdbcType=INTEGER},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_USER = #{updateUser,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <!--自定义sql-->
  <delete id="deleteByCompany" parameterType="java.lang.Integer">
    delete from hs_hotel_qrcode
    where  COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </delete>
  <select id="findByRoomId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hs_hotel_qrcode
    where 1=1
    <if test="roomId !=null">
      AND ROOM_ID = #{roomId,jdbcType=VARCHAR}
    </if>
    and COMPANY_ID = #{companyId,jdbcType=INTEGER}
    and QR_TYPE = #{qrType,jdbcType=TINYINT}
  </select>
  <insert id="replaceHotelCode" parameterType="com.shands.mod.dao.model.hs.HsHotelQrcode">
     replace into hs_hotel_qrcode(ID,ROOM_ID,QR_CODE,QR_TYPE,COMPANY_ID,VERSION
     ,DELETED,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME)value (#{id},#{roomId},#{qrCode},#{qrType},
     #{companyId},#{version},#{deleted},#{createUser},#{createTime},#{updateUser},#{updateTime})
  </insert>

</mapper>