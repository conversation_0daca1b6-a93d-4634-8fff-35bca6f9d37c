package com.shands.mod.dao.model.hs.enums;

public enum UmMessageType {
  // 目前只需要单播 unicast，列播 listcast，广播broadcast
  UNICAST(1, "单播", "unicast"),
  LISTCAST(2, "列播", "listcast"),
  BROADCAST(3, "广播", "broadcast");

  private Integer code;
  private String ChType;
  private String EnType;

  private UmMessageType umMessageType;

  UmMessageType(Integer code, String ChType, String EnType) {
    this.code = code;
    this.ChType = ChType;
    this.EnType = EnType;
  }

  public UmMessageType umMessageType() {
    return umMessageType;
  }

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public String getChType() {
    return ChType;
  }

  public void setChType(String chType) {
    ChType = chType;
  }

  public String getEnType() {
    return EnType;
  }

  public void setEnType(String enType) {
    EnType = enType;
  }
}
