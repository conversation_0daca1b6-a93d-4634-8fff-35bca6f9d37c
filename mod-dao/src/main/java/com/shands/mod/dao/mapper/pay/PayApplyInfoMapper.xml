<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shands.mod.dao.mapper.pay.PayApplyInfoMapper" >
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.v0701.pojo.PayApplyInfo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="apply_channel" property="applyChannel" jdbcType="VARCHAR" />
    <result column="module_code" property="moduleCode" jdbcType="VARCHAR" />
    <result column="hotel_id" property="hotelId" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, apply_channel, module_code, hotel_id, status, create_time, update_time, remark
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from pay_apply_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from pay_apply_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.v0701.pojo.PayApplyInfo" >
    insert into pay_apply_info (id, apply_channel, module_code, 
      hotel_id, status, create_time, 
      update_time, remark)
    values (#{id,jdbcType=INTEGER}, #{applyChannel,jdbcType=VARCHAR}, #{moduleCode,jdbcType=VARCHAR}, 
      #{hotelId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.v0701.pojo.PayApplyInfo" >
    insert into pay_apply_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="applyChannel != null" >
        apply_channel,
      </if>
      <if test="moduleCode != null" >
        module_code,
      </if>
      <if test="hotelId != null" >
        hotel_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="applyChannel != null" >
        #{applyChannel,jdbcType=VARCHAR},
      </if>
      <if test="moduleCode != null" >
        #{moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="hotelId != null" >
        #{hotelId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.v0701.pojo.PayApplyInfo" >
    update pay_apply_info
    <set >
      <if test="applyChannel != null" >
        apply_channel = #{applyChannel,jdbcType=VARCHAR},
      </if>
      <if test="moduleCode != null" >
        module_code = #{moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="hotelId != null" >
        hotel_id = #{hotelId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.v0701.pojo.PayApplyInfo" >
    update pay_apply_info
    set apply_channel = #{applyChannel,jdbcType=VARCHAR},
      module_code = #{moduleCode,jdbcType=VARCHAR},
      hotel_id = #{hotelId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 查询支付渠道配置参数 -->
  <select id="selectApplyChannel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_apply_info
    where hotel_id = #{hotelId,jdbcType=INTEGER}
    and module_code = #{moduleCode,jdbcType=VARCHAR}
    and status = 1
  </select>
</mapper>