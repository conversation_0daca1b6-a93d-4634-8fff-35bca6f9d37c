package com.shands.mod.dao.model.shopping;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 扫码购买商品，商品详情(HsShoppingDetail)实体类
 *
 * <AUTHOR>
 * @since 2021-05-31 17:11:26
 */

@Data
public class HsShoppingDetail implements Serializable {
    private static final long serialVersionUID = -76413913969006042L;
    /**
    * 扫码订单商品详情
    */
    private Integer id;
    /**
    * 菜品code
    */
    private String pluCode;
    /**
    * 菜品名称
    */
    private String pluName;
    /**
    * 菜品规格/做法  code
    */
    private String pluPracticeCode;
    /**
    * 菜品规格/做法  名称
    */
    private String pluPracticeName;
    /**
    * 购买数量
    */
    private Integer quantity;
    /**
    * 明细单价
    */
    private BigDecimal detailPrice;
    /**
    * 明细总价(明细单价*购买数量)
    */
    private BigDecimal totalPrice;
    /**
    * hs_shopping表的id
    */
    private Integer shoppingId;
    /**
    * 营业点code  hs_pos_outlet
    */
    private String outletCode;
    /**
    * 营业点名称
    */
    private String outletName;
    /**
    * 菜本code  hs_pos_note
    */
    private String noteCode;
    /**
    * 菜本名称
    */
    private String noteName;

}