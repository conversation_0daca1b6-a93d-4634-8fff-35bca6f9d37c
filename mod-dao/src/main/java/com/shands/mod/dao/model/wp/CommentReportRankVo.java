package com.shands.mod.dao.model.wp;

import com.shands.mod.dao.util.ThousandSeparatorUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class CommentReportRankVo {

  @ApiModelProperty(value="酒店名称")
  private String HotelName;

  private String hotelCode;

  private LocalDate bizDate;

  @ApiModelProperty(value="平均分")
  private Double score;

  @ApiModelProperty(value="千分位平均分")
  private String scoreStr;

  @ApiModelProperty(value="新增数")
  private Integer commentNum;

  @ApiModelProperty(value="千分位新增数")
  private String commentNumStr;

  public String getScoreStr() {
    scoreStr = ThousandSeparatorUtil.format(score);
    return scoreStr;
  }

  public String getCommentNumStr() {
    commentNumStr = ThousandSeparatorUtil.format(commentNum);
    return commentNumStr;
  }
}
