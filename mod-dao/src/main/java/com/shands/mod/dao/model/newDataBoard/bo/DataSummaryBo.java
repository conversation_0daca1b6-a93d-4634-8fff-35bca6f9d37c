package com.shands.mod.dao.model.newDataBoard.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * @Description:
 * @Author: Java菜鸟
 * @CreateDate: 2022/11/2 19:02
 */
@Data
@ApiModel("数据总表请求参数")
public class DataSummaryBo {

  @ApiModelProperty("本期开始时间")
  private String startTime;

  @ApiModelProperty("本期结束时间")
  private String endTime;

  @ApiModelProperty("对比时间")
  private String contrastTime;

  @ApiModelProperty("时间类型")
  private String timeType;

  @ApiModelProperty("类型 HOTEL/BLOC")
  private String type;

  @ApiModelProperty("酒店code")
  private List<String> hotelCodes;

  @ApiModelProperty("事业部code")
  private List<String> divisionCodes;

  @ApiModelProperty("品牌code")
  private List<String> brandCodes;

  @ApiModelProperty("管理方式code")
  private List<String> managementCodes;

}
