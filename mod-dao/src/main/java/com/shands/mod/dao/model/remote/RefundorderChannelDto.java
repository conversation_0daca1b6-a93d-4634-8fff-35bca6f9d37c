package com.shands.mod.dao.model.remote;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName RefundorderDto
 * @Description 退款申请渠道请求参数dto对象
 * <AUTHOR>
 * @Date 2021/4/8 10:43
 * @Version 1.0
 */
@Data
public class RefundorderChannelDto {

  /** 应用调用方渠道标识 */
  @NotBlank(message = "渠道标识不能为空")
  private String apply_channel;

  /** 应用调用方随机字符串 */
  @NotBlank(message = "随机字符串不能为空")
  private String apply_nonce_str;

  /** 应用调用方商户订单号 */
  @NotBlank(message = "商户订单号不能为空")
  private String apply_trade_no;

  /** 应用调用方商户退款申请单号 */
  @NotBlank(message = "商户退款申请单号不能为空")
  private String apply_refund_no;

  /** 申请退款金额 单位：分 */
  @Min(value = 1,message = "订单金额不能为空且大于0")
  private long refund_fee;

  /** 退款原因*/
  @NotBlank(message = "退款原因不能为空")
  private String refund_desc;
}
