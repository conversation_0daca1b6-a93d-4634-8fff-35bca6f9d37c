package com.shands.mod.dao.model.req.hs.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("分配计划卫生人员")
public class AllotPlanCleanUserReq implements Serializable {

  private static final long serialVersionUID = 227238530602869866L;

  @NotNull(message = "清扫人员id不能为空")
  @ApiModelProperty("清扫人员id")
  private Integer cleanUser;

  @NotBlank(message = "清扫人员姓名不能为空")
  @ApiModelProperty("清扫人员姓名")
  private String cleanUserName;

  @NotNull(message = "计划卫生项目id不能为空")
  @ApiModelProperty("计划卫生项目id")
  private Integer programId;

  @NotNull(message = "计划卫生分类id不能为空")
  @ApiModelProperty("计划卫生分类id")
  private Integer classifyId;

  @ApiModelProperty(value = "酒店id",hidden = true)
  private Integer companyId;

  @ApiModelProperty(value = "当前登录人id",hidden = true)
  private Integer userId;

  private List<PlanCleanRooms> rooms;
}
