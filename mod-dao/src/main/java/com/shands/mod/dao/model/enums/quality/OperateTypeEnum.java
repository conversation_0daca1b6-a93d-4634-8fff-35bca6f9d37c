package com.shands.mod.dao.model.enums.quality;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.ArrayList;
import java.util.List;

/**
 * 处理任务
 */
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {
  CREATE_TASK("创建任务",1),
  START("开始处理",2),
  TRANSFER("转交",3),
  FINISH("处理完成",4),
  REVIEW_PASS("复查通过",5),
  NOT_REVIEW_PASS("复查不通过",6),
  PAUSE("暂停",7);
  private String description;
  private Integer sort;

  public static List<OperateTypeEnum> getList(){
    List<OperateTypeEnum> demoList = new ArrayList<OperateTypeEnum>();
    for(OperateTypeEnum demo : OperateTypeEnum.values()){
      demoList.add(demo);
    }
    return demoList;
  }

  public static String getValues(String key){
    OperateTypeEnum[] values = OperateTypeEnum.values();
    for (OperateTypeEnum value : values) {
      if(value.name().equals(key)){
        return value.getDescription();
      }
    }
    return "";
  }

  public static Integer getSort(String key){
    OperateTypeEnum[] values = OperateTypeEnum.values();
    for (OperateTypeEnum value : values) {
      if(value.name().equals(key)){
        return value.getSort();
      }
    }
    return 0;
  }

  public static String getKey(String name){
    OperateTypeEnum[] values = OperateTypeEnum.values();
    for (OperateTypeEnum value : values) {
      if(value.getDescription().equals(name)){
        return value.name();
      }
    }
    return "";
  }

}
