package com.shands.mod.dao.model.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkOrderExport {
  private Integer idNum; // 工单编号
  private String typeName; // 工单类型名称
  private String acceptUserName; // 处理人姓名
  private String createUserName; // 发起人姓名
  private Date createTime; // 创建时间
  private String status;

  public Integer getIdNum() {
    return idNum;
  }

  public void setIdNum(Integer idNum) {
    this.idNum = idNum;
  }

  public String getTypeName() {
    return typeName;
  }

  public void setTypeName(String typeName) {
    this.typeName = typeName;
  }

  public String getAcceptUserName() {
    return acceptUserName;
  }

  public void setAcceptUserName(String acceptUserName) {
    this.acceptUserName = acceptUserName;
  }

  public String getCreateUserName() {
    return createUserName;
  }

  public void setCreateUserName(String createUserName) {
    this.createUserName = createUserName;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }
}
