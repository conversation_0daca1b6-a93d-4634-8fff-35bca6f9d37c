<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.training.ModCourseMapper">

    <resultMap type="com.shands.mod.dao.model.training.po.ModCourse" id="ModCourseMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="onShelfTime" column="on_shelf_time" jdbcType="TIMESTAMP"/>
        <result property="courseCollectId" column="course_collect_id" jdbcType="VARCHAR"/>
        <result property="courseGroupId" column="course_group_id" jdbcType="VARCHAR"/>
        <result property="labelId" column="label_id" jdbcType="VARCHAR"/>
        <result property="labelName" column="label_name" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="courseIntroduction" column="course_introduction" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="configType" column="config_type" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="ifDelete" column="if_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="ifStaff" column="if_staff" jdbcType="INTEGER"/>
        <result property="brandCode" column="brand_code" jdbcType="VARCHAR"/>
        <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
        <result property="divisionCode" column="division_code" jdbcType="VARCHAR"/>
        <result property="divisionName" column="division_name" jdbcType="VARCHAR"/>
        <result property="hotelIds" column="hotel_ids" jdbcType="VARCHAR"/>
        <result property="hotelName" column="hotel_name" jdbcType="VARCHAR"/>
        <result property="roleCode" column="role_code" jdbcType="VARCHAR"/>
        <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
        <result property="courseGroupIds" column="course_groupIds" jdbcType="VARCHAR"/>
        <result property="releaseHotel" column="release_hotel" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ModCourseMap">
        select
        id, name, type, status, on_shelf_time, course_collect_id, course_group_id, label_id,label_name, file_url, course_introduction, image_url, hotel_ids, remark, if_delete, create_time, update_time, create_user, update_user, version, if_staff, brand_code, brand_name, division_code, division_name, hotel_name,role_code,role_name,config_type,course_groupIds,release_hotel from mod_course
        where id = #{id} and if_delete = 0
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ModCourseMap">
        select
          id, name, type, status, on_shelf_time, course_collect_id, course_group_id, label_id,label_name, file_url, course_introduction, image_url, hotel_ids, remark, if_delete, create_time, update_time, create_user, update_user, version, if_staff, brand_code, brand_name, division_code, division_name, hotel_name,role_code,role_name,config_type,course_groupIds,release_hotel
        from mod_course
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="onShelfTime != null">
                and on_shelf_time = #{onShelfTime}
            </if>
            <if test="courseCollectId != null">
                and course_collect_id = #{courseCollectId}
            </if>
            <if test="courseGroupId != null">
                and course_group_id = #{courseGroupId}
            </if>
            <if test="labelId != null  and labelId != ''">
                and label_id = #{labelId}
            </if>
            <if test="labelName != null  and labelName != ''">
              and label_name = #{labelName}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
            <if test="courseIntroduction != null and courseIntroduction != ''">
                and course_introduction = #{courseIntroduction}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="hotelIds != null and hotelIds != ''">
                and hotel_ids = #{hotelIds}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="ifDelete != null">
                and if_delete = #{ifDelete}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="ifStaff != null">
                and if_staff = #{ifStaff}
            </if>
            <if test="brandCode != null and brandCode != ''">
                and brand_code = #{brandCode}
            </if>
            <if test="brandName != null and brandName != ''">
                and brand_name = #{brandName}
            </if>
            <if test="divisionCode != null and divisionCode != ''">
                and division_code = #{divisionCode}
            </if>
            <if test="divisionName != null and divisionName != ''">
                and division_name = #{divisionName}
            </if>
            <if test="hotelName != null and hotelName != ''">
              and hotel_name = #{hotelName}
            </if>
            <if test="roleCode != null and roleCode != ''">
              and role_code = #{roleCode}
            </if>
            <if test="roleName != null and roleName != ''">
              and role_name = #{roleName}
            </if>
            <if test="configType != null and configType != ''">
              and config_type = #{configType}
            </if>
            <if test="courseGroupIds != null and courseGroupIds != ''">
              and course_groupIds = #{courseGroupIds}
            </if>
            <if test="releaseHotel != null and releaseHotel != ''">
              and release_hotel = #{releaseHotel}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mod_course
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="onShelfTime != null">
                and on_shelf_time = #{onShelfTime}
            </if>
            <if test="courseCollectId != null">
                and course_collect_id = #{courseCollectId}
            </if>
            <if test="courseGroupId != null">
                and course_group_id = #{courseGroupId}
            </if>
            <if test="labelId != null">
                and label_id = #{labelId}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
            <if test="courseIntroduction != null and courseIntroduction != ''">
                and course_introduction = #{courseIntroduction}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="hotelIds != null and hotelIds != ''">
                and hotel_ids = #{hotelIds}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="ifDelete != null">
                and if_delete = #{ifDelete}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="ifStaff != null">
                and if_staff = #{ifStaff}
            </if>
            <if test="brandCode != null and brandCode != ''">
                and brand_code = #{brandCode}
            </if>
            <if test="brandName != null and brandName != ''">
                and brand_name = #{brandName}
            </if>
            <if test="divisionCode != null and divisionCode != ''">
                and division_code = #{divisionCode}
            </if>
            <if test="divisionName != null and divisionName != ''">
                and division_name = #{divisionName}
            </if>
            <if test="hotelName != null and hotelName != ''">
              and hotel_name = #{hotelName}
            </if>
            <if test="roleCode != null and roleCode != ''">
              and role_code = #{roleCode}
            </if>
            <if test="roleName != null and roleName != ''">
              and role_name = #{roleName}
            </if>
            <if test="configType != null and configType != ''">
              and config_type = #{configType}
            </if>
            <if test="courseGroupIds != null and courseGroupIds != ''">
              and course_groupIds = #{courseGroupIds}
            </if>
            <if test="releaseHotel != null and releaseHotel != ''">
              and release_hotel = #{releaseHotel}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course(name, type, status, on_shelf_time, course_collect_id, course_group_id, label_id,label_name, file_url, course_introduction, image_url, hotel_ids, remark, if_delete, create_time, update_time, create_user, update_user, version, if_staff, brand_code, brand_name, division_code, division_name, hotel_name,role_code,role_name,config_type,course_groupIds,release_hotel)
        values (#{name}, #{type}, #{status}, #{onShelfTime}, #{courseCollectId}, #{courseGroupId}, #{labelId},#{labelName}, #{fileUrl}, #{courseIntroduction}, #{imageUrl}, #{hotelIds}, #{remark}, #{ifDelete}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{version}, #{ifStaff}, #{brandCode}, #{brandName}, #{divisionCode}, #{divisionName}, #{hotelName}, #{roleCode},#{roleName}, #{configType},#{courseGroupIds}, #{releaseHotel})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course(name, type, status, on_shelf_time, course_collect_id, course_group_id, label_id,label_name, file_url, course_introduction, image_url, hotel_ids, remark, if_delete, create_time, update_time, create_user, update_user, version, if_staff, brand_code, brand_name, division_code, division_name, hotel_name,role_code,role_name,config_type,course_groupIds,release_hotel)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name}, #{entity.type}, #{entity.status}, #{entity.onShelfTime}, #{entity.courseCollectId}, #{entity.courseGroupId}, #{entity.labelId},#{entity.labelName}, #{entity.fileUrl}, #{entity.courseIntroduction}, #{entity.imageUrl}, #{entity.hotelIds}, #{entity.remark}, #{entity.ifDelete}, #{entity.createTime}, #{entity.updateTime}, #{entity.createUser}, #{entity.updateUser}, #{entity.version}, #{entity.ifStaff}, #{entity.brandCode}, #{entity.brandName}, #{entity.divisionCode}, #{entity.divisionName}, #{entity.hotelName}, #{entity.roleCode},#{entity.roleName}, #{entity.configType},#{entity.courseGroupIds}, #{entity.releaseHotel})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course(name, type, status, on_shelf_time, course_collect_id, course_group_id, label_id,label_name, file_url, course_introduction, image_url, hotel_ids, remark, if_delete, create_time, update_time, create_user, update_user, version, if_staff, brand_code, brand_name, division_code, division_name, hotel_name,role_code,role_name,config_type,course_groupIds,release_hotel)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.type}, #{entity.status}, #{entity.onShelfTime}, #{entity.courseCollectId}, #{entity.courseGroupId}, #{entity.labelId},#{entity.labelName}, #{entity.fileUrl}, #{entity.courseIntroduction}, #{entity.imageUrl}, #{entity.hotelIds}, #{entity.remark}, #{entity.ifDelete}, #{entity.createTime}, #{entity.updateTime}, #{entity.createUser}, #{entity.updateUser}, #{entity.version}, #{entity.ifStaff}, #{entity.brandCode}, #{entity.brandName}, #{entity.divisionCode}, #{entity.divisionName}, #{entity.hotelName}, #{entity.roleCode},#{entity.roleName}, #{entity.configType},#{entity.courseGroupIds}, #{entity.releaseHotel})
        </foreach>
        on duplicate key update
        name = values(name),
        type = values(type),
        status = values(status),
        on_shelf_time = values(on_shelf_time),
        course_collect_id = values(course_collect_id),
        course_group_id = values(course_group_id),
        label_id = values(label_id),
        label_name = values(label_name),
        file_url = values(file_url),
        course_introduction = values(course_introduction),
        image_url = values(image_url),
        hotel_ids = values(hotel_ids),
        remark = values(remark),
        if_delete = values(if_delete),
        create_time = values(create_time),
        update_time = values(update_time),
        create_user = values(create_user),
        update_user = values(update_user),
        version = values(version),
        if_staff = values(if_staff),
        brand_code = values(brand_code),
        brand_name = values(brand_name),
        division_code = values(division_code),
        division_name = values(division_name),
        hotel_name = values(hotel_name),
        role_code = values(role_code),
        role_name = values(role_name),
        config_type = values(config_type),
        course_groupIds = values(course_groupIds),
        release_hotel = values(release_hotel)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mod_course
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="onShelfTime != null">
                on_shelf_time = #{onShelfTime},
            </if>
            <if test="courseCollectId != null">
                course_collect_id = #{courseCollectId},
            </if>
            <if test="courseGroupId != null">
                course_group_id = #{courseGroupId},
            </if>
            <if test="labelId != null  and labelId != ''">
                label_id = #{labelId},
            </if>
            <if test="labelName != null  and labelName != ''">
              label_name = #{labelName},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
            <if test="courseIntroduction != null and courseIntroduction != ''">
                course_introduction = #{courseIntroduction},
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                image_url = #{imageUrl},
            </if>
            <if test="hotelIds != null ">
                hotel_ids = #{hotelIds},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="ifDelete != null">
                if_delete = #{ifDelete},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="ifStaff != null">
                if_staff = #{ifStaff},
            </if>
            <if test="brandCode != null">
                brand_code = #{brandCode},
            </if>
            <if test="brandName != null">
                brand_name = #{brandName},
            </if>
            <if test="divisionCode != null">
                division_code = #{divisionCode},
            </if>
            <if test="divisionName != null">
                division_name = #{divisionName},
            </if>
            <if test="hotelName != null">
                hotel_name = #{hotelName},
            </if>
            <if test="roleCode != null">
                role_code = #{roleCode},
            </if>
            <if test="roleName != null">
                role_name = #{roleName},
            </if>
            <if test="configType != null and configType != ''">
                config_type = #{configType},
            </if>
            <if test="courseGroupIds != null and courseGroupIds != ''">
                course_groupIds = #{courseGroupIds},
            </if>
            <if test="releaseHotel != null and releaseHotel != ''">
                release_hotel = #{releaseHotel},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mod_course where id = #{id}
    </delete>

    <select id="queryCourseList" resultType="com.shands.mod.dao.model.training.vo.SingleCourseVo">
      SELECT
        id id,
        name courseName,
        type courseType,
        status courseStatus,
        on_shelf_time onShelfTime
      FROM
        mod_course
      <where>
        if_delete = 0
        <if test="singleCourseBo.name != null and singleCourseBo.name != ''">
          and name like concat('%',#{singleCourseBo.name},'%')
        </if>
        <if test="singleCourseBo.type != null and singleCourseBo.type != ''">
          and type = #{singleCourseBo.type}
        </if>
        <if test="singleCourseBo.status != null">
          and status = #{singleCourseBo.status}
        </if>
        <if test="singleCourseBo.configType != null and singleCourseBo.configType != ''">
          and config_type = #{singleCourseBo.configType}
        </if>
        <if test="singleCourseBo.hotelId != null and singleCourseBo.hotelId != ''">
          and hotel_ids LIKE CONCAT('%',#{singleCourseBo.hotelId},'%')
        </if>
      </where>
      ORDER BY create_time desc
    </select>
  <select id="queryContentManageList"
    resultType="com.shands.mod.dao.model.training.vo.ContentManageListVo">

    SELECT mc.id as courseId,mc.`name` as coursesName, mc.`type`,mc.`status`,mc.on_shelf_time as shelfTime,mc.create_time as createTime,ca.sort from
    mod_course_association ca LEFT JOIN mod_course mc on ca.course_id=mc.id and ca.if_delete=0
    WHERE ca.course_collect_id=#{contentManageListBo.collectionId}
    and mc.if_delete=0
    <if test="contentManageListBo.coursesName != null and contentManageListBo.coursesName != ''">
      and mc.`name` LIKE CONCAT('%',#{contentManageListBo.coursesName},'%')
    </if>
    <if test="contentManageListBo.type != null and contentManageListBo.type != ''">
      and mc.`type` = #{contentManageListBo.type}
    </if>
    <if test="contentManageListBo.status != null">
      and mc.`status` = #{contentManageListBo.status}
    </if>
    order by ca.sort
  </select>


  <select id="latestCourseList" resultType="com.shands.mod.dao.model.training.vo.LatestCourseVo">
  SELECT mc.hotel_ids as hotelIds,mc.brand_code as brandCode,mc.division_code as divisionCode,mc.config_type as configType,mc.if_staff as ifStaff,mc.id as tempId,'SINGLE_CLASS' as courseDistinguish, mc.role_code as roleCode,IFNULL(count(cu.id),0) as learningNum, mc.hotel_ids, mc.`name`,mc.`type`,mc.file_url as fileUrl,mc.image_url as imageUrl
	from mod_course mc
	LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='SINGLE_CLASS'
	WHERE
	(mc.config_type= 'BLOC' and mc.`status`=1  and mc.if_delete=0)
	or (mc.hotel_ids like concat('%',#{hotelId},'%') and mc.config_type= 'HOTEL' and mc.if_delete=0 and mc.`status`=1)
	GROUP BY mc.id
	ORDER BY mc.create_time desc
  </select>

  <select id="recentBrowseList" resultType="com.shands.mod.dao.model.training.vo.LatestCourseVo">

    SELECT mc.hotel_ids as hotelIds, mc.brand_code as brandCode,mc.division_code as divisionCode,mc.config_type as configType,mc.if_staff as ifStaff,mc.id as tempId,'SINGLE_CLASS' as courseDistinguish,mc.role_code as roleCode,IFNULL(tb1.learningNum,0)as learningNum,mc.id as courseId, mc.hotel_ids, mc.`name`,mc.`type`,mc.file_url as fileUrl,mc.image_url as imageUrl
	from mod_course mc
	LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='SINGLE_CLASS'
  LEFT JOIN (
   SELECT  mc.id as courseId, count(cu.id) as learningNum
	from mod_course mc
	LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='SINGLE_CLASS'
	WHERE mc.if_delete=0
	GROUP BY mc.id
  ) tb1 on tb1.courseId=mc.id
 WHERE  cu.user_id=#{userId} and mc.`status`=1
 ORDER BY cu.last_learn_time desc
  </select>

  <select id="collectionList" resultType="com.shands.mod.dao.model.training.vo.LatestCourseVo">
  SELECT mc.hotel_ids as hotelIds, mc.brand_code as brandCode,mc.division_code as divisionCode,mc.config_type as configType,ck.create_time as createTime ,mc.if_staff as ifStaff, mc.id as tempId,'SINGLE_CLASS' as courseDistinguish,mc.role_code as roleCode,IFNULL(tb1.learningNum,0)as learningNum, mc.id as courseId, mc.hotel_ids, mc.`name`,mc.`type`,mc.file_url as fileUrl,mc.image_url as imageUrl from   mod_course mc  LEFT JOIN  mod_course_keep ck on mc.id=ck.course_id
  LEFT JOIN (
   SELECT  cu.`type`,mc.id as courseId, count(cu.id) as learningNum
	from mod_course mc
	LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='SINGLE_CLASS'
	WHERE mc.if_delete=0
	GROUP BY mc.id
  ) tb1 on tb1.courseId=mc.id
  WHERE ck.course_user_id=#{userId} and ck.if_keep=1 and mc.if_delete=0 and mc.`status`=1
  </select>


  <select id="searchCourse" resultType="com.shands.mod.dao.model.training.vo.LatestCourseVo">
select * from (
    SELECT mc.hotel_ids as hotelIds, mc.brand_code as brandCode,mc.division_code as divisionCode,mc.if_staff as ifStaff,mc.config_type as configType,GROUP_CONCAT(cl.label_name) as labelName ,mc.id as tempId,'SINGLE_CLASS' as courseDistinguish, mc.role_code as roleCode,IFNULL(tb1.learningNum,0) as learningNum,mc.id as courseId, mc.hotel_ids, mc.`name`,mc.`type`,mc.file_url as fileUrl,mc.image_url as imageUrl from  mod_course mc
    LEFT JOIN mod_course_label  cl on FIND_IN_SET(cl.id,mc.label_id) and cl.label_type='SINGLE_CLASS'
    LEFT JOIN (
    SELECT  mc.id as courseId, count(cu.id) as learningNum
    from mod_course mc
    LEFT JOIN mod_course_user cu on cu.course_id= mc.id
    WHERE mc.if_delete=0
    GROUP BY mc.id
    ) tb1 on tb1.courseId=mc.id
    WHERE
       (mc.config_type= 'BLOC' and mc.`status`=1  and mc.if_delete=0)
      or (mc.hotel_ids like concat('%',#{hotelId},'%') and mc.config_type= 'HOTEL' and mc.`status`=1 and mc.if_delete=0 )
    GROUP BY mc.id
    ) tb2
    where tb2.`name` like concat('%',#{keyWord},'%') or tb2.labelName like concat('%',#{keyWord},'%')
    ORDER BY
    CASE WHEN tb2.`name` LIKE concat('%',#{keyWord},'%') THEN 1
    ELSE tb2.labelName  end
  </select>

  <select id="courseDetails" resultType="com.shands.mod.dao.model.training.vo.CourseDetailsVo">
    SELECT mc.release_hotel as releaseHotel,mc.create_time as createTime, IFNULL(ck.if_keep,0) as if_collect,IFNULL(tb1.learningNum,0)as learningNum,
    mc.hotel_ids, mc.`name`,mc.`type`,mc.file_url as fileUrl,mc.image_url as imageUrl,mc.course_introduction as courseIntroduction from  mod_course mc
    LEFT JOIN mod_course_keep ck on ck.course_id=mc.id and ck.course_user_id=#{userId}
    LEFT JOIN (
    SELECT mc.id as courseId, count(cu.id) as learningNum
    from mod_course mc
    LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='SINGLE_CLASS'
    WHERE mc.if_delete=0 and mc.id=#{courseId}
    GROUP BY mc.id
    ) tb1 on tb1.courseId=mc.id
    WHERE  mc.id=#{courseId}
  </select>

  <select id="selectCourseDirectoryByCollection"
    resultType="com.shands.mod.dao.model.training.vo.CourseDirectoryListVo">
  SELECT mc.hotel_ids as hotelIds,mc.brand_code as brandCode,mc.role_code as roleCode,mc.config_type as configType,mc.if_staff as ifStaff,mc.file_url AS fileUrl,mc.image_url as imageUrl,mc.`type`,IFNULL(tb1.learningNum,0)as learningNum,mc.`name`,mc.id as tempId from  mod_course mc   LEFT JOIN mod_course_association  ca on mc.id= ca.course_id
  LEFT JOIN mod_course_collection mcc on mcc.id=ca.course_collect_id
  LEFT JOIN (
   SELECT  mc.id as courseId, count(cu.id) as learningNum
	from mod_course mc
	LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='SINGLE_CLASS'
	WHERE mc.if_delete=0
	GROUP BY mc.id
  ) tb1 on tb1.courseId=mc.id
  WHERE ca.course_collect_id=#{collectionId} and mc.if_delete=0 and mc.`status`=1 and mcc.if_delete=0 AND ca.if_delete = 0
  order by ca.sort asc
  </select>
    <select id="selectCourse" resultType="com.shands.mod.dao.model.training.po.ModCourse">
      SELECT
        *
      FROM
        mod_course
      WHERE if_delete = 0
      <if test="chooseCourseBo.name != null and chooseCourseBo.name != ''">
        AND name LIKE CONCAT('%',#{chooseCourseBo.name},'%')
      </if>
      <if test="chooseCourseBo.configType != null and chooseCourseBo.configType != ''">
        and config_type = #{chooseCourseBo.configType}
      </if>
      <if test="chooseCourseBo.hotelId != null and chooseCourseBo.hotelId != ''">
        and hotel_ids LIKE CONCAT('%',#{chooseCourseBo.hotelId},'%')
      </if>
    </select>
</mapper>

