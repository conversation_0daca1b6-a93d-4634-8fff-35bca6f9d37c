package com.shands.mod.dao.model.enums;

import io.swagger.annotations.ApiModelProperty;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProxyOrderStatusEnum {

  PRE_ARRIVAL(1, "预抵单"),
  COMPLETED(2, "已完成"),
  CANCELLED(3, "已取消"),
  CHECKED_IN(4, "入住中");

  @ApiModelProperty(value = "订单状态 1-预抵单，2-已完成，3-已取消，4-入住中")
  private final Integer status;
  private final String description;

  public static ProxyOrderStatusEnum getEnum(Integer status) {
    return Arrays.stream(ProxyOrderStatusEnum.values())
        .filter(l -> l.getStatus().equals(status)).findFirst()
        .orElse(COMPLETED);
  }
}
