package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.GoodsType;
import com.shands.mod.dao.model.req.hs.goods.GoodsTypeAddAddReq;
import com.shands.mod.dao.model.req.hs.goods.ListReq;
import com.shands.mod.dao.model.res.hs.good.GoodsRes;
import com.shands.mod.dao.model.res.hs.good.GoodsTypeQueryRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface GoodsTypeMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(GoodsType record);

  int insertSelective(GoodsType record);

  GoodsType selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(GoodsType record);

  int updateByPrimaryKey(GoodsType record);

  List<GoodsTypeQueryRes> query(GoodsTypeAddAddReq req);

  List<GoodsRes> typeList(@Param("queryReq") ListReq queryReq);

  GoodsTypeQueryRes typeById(@Param("id") Integer id);
}
