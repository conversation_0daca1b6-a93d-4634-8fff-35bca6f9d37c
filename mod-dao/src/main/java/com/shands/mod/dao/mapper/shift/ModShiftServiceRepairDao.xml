<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.shift.ModShiftServiceRepairDao">

  <resultMap type="com.shands.mod.dao.model.shift.ModShiftServiceRepair" id="ModShiftServiceRepairMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="serviceType" column="service_type" jdbcType="VARCHAR"/>
    <result property="shiftDate" column="shift_date" jdbcType="TIMESTAMP"/>
    <result property="shiftUserId" column="shift_user_id" jdbcType="INTEGER"/>
    <result property="extendId" column="extend_id" jdbcType="VARCHAR"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="hotelId" column="hotel_id" jdbcType="INTEGER"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="createUser" column="create_user" jdbcType="INTEGER"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
  </resultMap>

  <!--查询单个-->
  <select id="queryById" resultMap="ModShiftServiceRepairMap">
        select
          id, service_type, shift_date, shift_user_id, extend_id, status, hotel_id, create_time, create_user, update_time, update_user, remark
        from   mod_shift_service_repair
        where id = #{id}
    </select>

  <!--查询指定行数据-->
  <select id="queryAllByLimit" resultMap="ModShiftServiceRepairMap">
        select
          id, service_type, shift_date, shift_user_id, extend_id, status, hotel_id, create_time, create_user, update_time, update_user, remark
        from   mod_shift_service_repair
        limit #{offset}, #{limit}
    </select>

  <!--通过实体作为筛选条件查询-->
  <select id="queryAll" resultMap="ModShiftServiceRepairMap">
    select
    id, service_type, shift_date, shift_user_id, extend_id, status, hotel_id, create_time,
    create_user, update_time, update_user, remark
    from   mod_shift_service_repair
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="serviceType != null and serviceType != ''">
        and service_type = #{serviceType}
      </if>
      <if test="shiftDate != null">
        and shift_date = #{shiftDate}
      </if>
      <if test="shiftUserId != null">
        and shift_user_id = #{shiftUserId}
      </if>
      <if test="extendId != null and extendId != ''">
        and extend_id = #{extendId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="hotelId != null">
        and hotel_id = #{hotelId}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark}
      </if>
    </where>
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into   mod_shift_service_repair(service_type, shift_date, shift_user_id, extend_id, status, hotel_id, create_time, create_user, update_time, update_user, remark)
        values (#{serviceType}, #{shiftDate}, #{shiftUserId}, #{extendId}, #{status}, #{hotelId}, #{createTime}, #{createUser}, #{updateTime}, #{updateUser}, #{remark})
    </insert>

  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into   mod_shift_service_repair(service_type, shift_date, shift_user_id,
    extend_id, status, hotel_id, create_time, create_user, update_time, update_user, remark)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.serviceType}, #{entity.shiftDate}, #{entity.shiftUserId}, #{entity.extendId},
      #{entity.status}, #{entity.hotelId}, #{entity.createTime}, #{entity.createUser},
      #{entity.updateTime}, #{entity.updateUser}, #{entity.remark})
    </foreach>
  </insert>

  <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
    insert into   mod_shift_service_repair(service_type, shift_date, shift_user_id,
    extend_id, status, hotel_id, create_time, create_user, update_time, update_user, remark)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.serviceType}, #{entity.shiftDate}, #{entity.shiftUserId}, #{entity.extendId},
      #{entity.status}, #{entity.hotelId}, #{entity.createTime}, #{entity.createUser},
      #{entity.updateTime}, #{entity.updateUser}, #{entity.remark})
    </foreach>
    on duplicate key update
    service_type = values(service_type) , shift_date = values(shift_date) , shift_user_id =
    values(shift_user_id) , extend_id = values(extend_id) , status = values(status) , hotel_id =
    values(hotel_id) , create_time = values(create_time) , create_user = values(create_user) ,
    update_time = values(update_time) , update_user = values(update_user) , remark = values(remark)
  </insert>

  <!--通过主键修改数据-->
  <update id="update">
    update   mod_shift_service_repair
    <set>
      <if test="serviceType != null and serviceType != ''">
        service_type = #{serviceType},
      </if>
      <if test="shiftDate != null">
        shift_date = #{shiftDate},
      </if>
      <if test="shiftUserId != null">
        shift_user_id = #{shiftUserId},
      </if>
      <if test="extendId != null and extendId != ''">
        extend_id = #{extendId},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="hotelId != null">
        hotel_id = #{hotelId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="createUser != null">
        create_user = #{createUser},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark},
      </if>
    </set>
    where id = #{id}
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
        delete from   mod_shift_service_repair where id = #{id}
    </delete>

  <update id="updStatus">
    update mod_shift_service_repair set status = 0 where hotel_id = #{hotelId} and shift_date = #{shiftDate} and extend_id = #{extendId}
  </update>

</mapper>