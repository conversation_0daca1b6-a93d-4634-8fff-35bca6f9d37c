package com.shands.mod.dao.model.workorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shands.mod.dao.model.enums.OwnershipEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * @ClassName NwWorkOrderResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/5/10 11:01
 * @Version 1.0
 */
@Data
@ApiModel("查询工单管理列表")
public class NwWorkOrderResponse {

  @ApiModelProperty(value = "id")
  private Integer id;

  @ApiModelProperty(value = "创建来源，PC,APP")
  private String source;

  @ApiModelProperty(value = "工单编码")
  private String workNumber;

  @ApiModelProperty(value = "渠道")
  private String channel;

  @ApiModelProperty(value = "问题分类")
  private String problemType;

  @ApiModelProperty(value = "标题")
  private String title;

  @ApiModelProperty(value = "优先级")
  private String priority;

  @ApiModelProperty(value = "受理机构")
  private String hotelName;

  @ApiModelProperty(value = "酒店code")
  private String hotelCode;

  @ApiModelProperty(value = "事业部code")
  private String deptCode;

  @ApiModelProperty(value = "事业部名称")
  private String deptName;

  @ApiModelProperty(value = "问题分类")
  private String problemName;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "模板名称")
  private String templateName;

  @ApiModelProperty("客户手机号")
  private String customerMobile;

  @ApiModelProperty(value = "工单状态")
  private String status;

  @ApiModelProperty(value = "创建人")
  private String createUser;

  @ApiModelProperty(value = "抄送人")
  private String copyUser;

  @ApiModelProperty(value = "受理人")
  private String acceptUser;

  @ApiModelProperty(value = "描述")
  private String remark;

  @ApiModelProperty(value = "完结原因")
  private String finishReason;

  @ApiModelProperty(value = "关闭原因")
  private String colseReason;

  @ApiModelProperty(value = "是否超时")
  private boolean ifTimeOut;

  public String getDeptName() {
    return OwnershipEnum.getValue(deptCode);
  }
}
