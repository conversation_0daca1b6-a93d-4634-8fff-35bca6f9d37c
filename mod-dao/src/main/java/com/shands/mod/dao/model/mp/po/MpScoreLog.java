package com.shands.mod.dao.model.mp.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;

/**
 * 评星记录表(MpScoreLog)实体类
 *
 * <AUTHOR>
 * @since 2022-11-17 15:05:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class MpScoreLog implements Serializable {
    private static final long serialVersionUID = -75028342482387207L;
    
    private Integer id;
    /**
     * 版本号
版本号
     */
    private Integer version;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private Integer createUser;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改人
     */
    private Integer updateUser;
    
    private Integer userId;
    
    private Double originalScore;
    
    private Double modifyScore;
    /**
     * 内容
     */
    private String remark;
    /**
     * 图片
     */
    private String picture;
    
    private String batchCode;
}

