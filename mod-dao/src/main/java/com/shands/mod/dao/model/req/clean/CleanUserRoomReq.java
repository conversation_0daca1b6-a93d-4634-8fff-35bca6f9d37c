package com.shands.mod.dao.model.req.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel("分配清洁人员 房间信息")
public class CleanUserRoomReq {

  @ApiModelProperty("id")
  private Integer id;

  @ApiModelProperty("房间号")
  private String roomNo;


  @ApiModelProperty("检查人员id")
  private Integer checkUser;

  @ApiModelProperty("检查人员 姓名")
  private String checkUserName;

  @ApiModelProperty("房型code")
  private String roomType;

  @ApiModelProperty("清扫类型 房态中的状态")
  private String cleanType;

  @ApiModelProperty("宾客姓名")
  private String guestName;

  @ApiModelProperty("入住时间")
  private Date arrivalTime;

  @ApiModelProperty("退房时间")
  private Date leaveTime;

  @ApiModelProperty(value = "是否将离(T:是/F:否)")
  private String isDep;

  @ApiModelProperty("1加班 0正常班")
  private Integer overtime;
}
