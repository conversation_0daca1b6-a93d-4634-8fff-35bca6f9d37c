<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.voucher.ModVoucherRecordMapper">
  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.voucher.ModVoucherRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="uc_id" jdbcType="INTEGER" property="ucId" />
    <result column="hotel_code" jdbcType="VARCHAR" property="hotelCode" />
    <result column="biz_date" jdbcType="DATE" property="bizDate" />
    <result column="record_code" jdbcType="VARCHAR" property="recordCode" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="send_type" jdbcType="TINYINT" property="sendType" />
    <result column="template_ids" jdbcType="VARCHAR" property="templateIds" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, uc_id, hotel_code, biz_date, record_code, count, send_type, template_ids, create_time, 
    update_time, create_by, update_by, del_flag
  </sql>
  <select id="selectByExample" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mod_voucher_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mod_voucher_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mod_voucher_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecordExample">
    delete from mod_voucher_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecord">
    insert into mod_voucher_record (id, uc_id, hotel_code, 
      biz_date, record_code, count, 
      send_type, template_ids, create_time, 
      update_time, create_by, update_by, 
      del_flag)
    values (#{id,jdbcType=BIGINT}, #{ucId,jdbcType=INTEGER}, #{hotelCode,jdbcType=VARCHAR}, 
      #{bizDate,jdbcType=DATE}, #{recordCode,jdbcType=VARCHAR}, #{count,jdbcType=INTEGER}, 
      #{sendType,jdbcType=TINYINT}, #{templateIds,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{delFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecord">
    insert into mod_voucher_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ucId != null">
        uc_id,
      </if>
      <if test="hotelCode != null">
        hotel_code,
      </if>
      <if test="bizDate != null">
        biz_date,
      </if>
      <if test="recordCode != null">
        record_code,
      </if>
      <if test="count != null">
        count,
      </if>
      <if test="sendType != null">
        send_type,
      </if>
      <if test="templateIds != null">
        template_ids,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ucId != null">
        #{ucId,jdbcType=INTEGER},
      </if>
      <if test="hotelCode != null">
        #{hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="bizDate != null">
        #{bizDate,jdbcType=DATE},
      </if>
      <if test="recordCode != null">
        #{recordCode,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="sendType != null">
        #{sendType,jdbcType=TINYINT},
      </if>
      <if test="templateIds != null">
        #{templateIds,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecordExample" resultType="java.lang.Long">
    select count(*) from mod_voucher_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mod_voucher_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ucId != null">
        uc_id = #{record.ucId,jdbcType=INTEGER},
      </if>
      <if test="record.hotelCode != null">
        hotel_code = #{record.hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizDate != null">
        biz_date = #{record.bizDate,jdbcType=DATE},
      </if>
      <if test="record.recordCode != null">
        record_code = #{record.recordCode,jdbcType=VARCHAR},
      </if>
      <if test="record.count != null">
        count = #{record.count,jdbcType=INTEGER},
      </if>
      <if test="record.sendType != null">
        send_type = #{record.sendType,jdbcType=TINYINT},
      </if>
      <if test="record.templateIds != null">
        template_ids = #{record.templateIds,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mod_voucher_record
    set id = #{record.id,jdbcType=BIGINT},
      uc_id = #{record.ucId,jdbcType=INTEGER},
      hotel_code = #{record.hotelCode,jdbcType=VARCHAR},
      biz_date = #{record.bizDate,jdbcType=DATE},
      record_code = #{record.recordCode,jdbcType=VARCHAR},
      count = #{record.count,jdbcType=INTEGER},
      send_type = #{record.sendType,jdbcType=TINYINT},
      template_ids = #{record.templateIds,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      del_flag = #{record.delFlag,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecord">
    update mod_voucher_record
    <set>
      <if test="ucId != null">
        uc_id = #{ucId,jdbcType=INTEGER},
      </if>
      <if test="hotelCode != null">
        hotel_code = #{hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="bizDate != null">
        biz_date = #{bizDate,jdbcType=DATE},
      </if>
      <if test="recordCode != null">
        record_code = #{recordCode,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="sendType != null">
        send_type = #{sendType,jdbcType=TINYINT},
      </if>
      <if test="templateIds != null">
        template_ids = #{templateIds,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecord">
    update mod_voucher_record
    set uc_id = #{ucId,jdbcType=INTEGER},
      hotel_code = #{hotelCode,jdbcType=VARCHAR},
      biz_date = #{bizDate,jdbcType=DATE},
      record_code = #{recordCode,jdbcType=VARCHAR},
      count = #{count,jdbcType=INTEGER},
      send_type = #{sendType,jdbcType=TINYINT},
      template_ids = #{templateIds,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      del_flag = #{delFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.shands.mod.dao.model.voucher.ModVoucherRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mod_voucher_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert">
    <if test="items.get(0) != null">
            insert into mod_voucher_record  (id, uc_id, hotel_code, 
        biz_date, record_code, count, 
        send_type, template_ids, create_time, 
        update_time, create_by, update_by, 
        del_flag)
      values
      <foreach collection="items" item="item" index="index" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.ucId,jdbcType=INTEGER}, #{item.hotelCode,jdbcType=VARCHAR}, 
        #{item.bizDate,jdbcType=DATE}, #{item.recordCode,jdbcType=VARCHAR}, #{item.count,jdbcType=INTEGER}, 
        #{item.sendType,jdbcType=TINYINT}, #{item.templateIds,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT}, 
        #{item.delFlag,jdbcType=TINYINT})
      </foreach>
    </if>
  </insert>
</mapper>