package com.shands.mod.dao.model.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@ApiModel("根据员工姓名模糊搜索")
@Data
public class UserDeptRes {

  @ApiModelProperty("部门id")
  private Integer deptId;

  @ApiModelProperty("员工id")
  private Integer id;

  @ApiModelProperty("员工姓名")
  private String name;

  @ApiModelProperty("部门名称")
  private String deptName;

  List<UserDeptRes> users;
}
