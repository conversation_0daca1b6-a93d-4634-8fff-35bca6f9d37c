package com.shands.mod.dao.model.datarevision.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/5/20
 **/
@Data
@ApiModel("酒店礼包销售排行榜接口 出参")
public class HotelGiftPackageVo{

  @ApiModelProperty("酒店端描述")
  private String desc;

  @ApiModelProperty("本店礼包排行")
  HotelPackageRankVo packageRanKVo;

  @ApiModelProperty("酒店礼包排行")
  List<HotelPackageRankVo> packageRanKVos;
}