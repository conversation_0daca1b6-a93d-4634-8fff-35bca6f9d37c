package com.shands.mod.dao.model.enums.quality;

import java.util.ArrayList;
import java.util.List;

/**
 * 检查类型
 */
public enum CheckTypeEnum {
  BLOC_INSPECTION("巡检样表"),
  HOTEL_SELF("自检样表");
  private String description;

  CheckTypeEnum(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public static List<CheckTypeEnum> getList(){
    List<CheckTypeEnum> demoList = new ArrayList<CheckTypeEnum>();
    for(CheckTypeEnum demo : CheckTypeEnum.values()){
      demoList.add(demo);
    }
    return demoList;
  }

  public static String getValues(String key){
    CheckTypeEnum[] values = CheckTypeEnum.values();
    for (CheckTypeEnum value : values) {
      if(value.name().equals(key)){
        return value.getDescription();
      }
    }
    return "";
  }

  public static String getKey(String name){
    CheckTypeEnum[] values = CheckTypeEnum.values();
    for (CheckTypeEnum value : values) {
      if(value.getDescription().equals(name)){
        return value.name();
      }
    }
    return "";
  }

}
