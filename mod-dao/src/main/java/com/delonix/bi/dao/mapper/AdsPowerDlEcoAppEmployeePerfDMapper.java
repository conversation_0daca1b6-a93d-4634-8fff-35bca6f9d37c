package com.delonix.bi.dao.mapper;

import com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfD;
import com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDExample;
import com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDKey;
import java.util.Date;
import java.util.List;
import com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AdsPowerDlEcoAppEmployeePerfDMapper {
    long countByExample(AdsPowerDlEcoAppEmployeePerfDExample example);

    int deleteByExample(AdsPowerDlEcoAppEmployeePerfDExample example);

    int deleteByPrimaryKey(AdsPowerDlEcoAppEmployeePerfDKey key);

    int insert(AdsPowerDlEcoAppEmployeePerfD record);

    int insertSelective(AdsPowerDlEcoAppEmployeePerfD record);

    List<AdsPowerDlEcoAppEmployeePerfD> selectByExampleWithRowbounds(AdsPowerDlEcoAppEmployeePerfDExample example, RowBounds rowBounds);

    List<AdsPowerDlEcoAppEmployeePerfD> selectByExample(AdsPowerDlEcoAppEmployeePerfDExample example);

    AdsPowerDlEcoAppEmployeePerfD selectByPrimaryKey(AdsPowerDlEcoAppEmployeePerfDKey key);

    int updateByExampleSelective(@Param("record") AdsPowerDlEcoAppEmployeePerfD record, @Param("example") AdsPowerDlEcoAppEmployeePerfDExample example);

    int updateByExample(@Param("record") AdsPowerDlEcoAppEmployeePerfD record, @Param("example") AdsPowerDlEcoAppEmployeePerfDExample example);

    int updateByPrimaryKeySelective(AdsPowerDlEcoAppEmployeePerfD record);

    int updateByPrimaryKey(AdsPowerDlEcoAppEmployeePerfD record);

    void batchInsert(@Param("items") List<AdsPowerDlEcoAppEmployeePerfD> items);

  /**
   * 按酒店汇总业绩数据
   */
  AdsHotelPerformanceHDTO sumPerformanceByHotel(
      @Param("hotelCode") String hotelCode,
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate);

  /**
   * 按酒店和用户汇总业绩数据
   */
  AdsHotelPerformanceHDTO sumPerformanceByHotelAndUser(
      @Param("hotelCode") String hotelCode,
      @Param("ucId") String ucId,
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate);

  /**
   * 获取门店售卡排行榜TOP5
   */
  List<AdsHotelPerformanceHDTO> getTopHotelCardSellRanking(
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate);

  /**
   * 获取员工售卡排行榜TOP5
   */
  List<AdsHotelPerformanceHDTO> getTopEmployeeCardSellRanking(
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate);

  /**
   * 查询券类型统计数据 - 按员工维度
   */
  List<AdsHotelPerformanceHDTO> queryVoucherDataByEmployee(
      @Param("hotelCode") String hotelCode,
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate,
      @Param("voucherType") String voucherType);

  /**
   * 查询百达卡统计数据 - 按员工维度
   */
  List<AdsHotelPerformanceHDTO> queryBdwCardDataByEmployee(
      @Param("hotelCode") String hotelCode,
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate,
      @Param("cardType") String cardType);
}