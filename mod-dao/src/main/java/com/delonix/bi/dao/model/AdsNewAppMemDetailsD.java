package com.delonix.bi.dao.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AdsNewAppMemDetailsD implements Serializable {
  /**
   * 会员ID
   */
  private String memberId;

  /**
   * 业务日期
   */
  private Date bizDate;

  /**
   * 酒店代码
   */
  private String hotelCode;

  /**
   * 酒店名称
   */
  private String hotelName;

  /**
   * 部门名称
   */
  private String deptName;

  /**
   * 员工姓名
   */
  private String staffName;

  /**
   * 通宝员工ID
   */
  private String ucId;

  /**
   * 二次发展销售ID
   */
  private String saleId;

  /**
   * 会员手机号(脱敏)
   */
  private String memberPhone;

  /**
   * 会员注册时间
   */
  private Date developTime;

  /**
   * 引导场景值
   */
  private String scene;

  /**
   * 数据更新时间
   */
  private Date updateTime;


    private static final long serialVersionUID = 1L;
}