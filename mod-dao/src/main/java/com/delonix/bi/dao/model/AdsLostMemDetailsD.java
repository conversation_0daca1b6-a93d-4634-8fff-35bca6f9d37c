package com.delonix.bi.dao.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AdsLostMemDetailsD implements Serializable {

  /**
   * 业务日期
   */
  private Date bizDate;

  /**
   * 酒店代码
   */
  private String hotelCode;

  /**
   * 酒店名称
   */
  private String hotelName;

  /**
   * 部门名称
   */
  private String deptName;

  /**
   * 员工姓名
   */
  private String staffName;

  /**
   * 员工ID
   */
  private String ucId;

  /**
   * 员工销售ID
   */
  private String salesId;

  /**
   * 会员ID
   */
  private String memberId;

  /**
   * 会员手机号(脱敏)
   */
  private String memberPhone;

  /**
   * 流失场景值
   */
  private String lostSource;

  /**
   * 解绑原因
   */
  private String lostReason;

  /**
   * 解绑酒店名称
   */
  private String lostHotelName;

  /**
   * 更新时间
   */
  private Date updateTime;

  private static final long serialVersionUID = 1L;
}