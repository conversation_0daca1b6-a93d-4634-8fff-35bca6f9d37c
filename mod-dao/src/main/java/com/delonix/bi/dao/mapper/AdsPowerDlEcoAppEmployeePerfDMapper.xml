<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.delonix.bi.dao.mapper.AdsPowerDlEcoAppEmployeePerfDMapper">
  <resultMap id="BaseResultMap" type="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfD">
    <id column="hotel_code" jdbcType="VARCHAR" property="hotelCode" />
    <id column="biz_date" jdbcType="DATE" property="bizDate" />
    <id column="employee_nc_id" jdbcType="VARCHAR" property="employeeNcId" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_dept" jdbcType="VARCHAR" property="employeeDept" />
    <result column="mem_app_nights_n" jdbcType="DOUBLE" property="memAppNightsN" />
    <result column="mem_app_room_amt" jdbcType="DOUBLE" property="memAppRoomAmt" />
    <result column="mem_card_n" jdbcType="BIGINT" property="memCardN" />
    <result column="mem_card_bonus_amt" jdbcType="DOUBLE" property="memCardBonusAmt" />
    <result column="app_add_mem_n" jdbcType="BIGINT" property="appAddMemN" />
    <result column="perf_belong_mem_n" jdbcType="BIGINT" property="perfBelongMemN" />
    <result column="perf_loss_mem_n" jdbcType="BIGINT" property="perfLossMemN" />
    <result column="company_card_n" jdbcType="BIGINT" property="companyCardN" />
    <result column="hotel_capacity_n" jdbcType="BIGINT" property="hotelCapacityN" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    hotel_code, biz_date, employee_nc_id, hotel_name, employee_name, employee_dept, mem_app_nights_n, 
    mem_app_room_amt, mem_card_n, mem_card_bonus_amt, app_add_mem_n, perf_belong_mem_n, 
    perf_loss_mem_n, company_card_n, hotel_capacity_n, update_time
  </sql>
  <select id="selectByExample" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ads_power_dl_eco_app_employee_perf_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ads_power_dl_eco_app_employee_perf_d
    where hotel_code = #{hotelCode,jdbcType=VARCHAR}
      and biz_date = #{bizDate,jdbcType=DATE}
      and employee_nc_id = #{employeeNcId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDKey">
    delete from ads_power_dl_eco_app_employee_perf_d
    where hotel_code = #{hotelCode,jdbcType=VARCHAR}
      and biz_date = #{bizDate,jdbcType=DATE}
      and employee_nc_id = #{employeeNcId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDExample">
    delete from ads_power_dl_eco_app_employee_perf_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfD">
    insert into ads_power_dl_eco_app_employee_perf_d (hotel_code, biz_date, employee_nc_id, 
      hotel_name, employee_name, employee_dept, 
      mem_app_nights_n, mem_app_room_amt, mem_card_n, 
      mem_card_bonus_amt, app_add_mem_n, perf_belong_mem_n, 
      perf_loss_mem_n, company_card_n, hotel_capacity_n, 
      update_time)
    values (#{hotelCode,jdbcType=VARCHAR}, #{bizDate,jdbcType=DATE}, #{employeeNcId,jdbcType=VARCHAR}, 
      #{hotelName,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR}, #{employeeDept,jdbcType=VARCHAR}, 
      #{memAppNightsN,jdbcType=DOUBLE}, #{memAppRoomAmt,jdbcType=DOUBLE}, #{memCardN,jdbcType=BIGINT}, 
      #{memCardBonusAmt,jdbcType=DOUBLE}, #{appAddMemN,jdbcType=BIGINT}, #{perfBelongMemN,jdbcType=BIGINT}, 
      #{perfLossMemN,jdbcType=BIGINT}, #{companyCardN,jdbcType=BIGINT}, #{hotelCapacityN,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfD">
    insert into ads_power_dl_eco_app_employee_perf_d
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hotelCode != null">
        hotel_code,
      </if>
      <if test="bizDate != null">
        biz_date,
      </if>
      <if test="employeeNcId != null">
        employee_nc_id,
      </if>
      <if test="hotelName != null">
        hotel_name,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeeDept != null">
        employee_dept,
      </if>
      <if test="memAppNightsN != null">
        mem_app_nights_n,
      </if>
      <if test="memAppRoomAmt != null">
        mem_app_room_amt,
      </if>
      <if test="memCardN != null">
        mem_card_n,
      </if>
      <if test="memCardBonusAmt != null">
        mem_card_bonus_amt,
      </if>
      <if test="appAddMemN != null">
        app_add_mem_n,
      </if>
      <if test="perfBelongMemN != null">
        perf_belong_mem_n,
      </if>
      <if test="perfLossMemN != null">
        perf_loss_mem_n,
      </if>
      <if test="companyCardN != null">
        company_card_n,
      </if>
      <if test="hotelCapacityN != null">
        hotel_capacity_n,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hotelCode != null">
        #{hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="bizDate != null">
        #{bizDate,jdbcType=DATE},
      </if>
      <if test="employeeNcId != null">
        #{employeeNcId,jdbcType=VARCHAR},
      </if>
      <if test="hotelName != null">
        #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeeDept != null">
        #{employeeDept,jdbcType=VARCHAR},
      </if>
      <if test="memAppNightsN != null">
        #{memAppNightsN,jdbcType=DOUBLE},
      </if>
      <if test="memAppRoomAmt != null">
        #{memAppRoomAmt,jdbcType=DOUBLE},
      </if>
      <if test="memCardN != null">
        #{memCardN,jdbcType=BIGINT},
      </if>
      <if test="memCardBonusAmt != null">
        #{memCardBonusAmt,jdbcType=DOUBLE},
      </if>
      <if test="appAddMemN != null">
        #{appAddMemN,jdbcType=BIGINT},
      </if>
      <if test="perfBelongMemN != null">
        #{perfBelongMemN,jdbcType=BIGINT},
      </if>
      <if test="perfLossMemN != null">
        #{perfLossMemN,jdbcType=BIGINT},
      </if>
      <if test="companyCardN != null">
        #{companyCardN,jdbcType=BIGINT},
      </if>
      <if test="hotelCapacityN != null">
        #{hotelCapacityN,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDExample" resultType="java.lang.Long">
    select count(*) from ads_power_dl_eco_app_employee_perf_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ads_power_dl_eco_app_employee_perf_d
    <set>
      <if test="record.hotelCode != null">
        hotel_code = #{record.hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizDate != null">
        biz_date = #{record.bizDate,jdbcType=DATE},
      </if>
      <if test="record.employeeNcId != null">
        employee_nc_id = #{record.employeeNcId,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelName != null">
        hotel_name = #{record.hotelName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDept != null">
        employee_dept = #{record.employeeDept,jdbcType=VARCHAR},
      </if>
      <if test="record.memAppNightsN != null">
        mem_app_nights_n = #{record.memAppNightsN,jdbcType=DOUBLE},
      </if>
      <if test="record.memAppRoomAmt != null">
        mem_app_room_amt = #{record.memAppRoomAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.memCardN != null">
        mem_card_n = #{record.memCardN,jdbcType=BIGINT},
      </if>
      <if test="record.memCardBonusAmt != null">
        mem_card_bonus_amt = #{record.memCardBonusAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.appAddMemN != null">
        app_add_mem_n = #{record.appAddMemN,jdbcType=BIGINT},
      </if>
      <if test="record.perfBelongMemN != null">
        perf_belong_mem_n = #{record.perfBelongMemN,jdbcType=BIGINT},
      </if>
      <if test="record.perfLossMemN != null">
        perf_loss_mem_n = #{record.perfLossMemN,jdbcType=BIGINT},
      </if>
      <if test="record.companyCardN != null">
        company_card_n = #{record.companyCardN,jdbcType=BIGINT},
      </if>
      <if test="record.hotelCapacityN != null">
        hotel_capacity_n = #{record.hotelCapacityN,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ads_power_dl_eco_app_employee_perf_d
    set hotel_code = #{record.hotelCode,jdbcType=VARCHAR},
      biz_date = #{record.bizDate,jdbcType=DATE},
      employee_nc_id = #{record.employeeNcId,jdbcType=VARCHAR},
      hotel_name = #{record.hotelName,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_dept = #{record.employeeDept,jdbcType=VARCHAR},
      mem_app_nights_n = #{record.memAppNightsN,jdbcType=DOUBLE},
      mem_app_room_amt = #{record.memAppRoomAmt,jdbcType=DOUBLE},
      mem_card_n = #{record.memCardN,jdbcType=BIGINT},
      mem_card_bonus_amt = #{record.memCardBonusAmt,jdbcType=DOUBLE},
      app_add_mem_n = #{record.appAddMemN,jdbcType=BIGINT},
      perf_belong_mem_n = #{record.perfBelongMemN,jdbcType=BIGINT},
      perf_loss_mem_n = #{record.perfLossMemN,jdbcType=BIGINT},
      company_card_n = #{record.companyCardN,jdbcType=BIGINT},
      hotel_capacity_n = #{record.hotelCapacityN,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfD">
    update ads_power_dl_eco_app_employee_perf_d
    <set>
      <if test="hotelName != null">
        hotel_name = #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeeDept != null">
        employee_dept = #{employeeDept,jdbcType=VARCHAR},
      </if>
      <if test="memAppNightsN != null">
        mem_app_nights_n = #{memAppNightsN,jdbcType=DOUBLE},
      </if>
      <if test="memAppRoomAmt != null">
        mem_app_room_amt = #{memAppRoomAmt,jdbcType=DOUBLE},
      </if>
      <if test="memCardN != null">
        mem_card_n = #{memCardN,jdbcType=BIGINT},
      </if>
      <if test="memCardBonusAmt != null">
        mem_card_bonus_amt = #{memCardBonusAmt,jdbcType=DOUBLE},
      </if>
      <if test="appAddMemN != null">
        app_add_mem_n = #{appAddMemN,jdbcType=BIGINT},
      </if>
      <if test="perfBelongMemN != null">
        perf_belong_mem_n = #{perfBelongMemN,jdbcType=BIGINT},
      </if>
      <if test="perfLossMemN != null">
        perf_loss_mem_n = #{perfLossMemN,jdbcType=BIGINT},
      </if>
      <if test="companyCardN != null">
        company_card_n = #{companyCardN,jdbcType=BIGINT},
      </if>
      <if test="hotelCapacityN != null">
        hotel_capacity_n = #{hotelCapacityN,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where hotel_code = #{hotelCode,jdbcType=VARCHAR}
      and biz_date = #{bizDate,jdbcType=DATE}
      and employee_nc_id = #{employeeNcId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfD">
    update ads_power_dl_eco_app_employee_perf_d
    set hotel_name = #{hotelName,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_dept = #{employeeDept,jdbcType=VARCHAR},
      mem_app_nights_n = #{memAppNightsN,jdbcType=DOUBLE},
      mem_app_room_amt = #{memAppRoomAmt,jdbcType=DOUBLE},
      mem_card_n = #{memCardN,jdbcType=BIGINT},
      mem_card_bonus_amt = #{memCardBonusAmt,jdbcType=DOUBLE},
      app_add_mem_n = #{appAddMemN,jdbcType=BIGINT},
      perf_belong_mem_n = #{perfBelongMemN,jdbcType=BIGINT},
      perf_loss_mem_n = #{perfLossMemN,jdbcType=BIGINT},
      company_card_n = #{companyCardN,jdbcType=BIGINT},
      hotel_capacity_n = #{hotelCapacityN,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where hotel_code = #{hotelCode,jdbcType=VARCHAR}
      and biz_date = #{bizDate,jdbcType=DATE}
      and employee_nc_id = #{employeeNcId,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.delonix.bi.dao.model.AdsPowerDlEcoAppEmployeePerfDExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ads_power_dl_eco_app_employee_perf_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert">
    <if test="items.get(0) != null">
            insert into ads_power_dl_eco_app_employee_perf_d  (hotel_code, biz_date, employee_nc_id, 
        hotel_name, employee_name, employee_dept, 
        mem_app_nights_n, mem_app_room_amt, mem_card_n, 
        mem_card_bonus_amt, app_add_mem_n, perf_belong_mem_n, 
        perf_loss_mem_n, company_card_n, hotel_capacity_n, 
        update_time)
      values
      <foreach collection="items" item="item" index="index" separator=",">
      (#{item.hotelCode,jdbcType=VARCHAR}, #{item.bizDate,jdbcType=DATE}, #{item.employeeNcId,jdbcType=VARCHAR}, 
        #{item.hotelName,jdbcType=VARCHAR}, #{item.employeeName,jdbcType=VARCHAR}, #{item.employeeDept,jdbcType=VARCHAR}, 
        #{item.memAppNightsN,jdbcType=DOUBLE}, #{item.memAppRoomAmt,jdbcType=DOUBLE}, #{item.memCardN,jdbcType=BIGINT}, 
        #{item.memCardBonusAmt,jdbcType=DOUBLE}, #{item.appAddMemN,jdbcType=BIGINT}, #{item.perfBelongMemN,jdbcType=BIGINT}, 
        #{item.perfLossMemN,jdbcType=BIGINT}, #{item.companyCardN,jdbcType=BIGINT}, #{item.hotelCapacityN,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
      </foreach>
    </if>
  </insert>

  <select id="sumPerformanceByHotel" resultType="com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO">
    SELECT
      SUM(perf_belong_mem_n) as attributedMembers,
      SUM(perf_loss_mem_n) as lostMembers,
      SUM(app_add_mem_n) as newAppMembers,
      SUM(mem_app_nights_n) as roomNightsN,
      SUM(mem_app_room_amt) as roomAmt,
      SUM(mem_card_n) as bdCardCnt,
      SUM(company_card_n) as enterpriseCards,
      SUM(mem_card_bonus_amt) as bonusAmt,
      MAX(update_time) as updateTime,
      SUM(mem_card_amt) as memCardAmt,
      SUM(card_room_nights_n) as cardRoomNightsN,
      SUM(mem_card_one_n) as memCardOneN,
      SUM(mem_card_one_bonus_amt) as memCardOneBonusAmt,
      SUM(mem_card_second_n) as memCardSecondN,
      SUM(mem_card_second_bonus_amt) as memCardSecondBonusAmt,
      SUM(mem_card_third_n) as memCardThirdN,
      SUM(mem_card_third_bonus_amt) as memCardThirdBonusAmt,
      ROUND(CAST(
              (SELECT SUM(daily_mem_app_nights_n) / NULLIF(SUM(daily_hotel_capacity_n), 0)
               FROM (
                      SELECT
                        biz_date,
                        SUM(mem_app_nights_n) as daily_mem_app_nights_n,
                        MAX(hotel_capacity_n) as daily_hotel_capacity_n
                      FROM ads_power_dl_eco_app_employee_perf_d
                      WHERE hotel_code = #{hotelCode}
                        AND biz_date BETWEEN #{startDate} AND #{endDate}
                      GROUP BY biz_date
                    ) daily_stats
              ) * 100 AS numeric
            ), 2) as hundredRoomNightsN
    FROM ads_power_dl_eco_app_employee_perf_d
    WHERE hotel_code = #{hotelCode}
      AND biz_date BETWEEN #{startDate} AND #{endDate}
  </select>

  <select id="sumPerformanceByHotelAndUser" resultType="com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO">
    SELECT
      SUM(perf_belong_mem_n) as attributedMembers,
      SUM(perf_loss_mem_n) as lostMembers,
      SUM(app_add_mem_n) as newAppMembers,
      SUM(mem_app_nights_n) as roomNightsN,
      SUM(mem_app_room_amt) as roomAmt,
      SUM(mem_card_n) as bdCardCnt,
      SUM(company_card_n) as enterpriseCards,
      SUM(mem_card_bonus_amt) as bonusAmt,
      MAX(update_time) as updateTime,
      CASE
        WHEN MAX(hotel_capacity_n) > 0
          THEN ROUND(CAST(SUM(mem_app_nights_n) AS numeric) / CAST(SUM(hotel_capacity_n) * 100 AS numeric), 2)
        END AS hundredRoomNightsN
    FROM ads_power_dl_eco_app_employee_perf_d
    WHERE hotel_code = #{hotelCode}
      AND employee_nc_id = #{ucId}
      and biz_date between #{startDate} and #{endDate};
  </select>

  <!-- 门店售卡排行榜 -->
  <select id="getTopHotelCardSellRanking" resultType="com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO">
    SELECT
      hotel_code as hotelCode,
      hotel_name as hotelName,
      SUM(mem_card_n) as bdCardCnt,
      SUM(mem_card_bonus_amt) as bonusAmt
    FROM ads_power_dl_eco_app_employee_perf_d
    WHERE biz_date BETWEEN #{startDate} AND #{endDate}
    GROUP BY hotel_code, hotel_name
    ORDER BY SUM(mem_card_bonus_amt) DESC
    LIMIT 5
  </select>

  <!-- 员工售卡排行榜 -->
  <select id="getTopEmployeeCardSellRanking" resultType="com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO">
    SELECT
      hotel_code as hotelCode,
      hotel_name as hotelName,
      employee_nc_id as employeeId,
      employee_name as employeeName,
      SUM(mem_card_n) as bdCardCnt,
      SUM(mem_card_bonus_amt) as bonusAmt
    FROM ads_power_dl_eco_app_employee_perf_d
    WHERE biz_date BETWEEN #{startDate} AND #{endDate}
    GROUP BY hotel_code, hotel_name, employee_nc_id, employee_name
    ORDER BY SUM(mem_card_bonus_amt) DESC
    LIMIT 5
  </select>

  <!-- 查询券类型统计数据 - 按员工维度 -->
  <select id="queryVoucherDataByEmployee" resultType="com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO">
    SELECT
      hotel_code as hotelCode,
      hotel_name as hotelName,
      employee_nc_id as employeeId,
      employee_name as employeeName,
      employee_dept as employeeDept,
      <choose>
        <when test="voucherType == 'APP_DISCOUNT_VOUCHER'">
          SUM(COALESCE(magic_app_send_n, 0)) as voucherSendCount,
          SUM(COALESCE(magic_app_use_n, 0)) as voucherUseCount
        </when>
        <when test="voucherType == 'BREAKFAST_VOUCHER'">
          SUM(COALESCE(magic_breakfast_send_n, 0)) as voucherSendCount,
          SUM(COALESCE(magic_breakfast_use_n, 0)) as voucherUseCount
        </when>
        <when test="voucherType == 'UPGRADE_ROOM_VOUCHER'">
          SUM(COALESCE(magic_free_send_n, 0)) as voucherSendCount,
          SUM(COALESCE(magic_free_use_n, 0)) as voucherUseCount
        </when>
        <when test="voucherType == 'DELAY_CHECKOUT_VOUCHER'">
          SUM(COALESCE(magic_delay_send_n, 0)) as voucherSendCount,
          SUM(COALESCE(magic_delay_use_n, 0)) as voucherUseCount
        </when>
        <otherwise>
          <!-- 酒店法宝类型，暂时使用所有券类型的汇总 -->
          SUM(COALESCE(magic_app_send_n, 0) + COALESCE(magic_breakfast_send_n, 0) + COALESCE(magic_free_send_n, 0) + COALESCE(magic_delay_send_n, 0)) as voucherSendCount,
          SUM(COALESCE(magic_app_use_n, 0) + COALESCE(magic_breakfast_use_n, 0) + COALESCE(magic_free_use_n, 0) + COALESCE(magic_delay_use_n, 0)) as voucherUseCount
        </otherwise>
      </choose>
    FROM ads_power_dl_eco_app_employee_perf_d
    WHERE hotel_code = #{hotelCode}
      AND biz_date BETWEEN #{startDate} AND #{endDate}
      AND employee_dept is not null and employee_dept != ''
    GROUP BY hotel_code, hotel_name, employee_nc_id, employee_name, employee_dept
  </select>

  <!-- 查询百达卡统计数据 - 按员工维度 -->
  <select id="queryBdwCardDataByEmployee" resultType="com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO">
    SELECT
      hotel_code as hotelCode,
      hotel_name as hotelName,
      employee_nc_id as employeeId,
      employee_name as employeeName,
      employee_dept as employeeDept,
      <choose>
        <when test="cardType == 'QIHANG_CARD'">
          SUM(COALESCE(mem_card_one_n, 0)) as bdCardCnt,
          SUM(COALESCE(mem_card_one_bonus_amt, 0)) as bonusAmt
        </when>
        <when test="cardType == 'MANLU_CARD'">
          SUM(COALESCE(mem_card_second_n, 0)) as bdCardCnt,
          SUM(COALESCE(mem_card_second_bonus_amt, 0)) as bonusAmt
        </when>
        <when test="cardType == 'KAITUO_CARD'">
          SUM(COALESCE(mem_card_third_n, 0)) as bdCardCnt,
          SUM(COALESCE(mem_card_third_bonus_amt, 0)) as bonusAmt
        </when>
        <otherwise>
          <!-- 百达卡汇总，所有卡类型的汇总 -->
          SUM(COALESCE(mem_card_one_n, 0) + COALESCE(mem_card_second_n, 0) + COALESCE(mem_card_third_n, 0)) as bdCardCnt,
          SUM(COALESCE(mem_card_one_bonus_amt, 0) + COALESCE(mem_card_second_bonus_amt, 0) + COALESCE(mem_card_third_bonus_amt, 0)) as bonusAmt
        </otherwise>
      </choose>
    FROM ads_power_dl_eco_app_employee_perf_d
    WHERE hotel_code = #{hotelCode}
      AND biz_date BETWEEN #{startDate} AND #{endDate}
      AND employee_dept is not null and employee_dept != ''
    GROUP BY hotel_code, hotel_name, employee_nc_id, employee_name, employee_dept
  </select>
</mapper>