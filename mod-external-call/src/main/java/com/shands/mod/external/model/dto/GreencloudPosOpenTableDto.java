package com.shands.mod.external.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/17
 * @desc 绿云pos开台请求基础类dto
*/

@Data
public class GreencloudPosOpenTableDto {

  /**
   * 酒店集团编码
   */
  private String hotelGroupCode;

  /**
   * 酒店编码
   */
  private String hotelCode;

  /**
   * 参数返回格式 默认：xml 支持：json
   */
  private String format = "json";

  /**
   * 请求应用appkey
   */
  private String appKey;

  /**
   * secret
   */
  private String secret;

  /**
   * 接口版本号
   */
  private String v = "1.0";

  /**
   * 方法名
   */
  private String method;

  /**
   * 请求url
   */
  private String url;

  /**
   * 营业点
   */
  private String pccode;

  /**
   *营业日期
   */
  private String bizdate;

  /**
   *班次
   */
  private String shift = "1";

  /**
   *桌号
   */
  private String tableno;
}
