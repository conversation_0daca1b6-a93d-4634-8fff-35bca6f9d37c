package com.shands.mod.external.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/17
 * @desc 绿云pos入账结算请求基础类dto
*/

@Data
public class GreencloudEntrySettlementDto {

  /**
   * 酒店集团编码
   */
  private String hotelGroupCode;

  /**
   * 酒店编码
   */
  private String hotelCode;

  /**
   * 参数返回格式 默认：xml 支持：json
   */
  private String format = "json";

  /**
   * 请求应用appkey
   */
  private String appKey;

  /**
   * secret
   */
  private String secret;

  /**
   * 接口版本号
   */
  private String v = "1.0";

  /**
   * 方法名
   */
  private String method;

  /**
   * 请求url
   */
  private String url;

  /**
   * 营业点
   */
  private String pccode;

  /**
   * 单号
   */
  private String accnt;

  /**
   * 主单原状态 默认I
   */
  private String osta = "I";

  /**
   * 桌号
   */
  private String tableno;

  /**
   * 班次 默认 1
   */
  private String shift = "1";

  /**
   * 营业日期
   */
  private String bizdate;

  /**
   * 折扣率 没有传：0.00
   */
  private String dsc = "0.00";

  /**
   * 折扣金额 没有传：0.00
   */
  private String dscAmount = "0.00";

  /**
   * 服务费率 没有传0.00
   */
  private String srv = "0.00";

  /**
   * 服务费用 没有传0.00
   */
  private String srvAmount = "0.00";

  /**
   * 税率 没有传0.00
   */
  private String tax = "0.00";

  /**
   * 税费 没有传0.00
   */
  private String taxAmount = "0.00";

  /**
   * 消费金额 没有传0.00
   */
  private String amount;

  /**
   * 付款金额 没有传0.00
   */
  private String payAmount;

  /**
   * 交易单号
   */
  private String billNo;

  /**
   * 菜品数量总量
   */
  private String pluNum;

  /**
   * 会员卡号
   */
  private String cardNo = "";

  /**
   * 会员卡信息
   */
  private String cardInfo = "";

  /**
   * 备注信息
   */
  private String remark = "";
}
