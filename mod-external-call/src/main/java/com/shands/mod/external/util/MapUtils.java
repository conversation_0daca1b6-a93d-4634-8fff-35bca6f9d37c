package com.shands.mod.external.util;

import lombok.extern.slf4j.Slf4j;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2021/5/17
 * @desc map工具类
*/
@Slf4j
public class MapUtils {

  /**
   * 对象转map for greencloud
   * @param obj
   * @return
   */
  public static TreeMap<String,String> objectToMapForGreencloud(Object obj){

    if(obj == null){
      return null;
    }

    TreeMap<String,String> params = new TreeMap<>();
    Class<?> clazz = obj.getClass();

    for(Field field : clazz.getDeclaredFields()){

      field.setAccessible(true);
      String fileName = field.getName();
      String value = null;
      try {
        value = String.valueOf(field.get(obj) != null ? field.get(obj) : null);
      } catch (IllegalAccessException e) {
        log.error(e.getMessage(), e);
      }

      if(!"secret".equals(fileName) && !"url".equals(fileName)){
        params.put(fileName,value);
      }
    }

    return params;
  }

  /**
   * 对象转map for greencloud
   * @param obj
   * @return
   */
  public static TreeMap<String,Object> objectToMapForGreencloudToObj(Object obj){

    if(obj == null){
      return null;
    }

    TreeMap<String,Object> params = new TreeMap<>();
    Class<?> clazz = obj.getClass();

    for(Field field : clazz.getDeclaredFields()){

      field.setAccessible(true);
      String fileName = field.getName();
      String value = null;
      try {
        value = String.valueOf(field.get(obj) != null ? field.get(obj) : null);
      } catch (IllegalAccessException e) {
        log.error(e.getMessage(), e);
      }

      if(!"secret".equals(fileName) && !"url".equals(fileName)){
        params.put(fileName,value);
      }
    }

    return params;
  }
}
