package com.shands.mod.external.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/7 16:09
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ModCommentReq {

  @ApiModelProperty(value = "第几页")
  private Integer current;
  @ApiModelProperty(value = "每页显示数量")
  private Integer size;
  @ApiModelProperty(value = "通宝用户")
  private String user;
  @ApiModelProperty(value = "酒店code")
  private String hotelCode;
  @NotNull(message = "状态不能为空")
  @ApiModelProperty(value = "未回复0，已回复1，全部2")
  private Integer status;
  @NotNull(message = "分数类型为空")
  @ApiModelProperty(value = "不低于0，低于1")
  private Integer checkFraction;


  public Integer getCurrent() {
    return current;
  }

  public void setCurrent(Integer current) {
    this.current = current;
  }

  public Integer getSize() {
    return size;
  }

  public void setSize(Integer size) {
    this.size = size;
  }

  public String getUser() {
    return user;
  }

  public void setUser(String user) {
    this.user = user;
  }

  public String getHotelCode() {
    return hotelCode;
  }

  public void setHotelCode(String hotelCode) {
    this.hotelCode = hotelCode;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Integer getCheckFraction() {
    return checkFraction;
  }

  public void setCheckFraction(Integer checkFraction) {
    this.checkFraction = checkFraction;
  }
}
