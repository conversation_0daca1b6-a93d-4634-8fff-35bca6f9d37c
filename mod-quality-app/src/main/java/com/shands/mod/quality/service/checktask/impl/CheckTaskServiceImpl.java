package com.shands.mod.quality.service.checktask.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shands.mod.config.RocketMqConfig;
import com.shands.mod.dao.mapper.quality.QtCheckTaskMapper;
import com.shands.mod.dao.mapper.quality.QtHotelCheckMapper;
import com.shands.mod.dao.mapper.quality.QtTaskUserMapper;
import com.shands.mod.dao.mapper.syncuc.ModDeptDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.enums.quality.AttributesTypeEnum;
import com.shands.mod.dao.model.enums.quality.CheckTypeEnum;
import com.shands.mod.dao.model.enums.quality.TaskFinishStatusTypeEnum;
import com.shands.mod.dao.model.enums.quality.TaskStatusTypeEnum;
import com.shands.mod.dao.model.enums.quality.TimeTypeEnum;
import com.shands.mod.dao.model.quality.bo.AddCheckTaskBo;
import com.shands.mod.dao.model.quality.bo.CheckTaskListBo;
import com.shands.mod.dao.model.quality.bo.StaffListBo;
import com.shands.mod.dao.model.quality.po.QtCheckTask;
import com.shands.mod.dao.model.quality.po.QtHotelCheck;
import com.shands.mod.dao.model.quality.po.QtTaskUser;
import com.shands.mod.dao.model.quality.vo.CheckTaskListVo;
import com.shands.mod.dao.model.quality.vo.DeptListVo;
import com.shands.mod.dao.model.quality.vo.StaffListVo;
import com.shands.mod.quality.service.checktask.CheckTaskService;
import com.shands.mod.quality.utils.DatesUtil;
import com.shands.mod.rocketmq.RocketMqProducer;
import com.shands.mod.vo.ResultVO;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CheckTaskServiceImpl implements CheckTaskService {

  @Resource
  private QtCheckTaskMapper qtCheckTaskMapper;
  @Resource
  private ModUserDao modUserDao;
  @Resource
  private QtTaskUserMapper qtTaskUserMapper;
  @Resource
  private ModDeptDao modDeptDao;
  @Resource
  private QtHotelCheckMapper qtHotelCheckMapper;
  @Resource
  private RocketMqProducer rocketMqProducer;

  @Override
  //@Transactional(rollbackFor = Exception.class)
  public ResultVO addcheckTask(AddCheckTaskBo addCheckTaskBo) {
    try {
      if (CheckTypeEnum.HOTEL_SELF.name().equals(addCheckTaskBo.getProperty())) {
        //判断任务时间是否在审核时间内
        QtHotelCheck qtHotelCheck = qtHotelCheckMapper.queryById(addCheckTaskBo.getCheckId());

        //只有在集团巡检创建任务的时候，需要判断是否在审核时间范围内
        if (AttributesTypeEnum.BLOC_UNIVERSA.name().equals(qtHotelCheck.getInspectType())) {
          if (addCheckTaskBo.getTaskStartTime().getTime() < qtHotelCheck.getAssessmentStartTime()
              .getTime()
              || addCheckTaskBo.getTaskStartTime().getTime() > qtHotelCheck.getAssessmentEndTime()
              .getTime()) {
            return ResultVO.failed("任务开始时间不在审核时间范围内！");
          }
        }
      }
      QtCheckTask qtCheckTask = new QtCheckTask();
      //酒店检查表专属
      qtCheckTask.setCreateTime(new Date());
      qtCheckTask.setIfDelete(false);
      qtCheckTask.setCheckType(addCheckTaskBo.getProperty());
      qtCheckTask.setCheckId(addCheckTaskBo.getCheckId());
      qtCheckTask.setTaskTitle(addCheckTaskBo.getTaskTitle());
      qtCheckTask.setHotelId(addCheckTaskBo.getHotelId());
      qtCheckTask.setTaskStartTime(addCheckTaskBo.getTaskStartTime());
      qtCheckTask.setTaskEndTime(addCheckTaskBo.getTaskEndTime());
      qtCheckTask.setHotelMsgUserId(addCheckTaskBo.getHotelMsgUserId());
      qtCheckTask.setCreateUser(addCheckTaskBo.getCreateUser());

      qtCheckTask.setHotelMsgTimeType(addCheckTaskBo.getHotelUserTimeType());
      qtCheckTask.setMsgCheckerTimeType(addCheckTaskBo.getMsgCheckerTimeType());
      qtCheckTask.setHotelMsgTime(addCheckTaskBo.getHotelMsgTime());
      qtCheckTask.setMsgCheckerTime(addCheckTaskBo.getMsgCheckerTime());

      qtCheckTask.setTaskStatus(TaskStatusTypeEnum.NOT_STARTED.name());
      qtCheckTask.setTaskFinishStatus(TaskFinishStatusTypeEnum.PENDING.name());
      qtCheckTask.setTaskType(addCheckTaskBo.getTaskType());
      qtCheckTaskMapper.insert(qtCheckTask);
      if (StrUtil.isNotEmpty(addCheckTaskBo.getUserIds())) {
        String[] split = addCheckTaskBo.getUserIds().split(",");
        for (String s : split) {
          QtTaskUser qtTaskUser = new QtTaskUser();
          qtTaskUser.setCreateTime(new Date());
          qtTaskUser.setIfDelete(false);
          qtTaskUser.setCreateUser(addCheckTaskBo.getCreateUser());
          qtTaskUser.setTaskId(qtCheckTask.getId());
          qtTaskUser.setUserId(Integer.valueOf(s));
          qtTaskUserMapper.insert(qtTaskUser);
        }
      }

      //延迟队列消息通知处理
      addCheckTaskBo.setTaskId(qtCheckTask.getId());
      if ("HOTEL_SELF".equals(addCheckTaskBo.getProperty())) {
        //直接发送
        this.sendCheckQueue(5000, addCheckTaskBo);
      } else {
        this.delayNotice(addCheckTaskBo);
      }

//      //任务开始时间处理
//      addCheckTaskBo.setTimeType("start");
//      Date taskStartTime = this.updateHMS1(addCheckTaskBo.getTaskStartTime());
//      //通知任务开始队列
//      long seconds = DatesUtil.betweenS(new Date(), taskStartTime);
//      if (seconds <= 0) {
//        //直接进行推送
//        this.sendTaskOverdueQueue(5000, addCheckTaskBo);
//      } else {
//        long l = DateUtil.betweenMs(new Date(), taskStartTime);
//        this.sendTaskOverdueQueue(l, addCheckTaskBo);
//      }
//
//      //任务过期时间处理
//      addCheckTaskBo.setTimeType("end");
//      Date taskEndTime = this.updateHMS2(addCheckTaskBo.getTaskEndTime());
//      seconds = DatesUtil.betweenS(new Date(), taskEndTime);
//      if (seconds <= 0) {
//        //直接进行推送
//        this.sendTaskOverdueQueue(5000, addCheckTaskBo);
//      } else {
//        long l = DateUtil.betweenMs(new Date(), taskEndTime);
//        this.sendTaskOverdueQueue(l, addCheckTaskBo);
//      }
    } catch (Exception e) {
      log.error("添加任务表异常", e);
      //手动回滚事务
      //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
      ResultVO.failed("添加任务表异常");
    }
    return ResultVO.success();
  }

  @Override
  public PageInfo<CheckTaskListVo> selectCheckTaskList(CheckTaskListBo checkTaskListBo) {

    List<CheckTaskListVo> checkTaskListVoList = null;
    PageInfo<CheckTaskListVo> pageInfo = null;
    try {
      PageHelper.startPage(checkTaskListBo.getPageNo(), checkTaskListBo.getPageSize());
      checkTaskListVoList = qtCheckTaskMapper.selectCheckTaskList(checkTaskListBo);
      checkTaskListBo.setTaskFinishStatus(TaskFinishStatusTypeEnum.CHECK.name());
      pageInfo = new PageInfo<>(checkTaskListVoList);
      pageInfo.setNavigateLastPage(selectCheckNumber(checkTaskListBo));
    } catch (Exception e) {
      log.error("selectCheckTaskList_查询任务列表异常_ " + JSON.toJSONString(checkTaskListVoList), e);
    }
    return pageInfo;


  }

  @Override
  public Integer selectCheckNumber(CheckTaskListBo checkTaskListBo) {
    List<CheckTaskListVo> checkTaskListVoList = null;
    try {

      checkTaskListBo.setTaskFinishStatus(TaskFinishStatusTypeEnum.CHECK.name());
      checkTaskListVoList = qtCheckTaskMapper.selectCheckTaskList(checkTaskListBo);
      return checkTaskListVoList.size();
    } catch (Exception e) {
      log.error("selectCheckTaskList_查询任务列表异常_ " + JSON.toJSONString(checkTaskListVoList), e);
    }
    return 0;
  }

  @Override
  public Integer cancelCheckTask(Integer taskId,Integer userId) {
    QtCheckTask qtCheckTask = qtCheckTaskMapper.queryById(taskId);
    //进行逻辑删除
    qtCheckTask.setIfDelete(true);
    qtCheckTask.setTaskFinishStatus(TaskFinishStatusTypeEnum.CANCEL.name());
    int update = qtCheckTaskMapper.update(qtCheckTask);
    //mq推送给检查人取消消息
    this.sendCancelTaskQueue(taskId,userId);
    return update;
  }

  @Override
  public List<StaffListVo> staffList(StaffListBo staffListBo) {
    return modUserDao.showStaffList(staffListBo);
  }

  @Override
  public List<DeptListVo> deptStaffList(StaffListBo staffListBo) {
    //本酒店所有部门
    try {
      List<DeptListVo> dept = modDeptDao.getDept(staffListBo.getHotelId());

      if (dept == null || dept.size() == 0) {
        log.info("deptStaffList_未查询到该酒店下的部门");
        return null;
      }
      for (DeptListVo deptListVo : dept) {
        //添加部门id
        staffListBo.setDeptId(deptListVo.getDeptId());
        List<StaffListVo> staffListVos = modUserDao.showStaffList(staffListBo);
        deptListVo.setStaffListVoList(staffListVos);
      }
      return dept;
    } catch (Exception e) {
      log.error("deptStaffList_查询部门异常 参数_ " + JSON.toJSONString(staffListBo), e);
    }
    return null;
  }


  //延迟队列时间处理
  @Override
  public Boolean delayNotice(AddCheckTaskBo addCheckTaskBo) {
    try {
      // 推送时间ms
      long pushStartMs = 0;
      long pushCheckStartMs = 0;
      DateTime differDayTime;
      //任务开始时间
      Date taskStartTime = this.updateHMS1(addCheckTaskBo.getTaskStartTime());

      //巡查人时间通知类型
      if (addCheckTaskBo.getMsgCheckerTimeType().equals(TimeTypeEnum.TWO_DAY_IN_ADVANCE.name())) {
        //提前二天
        differDayTime = DateUtil.offsetDay(taskStartTime, -2);
        long seconds = DatesUtil.betweenS(new Date(), updateHMS(differDayTime));
        if (seconds <= 0) {
          //直接进行推送
          pushCheckStartMs = 0;
        } else {
          pushCheckStartMs = DateUtil.betweenMs(new Date(), updateHMS(differDayTime));
        }
      } else if (addCheckTaskBo.getMsgCheckerTimeType()
          .equals(TimeTypeEnum.THREE_DAY_IN_ADVANCE.name())) {
        //提前三天
        differDayTime = DateUtil.offsetDay(taskStartTime, -3);
        long seconds = DatesUtil.betweenS(new Date(), updateHMS(differDayTime));
        if (seconds <= 0) {
          //直接进行推送
          pushCheckStartMs = 0;
        } else {
          pushCheckStartMs = DateUtil.betweenMs(new Date(), updateHMS(differDayTime));
        }
      } else if (addCheckTaskBo.getMsgCheckerTimeType()
          .equals(TimeTypeEnum.FIVE_DAY_IN_ADVANCE.name())) {
        //提前五天
        differDayTime = DateUtil.offsetDay(taskStartTime, -5);
        long seconds = DatesUtil.betweenS(new Date(), updateHMS(differDayTime));
        if (seconds <= 0) {
          //直接进行推送
          pushCheckStartMs = 0;
        } else {
          pushCheckStartMs = DateUtil.betweenMs(new Date(), updateHMS(differDayTime));
        }
      } else {
        //自定义
        Date msgCheckerTime = this.updateHMS(addCheckTaskBo.getMsgCheckerTime());
        long seconds = DatesUtil.betweenS(new Date(), msgCheckerTime);
        if (seconds <= 0) {
          //直接进行推送
          pushCheckStartMs = 0;
        } else {
          pushCheckStartMs = DateUtil.betweenMs(new Date(), msgCheckerTime);
        }
      }

      //----------------------------------------------------------------------------------------------
      //酒店时间通知类型
      if (addCheckTaskBo.getHotelUserTimeType().equals(TimeTypeEnum.IN_THE_DAY.name())) {
        //当天直接推送
        pushStartMs = 0;
      } else if (addCheckTaskBo.getHotelUserTimeType()
          .equals(TimeTypeEnum.ONE_DAY_IN_ADVANCE.name())) {
        differDayTime = DateUtil.offsetDay(taskStartTime, -1);
        long seconds = DatesUtil.betweenS(new Date(), updateHMS(differDayTime));
        if (seconds <= 0) {
          //直接进行推送
        } else {
          pushStartMs = DateUtil.betweenMs(new Date(), updateHMS(differDayTime));
        }
      } else if (addCheckTaskBo.getHotelUserTimeType()
          .equals(TimeTypeEnum.TWO_DAY_IN_ADVANCE.name())) {
        differDayTime = DateUtil.offsetDay(taskStartTime, -2);
        long seconds = DatesUtil.betweenS(new Date(), updateHMS(differDayTime));
        if (seconds <= 0) {
          //直接进行推送
        } else {
          pushStartMs = DateUtil.betweenMs(new Date(), updateHMS(differDayTime));
        }
      } else {
        //自定义
        Date hotelMsgTime = this.updateHMS(addCheckTaskBo.getHotelMsgTime());
        long seconds = DatesUtil.betweenS(new Date(), hotelMsgTime);
        if (seconds <= 0) {
          //直接进行推送
          pushStartMs = 0;
        } else {
          pushStartMs = DateUtil.betweenMs(new Date(), hotelMsgTime);
        }
      }
      //发送检查人推送
      sendCheckQueue(pushCheckStartMs, addCheckTaskBo);
      //酒店队列推送
      sendHotelQueue(pushStartMs, addCheckTaskBo);
    } catch (Exception e) {
      log.error("延迟队列时间处理{}", e);
      return false;
    }
    return true;
  }

  /**
   * 修改时间时分秒到8点进行推送
   */
  private Date updateHMS(Date date) throws Exception {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 08:00:00");
    String d1 = simpleDateFormat.format(date);
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date res = format.parse(d1);
    return res;
  }

  private Date updateHMS1(Date date) throws Exception {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
    String d1 = simpleDateFormat.format(date);
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date res = format.parse(d1);
    return res;
  }

  private Date updateHMS2(Date date) throws Exception {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
    String d1 = simpleDateFormat.format(date);
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date res = format.parse(d1);
    return res;
  }

  /**
   * 发送检查人队列
   *
   * @param ms
   */
  private void sendCheckQueue(long ms, AddCheckTaskBo addCheckTaskBo) {
    Map<String, Object> sendMap = new HashMap<>();
    sendMap.put("checkTaskBo", JSONObject.toJSONString(addCheckTaskBo));
    //加入延迟队列
    rocketMqProducer.syncSendDelayMessage(RocketMqConfig.QUALITY_CHECK_PUSH_TOPIC,
        JSONObject.toJSONString(sendMap), ms);
  }

  /**
   * 发送酒店队列
   *
   * @param ms
   */
  private void sendHotelQueue(long ms, AddCheckTaskBo addCheckTaskBo) {
    Map<String, Object> sendMap = new HashMap<>();
    sendMap.put("checkTaskBo", JSONObject.toJSONString(addCheckTaskBo));
    //加入延迟队列
    rocketMqProducer.syncSendDelayMessage(RocketMqConfig.QUALITY_HOTEL_PUSH_TOPIC,
        JSONObject.toJSONString(sendMap), ms);
  }

  /**
   * 发送任务开始消息队列
   *
   * @param ms
   */
  private void sendTaskOverdueQueue(long ms, AddCheckTaskBo addCheckTaskBo) {
    Map<String, Object> sendMap = new HashMap<>();
    sendMap.put("checkTaskBo", JSONObject.toJSONString(addCheckTaskBo));
    //加入延迟队列
    rocketMqProducer.syncSendDelayMessage(RocketMqConfig.QUALITY_TASK_OVER_PUSH_TOPIC,
        JSONObject.toJSONString(sendMap), ms);
  }


  /**
   * 发送任务取消消息队列
   *
   *
   */
  private void sendCancelTaskQueue(Integer taskId,Integer userId) {
    Map<String, Object> sendMap = new HashMap<>();
    sendMap.put("taskId", taskId);
    sendMap.put("userId", userId);
    //加入延迟队列
    rocketMqProducer.syncSendMessage(RocketMqConfig.QUALITY_CANCEL_PUSH_TOPIC,
        JSONObject.toJSONString(sendMap));
  }


  /*public static void main(String[] args) throws ParseException {
    //SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 14:00:20");
    //Date date = new Date();
    //String d1= simpleDateFormat.format(date);
    //SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    //Date res = format.parse(d1);
    //System.out.println(d1);
    //long between = DateUtil.between(new Date(), res, DateUnit.MS);
    //System.out.println(between);

    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date startTime = dateFormat.parse("2019-11-6 09:30:00");
    Date endTime = dateFormat.parse("2019-11-6 09:29:00");
    long l = DatesUtil.betweenS(startTime, endTime);
    System.out.println(l);

  }*/
}
