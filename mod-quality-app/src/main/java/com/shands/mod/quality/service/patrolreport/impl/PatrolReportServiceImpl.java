package com.shands.mod.quality.service.patrolreport.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.mapper.quality.QtBlocCheckMapper;
import com.shands.mod.dao.mapper.quality.QtCheckAnswerMapper;
import com.shands.mod.dao.mapper.quality.QtCheckDetailsMapper;
import com.shands.mod.dao.mapper.quality.QtCheckDictionaryMapper;
import com.shands.mod.dao.mapper.quality.QtCheckTaskMapper;
import com.shands.mod.dao.mapper.quality.QtHotelCheckMapper;
import com.shands.mod.dao.mapper.quality.QtRectificationProcessMapper;
import com.shands.mod.dao.mapper.quality.QtRectificationTaskMapper;
import com.shands.mod.dao.mapper.quality.QtRectificationUserMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.enums.quality.AgencyTypeEnum;
import com.shands.mod.dao.model.enums.quality.CheckTypeEnum;
import com.shands.mod.dao.model.enums.quality.ConfigureTypeEnum;
import com.shands.mod.dao.model.enums.quality.ExamCycleTypeEnum;
import com.shands.mod.dao.model.enums.quality.InsPersonTypeEnum;
import com.shands.mod.dao.model.enums.quality.MyTypeEnum;
import com.shands.mod.dao.model.enums.quality.StandardEnum;
import com.shands.mod.dao.model.enums.quality.TaskFinishStatusTypeEnum;
import com.shands.mod.dao.model.enums.quality.WeightTypeEnum;
import com.shands.mod.dao.model.quality.bo.AddCheckWeightsBo;
import com.shands.mod.dao.model.quality.bo.AnalyticalBo;
import com.shands.mod.dao.model.quality.bo.BlocAnalysisPlateBo;
import com.shands.mod.dao.model.quality.bo.v2.ReviewBlocReportBo;
import com.shands.mod.dao.model.quality.po.QtCheckTask;
import com.shands.mod.dao.model.quality.po.QtRectificationProcess;
import com.shands.mod.dao.model.quality.vo.AnalyticalPlateVo;
import com.shands.mod.dao.model.quality.vo.AnalyticalVo;
import com.shands.mod.dao.model.quality.vo.BlocAnalysisPlateVo;
import com.shands.mod.dao.model.quality.vo.BlocPreviewReportVo;
import com.shands.mod.dao.model.quality.vo.CheckOutVo;
import com.shands.mod.dao.model.quality.vo.DepartmentScoresVo;
import com.shands.mod.dao.model.quality.vo.DimensionScoresVo;
import com.shands.mod.dao.model.quality.vo.DimensionVo;
import com.shands.mod.dao.model.quality.vo.HotelNameVo;
import com.shands.mod.dao.model.quality.vo.HotelTaskVo;
import com.shands.mod.dao.model.quality.vo.MultiPlateVo;
import com.shands.mod.dao.model.quality.vo.QuestionVo;
import com.shands.mod.dao.model.quality.vo.RectifiedTaskVo;
import com.shands.mod.dao.model.quality.vo.RegionScoresVo;
import com.shands.mod.dao.model.quality.vo.RegionVo;
import com.shands.mod.dao.model.quality.vo.TaskPassRateVo;
import com.shands.mod.dao.model.quality.vo.TaskVo;
import com.shands.mod.dao.model.quality.vo.v2.DepartScoreVo;
import com.shands.mod.dao.model.quality.vo.v2.DimensionScoreVo;
import com.shands.mod.dao.model.quality.vo.v2.GroupScoreVo;
import com.shands.mod.dao.model.quality.vo.v2.OverallSituationVo;
import com.shands.mod.dao.model.quality.vo.v2.PatrolTopicVo;
import com.shands.mod.dao.model.quality.vo.v2.UserNameVo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.quality.service.patrolreport.PatrolReportService;
import com.shands.mod.quality.service.v2.SheetDTO;
import com.shands.mod.quality.utils.HuExcelUtils;
import com.shands.mod.util.BaseConstants;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 巡查报告
 *
 * <AUTHOR>
 * @Date 2022/3/17
 **/
@Service
@Slf4j
public class PatrolReportServiceImpl implements PatrolReportService {

  @Resource
  private QtHotelCheckMapper qtHotelCheckMapper;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Resource
  private QtRectificationUserMapper qtRectificationUserMapper;

  @Resource
  private QtRectificationProcessMapper processMapper;

  @Resource
  private QtCheckDictionaryMapper dictionaryMapper;

  /**
   * 集团分析看板列表查询
   *
   * @return
   */
  @Override
  public PageInfo<AnalyticalPlateVo> getBlocAnalyticalPlate(AnalyticalBo analyticalBo) {
    PageHelper.startPage(analyticalBo.getPageNo(), analyticalBo.getPageSize());
    List<AnalyticalPlateVo> analyticalPlateVos = new ArrayList<>();
    List<HotelNameVo> nameVos = qtHotelCheckMapper.selectHotelNameList(analyticalBo);
    List<Integer> sum = nameVos.stream().map(HotelNameVo::getHotelId).distinct()
        .collect(Collectors.toList());
    long total = new PageInfo<>(nameVos).getTotal();
    int a = 1;
    // 序号
    for (Integer element : sum) {
      List<AnalyticalVo> analyticalVos = new ArrayList<>();
      // 检查表
      for (HotelNameVo hotelNameVo : nameVos) {
        if (!element.equals(hotelNameVo.getHotelId())) {
          continue;
        }
        AnalyticalVo blocAnalyticalVo = getBlocAnalyticalVo(hotelNameVo, analyticalBo);
        if (null == blocAnalyticalVo) {
          continue;
        }
        analyticalVos.add(blocAnalyticalVo);
      }
      AnalyticalPlateVo analyticalPlateVo = new AnalyticalPlateVo();
      analyticalPlateVo.setHotelName(modHotelInfoDao.queryById(element).getHotelName());
      analyticalPlateVo.setSerialNumber(a);
      analyticalPlateVo.setAnalyticalVos(analyticalVos);
      analyticalPlateVos.add(analyticalPlateVo);
      a++;
    }
    PageInfo<AnalyticalPlateVo> analyticalPlateVoPageInfo = new PageInfo<>(analyticalPlateVos);
    analyticalPlateVoPageInfo.setTotal(total);
    return analyticalPlateVoPageInfo;
  }

  private AnalyticalVo getBlocAnalyticalVo(HotelNameVo hotelNameVo, AnalyticalBo analyticalBo) {
    AnalyticalVo analyticalVo = new AnalyticalVo();
    String time1 = analyticalBo.getAssessmentStartTime();
    String time2 = analyticalBo.getAssessmentEndTime();
    Date timeO = DateUtil.parse(time1, BaseConstants.FORMAT_TIME);
    Date timeT = DateUtil.parse(time2, BaseConstants.FORMAT_TIME);
    Date timeTh = hotelNameVo.getAssessmentStartTime();
    Date timeF = hotelNameVo.getAssessmentEndTime();
    long timeOne = timeO.getTime();
    long timeTwo = timeT.getTime();
    long timeThree = timeTh.getTime();
    long timeFour = timeF.getTime();
    long assessmentTimes = 0;
    if (ExamCycleTypeEnum.DAY.name().equals(hotelNameVo.getAssessmentType())) {
      if (timeOne <= (timeThree) && timeTwo >= (timeFour)) {
        assessmentTimes = DateUtil.betweenDay(timeTh, timeF, true) + 1;
        analyticalVo.setAssessmentStartTime(DateUtil.format(timeTh, BaseConstants.FORMAT_TIME));
        analyticalVo.setAssessmentEndTime(DateUtil.format(timeF, BaseConstants.FORMAT_TIME));
      } else {
        if (timeOne <= (timeThree) && timeThree <= (timeTwo)) {
          assessmentTimes = DateUtil.betweenDay(timeTh, timeT, true) + 1;
          analyticalVo.setAssessmentStartTime(DateUtil.format(timeTh, BaseConstants.FORMAT_TIME));
          analyticalVo.setAssessmentEndTime(time2);
        } else if (timeOne <= (timeFour) && timeTwo >= (timeFour)) {
          assessmentTimes = DateUtil.betweenDay(timeO, timeT, true) + 1;
          analyticalVo.setAssessmentStartTime(time1);
          analyticalVo.setAssessmentEndTime(DateUtil.format(timeF, BaseConstants.FORMAT_TIME));
        } else if (timeTwo <= (timeFour) && timeOne >= (timeThree)) {
          assessmentTimes = DateUtil.betweenDay(timeO, timeT, true) + 1;
          analyticalVo.setAssessmentStartTime(time1);
          analyticalVo.setAssessmentEndTime(time2);
        }
      }
      analyticalVo.setAssessmentTypeName("每天完成一次");
    } else if (ExamCycleTypeEnum.WEEK.name().equals(hotelNameVo.getAssessmentType())) {
      if (timeOne <= (timeThree) && timeTwo >= (timeFour)) {
        assessmentTimes = DateUtil.between(timeTh, timeF, DateUnit.WEEK) + 1;
        analyticalVo.setAssessmentStartTime(DateUtil.format(timeTh, BaseConstants.FORMAT_TIME));
        analyticalVo.setAssessmentEndTime(DateUtil.format(timeF, BaseConstants.FORMAT_TIME));
      } else {
        if (timeOne <= (timeThree) && timeThree <= (timeTwo)) {
          assessmentTimes = DateUtil.between(timeTh, timeT, DateUnit.WEEK) + 1;
          analyticalVo.setAssessmentStartTime(DateUtil.format(timeTh, BaseConstants.FORMAT_TIME));
          analyticalVo.setAssessmentEndTime(time2);
        } else if (timeOne <= (timeFour) && timeTwo >= (timeFour)) {
          assessmentTimes = DateUtil.between(timeO, timeT, DateUnit.WEEK) + 1;
          analyticalVo.setAssessmentStartTime(time1);
          analyticalVo.setAssessmentEndTime(DateUtil.format(timeF, BaseConstants.FORMAT_TIME));
        } else if (timeTwo <= (timeFour) && timeOne >= (timeThree)) {
          assessmentTimes = DateUtil.between(timeO, timeT, DateUnit.WEEK) + 1;
          analyticalVo.setAssessmentStartTime(time1);
          analyticalVo.setAssessmentEndTime(time2);
        }
      }
      analyticalVo.setAssessmentTypeName("每周完成一次");
    } else if (ExamCycleTypeEnum.MONTH.name().equals(hotelNameVo.getAssessmentType())) {
      if (timeOne <= (timeThree) && timeTwo >= (timeFour)) {
        assessmentTimes = DateUtil.betweenMonth(timeTh, timeF, true) + 1;
        analyticalVo.setAssessmentStartTime(DateUtil.format(timeTh, BaseConstants.FORMAT_TIME));
        analyticalVo.setAssessmentEndTime(DateUtil.format(timeF, BaseConstants.FORMAT_TIME));
      } else {
        if (timeOne <= (timeThree) && timeThree <= (timeTwo)) {
          assessmentTimes = DateUtil.betweenMonth(timeTh, timeT, true) + 1;
          analyticalVo.setAssessmentStartTime(DateUtil.format(timeTh, BaseConstants.FORMAT_TIME));
          analyticalVo.setAssessmentEndTime(time2);
        } else if (timeOne <= (timeFour) && timeTwo >= (timeFour)) {
          assessmentTimes = DateUtil.betweenMonth(timeO, timeT, true) + 1;
          analyticalVo.setAssessmentStartTime(time1);
          analyticalVo.setAssessmentEndTime(DateUtil.format(timeF, BaseConstants.FORMAT_TIME));
        } else if (timeTwo <= (timeFour) && timeOne >= (timeThree)) {
          assessmentTimes = DateUtil.betweenMonth(timeO, timeT, true) + 1;
          analyticalVo.setAssessmentStartTime(time1);
          analyticalVo.setAssessmentEndTime(time2);
        }
      }
      analyticalVo.setAssessmentTypeName("每月完成一次");
    }
    if (0 == assessmentTimes) {
      throw new ServiceException("考核周期必填或考核周期不存在");
    }
    analyticalVo.setAssessmentTimes(assessmentTimes);
    analyticalVo.setAssessmentType(hotelNameVo.getAssessmentType());
    analyticalVo.setCheckTitle(hotelNameVo.getCheckTitle());
    analyticalVo.setId(hotelNameVo.getCheckId());
    analyticalVo.setHotelId(hotelNameVo.getHotelId());
    analyticalVo.setHotelName(hotelNameVo.getHotelName());
    Long aLong = qtCheckTaskMapper.selectCompletionNumbers(hotelNameVo.getCheckId()
        , hotelNameVo.getHotelId(), analyticalVo.getAssessmentStartTime(),
        analyticalVo.getAssessmentEndTime(), CheckTypeEnum.HOTEL_SELF.name());
    analyticalVo.setCompletionNumbers(aLong);
    BigDecimal divide = BigDecimal.valueOf(analyticalVo.getCompletionNumbers())
        .divide(BigDecimal.valueOf(analyticalVo.getAssessmentTimes()), 2,
            BigDecimal.ROUND_DOWN);
    analyticalVo.setCompletionRate(divide.multiply(BigDecimal.valueOf(100)));
    if (StandardEnum.ALL.name().equals(analyticalBo.getStatus())) {
      if (divide.compareTo(BigDecimal.valueOf(1)) >= 0) {
        analyticalVo.setAssessmentResults(StandardEnum.UP_STANDARD.getDescription());
      } else {
        analyticalVo.setAssessmentResults(StandardEnum.NOT_STANDARD.getDescription());
      }
    } else if (StandardEnum.UP_STANDARD.name().equals(analyticalBo.getStatus())) {
      if (divide.compareTo(BigDecimal.valueOf(1)) >= 0) {
        analyticalVo.setAssessmentResults(StandardEnum.UP_STANDARD.getDescription());
      } else {
        return null;
      }
    } else if (StandardEnum.NOT_STANDARD.name().equals(analyticalBo.getStatus())) {
      if (divide.compareTo(BigDecimal.valueOf(1)) < 0) {
        analyticalVo.setAssessmentResults(StandardEnum.NOT_STANDARD.getDescription());
      } else {
        return null;
      }
    }
    return analyticalVo;
  }

  @Override
  public void getHotelAnalyticalExcel(HttpServletResponse response, AnalyticalBo analyticalBo) {
    List<AnalyticalVo> analyticalVos = new ArrayList<>();
    List<HotelNameVo> nameVos = qtHotelCheckMapper.selectHotelNameList(analyticalBo);
    // 检查表
    for (HotelNameVo hotelNameVo : nameVos) {
      AnalyticalVo blocAnalyticalVo = getBlocAnalyticalVo(hotelNameVo, analyticalBo);
      analyticalVos.add(blocAnalyticalVo);
    }
    ExcelWriter writer = ExcelUtil.getWriter();
    Sheet sheet = writer.getSheet();
    sheet.setDefaultColumnWidth(15);
    sheet.setDefaultRowHeightInPoints(20);
    List<List<String>> lists = new ArrayList<>();
    String format = DateUtil.format(new Date(), BaseConstants.FORMAT_TIME);
    List<String> row1 = CollUtil.newArrayList("导出时间：" + format + "     酒店：开元集团-质检架构");
    List<String> row2 = CollUtil.newArrayList("酒店考核-考核统计");
    List<String> row3 = CollUtil.newArrayList(
        "检查表：全部   考核时间：" + analyticalBo.getAssessmentStartTime() + "~" + analyticalBo
            .getAssessmentEndTime());
    List<String> row4 = CollUtil.newArrayList("");
    List<String> row5 = CollUtil.newArrayList("考核结果统计");
    List<String> row6 = null;
    if (ConfigureTypeEnum.HOTEL.name().equals(analyticalBo.getConfigType())) {
      row6 = CollUtil.newArrayList("检查表", "考核方式", "考核日/周/月", "完成日/周/月", "完成率", "考核结果");
    }
    if (ConfigureTypeEnum.BLOC.name().equals(analyticalBo.getConfigType())) {
      row6 = CollUtil.newArrayList("酒店名称", "检查表", "考核方式", "考核日/周/月", "完成日/周/月", "完成率", "考核结果");
    }
    lists.add(row1);
    lists.add(row2);
    lists.add(row3);
    lists.add(row4);
    lists.add(row5);
    lists.add(row6);
    CellRangeAddress region = new CellRangeAddress(1, 1, 0, 11);
    sheet.addMergedRegion(region);
    CellRangeAddress region1 = new CellRangeAddress(3, 3, 0, 11);
    sheet.addMergedRegion(region1);
    CellRangeAddress region2 = new CellRangeAddress(2, 2, 0, 11);
    sheet.addMergedRegion(region2);
    for (AnalyticalVo analyticalVo : analyticalVos) {
      if (ConfigureTypeEnum.HOTEL.name().equals(analyticalBo.getConfigType())) {
        List<String> row = CollUtil.newArrayList(
            analyticalVo.getCheckTitle(), analyticalVo.getAssessmentTypeName(),
            String.valueOf(analyticalVo.getAssessmentTimes())
            , String.valueOf(analyticalVo.getCompletionNumbers()),
            String.valueOf(analyticalVo.getCompletionRate()), analyticalVo.getAssessmentResults());
        lists.add(row);
      }
      if (ConfigureTypeEnum.BLOC.name().equals(analyticalBo.getConfigType())) {
        List<String> row = CollUtil.newArrayList(
            analyticalVo.getHotelName(), analyticalVo.getCheckTitle(),
            analyticalVo.getAssessmentTypeName(),
            String.valueOf(analyticalVo.getAssessmentTimes())
            , String.valueOf(analyticalVo.getCompletionNumbers()),
            String.valueOf(analyticalVo.getCompletionRate()), analyticalVo.getAssessmentResults());
        lists.add(row);
      }
    }
    writer.write(lists);
    try {
      excelExport(response, writer, "hotel_check");
    } catch (IOException e) {
      log.error(e.getMessage(), e);
    }
  }

  /**
   * excel导出
   *
   * @param response 响应
   * @param writer   ExcelWriter文件流
   * @param filename 文件名
   * @throws IOException ioexception
   */
  private void excelExport(HttpServletResponse response, ExcelWriter writer, String filename)
      throws IOException {

    response.setContentType(
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
    response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,X-Filename");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + URLEncoder.encode(filename, "UTF-8") + ".xlsx");
    ServletOutputStream out = response.getOutputStream();
    writer.flush(out, true);
    writer.close();
    IoUtil.close(out);
  }

  @Resource
  private QtCheckDetailsMapper qtCheckDetailsMapper;

  @Resource
  private QtCheckAnswerMapper qtCheckAnswerMapper;

  @Resource
  private QtCheckTaskMapper qtCheckTaskMapper;

  @Override
  public BlocPreviewReportVo searchBlocPreviewReport(Integer taskId) {
    try {
      BlocPreviewReportVo previewReportVo = new BlocPreviewReportVo();
      TaskVo taskVo = qtCheckTaskMapper.selectPreviewReport(taskId);
      List<String> falt = new ArrayList<>();
      if (taskVo == null || !CheckTypeEnum.BLOC_INSPECTION.name().equals(taskVo.getCheckType())) {
        throw new ServiceException("任务id不正确");
      }
      previewReportVo.setTaskVo(taskVo);
      //各维度得分概览
      DimensionVo dimensionVo = new DimensionVo();
      List<DimensionScoresVo> dimensionScoresVoList = new ArrayList<>();
      //获取有哪些维度
      List<AddCheckWeightsBo> qtCheckAnswerList = qtCheckAnswerMapper.selectdictDimensionList(
          taskId,
          taskVo.getHotelId());
      //获取所有的问题
      List<QuestionVo> questionVoList = qtCheckDetailsMapper.selectWrong(taskId,
          taskVo.getHotelId());
      //维度筛选百分比
      List<AddCheckWeightsBo> percentage = qtCheckAnswerList.stream()
          .filter(weight -> WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(weight.getWeightsType()))
          .collect(
              Collectors.toList());
      percentage.stream().forEach(weightsBo -> {
        DimensionScoresVo dimensionScoresVos = qtCheckDetailsMapper.getDimensionPercentage(taskId,
            taskVo.getHotelId(), weightsBo.getDimensionTypeId());
        if (dimensionScoresVos != null) {
          dimensionScoresVoList.add(dimensionScoresVos);
          if (Boolean.TRUE.equals(dimensionScoresVos.getIfCore())) {
            List<QuestionVo> questionVo = questionVoList.stream()
                .filter(item -> weightsBo.getDimensionTypeId().equals(item.getDimensionId()))
                .collect(
                    Collectors.toList());
            if (!CollectionUtils.isEmpty(questionVo)) {
              questionVo.stream().forEach(questionVo1 -> {
                //获取整改信息
                String type = null;
                if (AgencyTypeEnum.COMPLETED.name().equals(questionVo1.getStatus())) {
                  type = MyTypeEnum.FINISH.name();
                }
                QtRectificationProcess processes = processMapper.selectByRectificationId(
                    questionVo1.getId(), type);
                questionVo1.setRectificationRemark(processes.getDisposeContent());
                questionVo1.setRectificationUrl(processes.getUrl());
                questionVo1.setRectificationType(processes.getResourceType());

                List<UserNameVo> user = qtRectificationUserMapper.getRectificationUser(
                    questionVo1.getId());
                if (!CollectionUtils.isEmpty(user)) {
                  user.stream().forEach(user1 -> {
                    if (InsPersonTypeEnum.RECTIFIER_PERSON.name().equals(user1.getType())) {
                      questionVo1.setPersonName(user1.getName());
                    }
                    if (InsPersonTypeEnum.REVIEWER_PERSON.name().equals(user1.getType())) {
                      questionVo1.setReviewName(user1.getName());
                    }
                  });
                }
              });
              //分组
              /*Map<String, List<QuestionVo>> listMap =
                  questionVo.stream().collect(Collectors.groupingBy(QuestionVo::getProductName));*/
              dimensionScoresVos.setDimensionQuestionList(questionVo);
            }
          }
        }
      });
      //触及直接0分和按个数扣分的项目
      List<AddCheckWeightsBo> notPercentage = qtCheckAnswerList.stream()
          .filter(
              weight -> !WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(weight.getWeightsType()))
          .collect(
              Collectors.toList());
      notPercentage.stream().forEach(weightsBo -> {
        DimensionScoresVo dimensionScoresVos = qtCheckDetailsMapper.getDimensionNotPercentage(
            taskId,
            taskVo.getHotelId(), weightsBo.getDimensionTypeId());
        if (dimensionScoresVos != null) {
          dimensionScoresVoList.add(dimensionScoresVos);
          if (Boolean.TRUE.equals(dimensionScoresVos.getIfCore())) {
            List<QuestionVo> questionVo = questionVoList.stream()
                .filter(item -> weightsBo.getDimensionTypeId().equals(item.getDimensionId()))
                .collect(
                    Collectors.toList());
            if (!CollectionUtils.isEmpty(questionVo)) {
              questionVo.stream().forEach(questionVo1 -> {
                if (questionVo1.getId() != null) {
                  //获取整改信息
                  String type = null;
                  if (AgencyTypeEnum.COMPLETED.name().equals(questionVo1.getStatus())) {
                    type = MyTypeEnum.FINISH.name();
                  }
                  QtRectificationProcess processes = processMapper.selectByRectificationId(
                      questionVo1.getId(), type);
                  questionVo1.setRectificationRemark(processes.getDisposeContent());
                  questionVo1.setRectificationUrl(processes.getUrl());
                  questionVo1.setRectificationType(processes.getResourceType());
                  List<UserNameVo> user = qtRectificationUserMapper.getRectificationUser(
                      questionVo1.getId());
                  if (!CollectionUtils.isEmpty(user)) {
                    user.stream().forEach(user1 -> {
                      if (InsPersonTypeEnum.RECTIFIER_PERSON.name().equals(user1.getType())) {
                        questionVo1.setPersonName(user1.getName());
                      }
                      if (InsPersonTypeEnum.REVIEWER_PERSON.name().equals(user1.getType())) {
                        questionVo1.setReviewName(user1.getName());
                      }
                    });
                  }

                }

              });
              //分组
              /*Map<String, List<QuestionVo>> listMap =
                  questionVo.stream().collect(Collectors.groupingBy(QuestionVo::getProductName));*/
              dimensionScoresVos.setDimensionQuestionList(questionVo);
            }
          }
          if (WeightTypeEnum.WEIGHT_TOUCH.name().equals(dimensionScoresVos.getWeightsType())
              && dimensionScoresVos.getScore().compareTo(BigDecimal.ZERO) == 1) {
            falt.add("安全红线");
          }
        }
      });
      //总分合格率
      dimensionVo.setDimensionScoresVos(dimensionScoresVoList);
      //计算合格率
      BigDecimal passRate = qtCheckDetailsMapper.calculatePassRate(taskId, taskVo.getHotelId());
      dimensionVo.setPassRate(passRate == null ? new BigDecimal(0) : passRate);
      //判断是否有触及红线的
      if (!CollectionUtils.isEmpty(falt)) {
        dimensionVo.setTotalScore(new BigDecimal(0));
      } else {
        BigDecimal sum = BigDecimal.ZERO;
        for (DimensionScoresVo scoresVo : dimensionScoresVoList) {
          if (WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(scoresVo.getWeightsType())) {
            sum = sum.add(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
          }
          if (WeightTypeEnum.WEIGHT_NUMBER.name().equals(scoresVo.getWeightsType())) {
            sum = sum.subtract(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
          }
        }
        dimensionVo.setTotalScore(sum.setScale(1, BigDecimal.ROUND_HALF_UP));
      }
      previewReportVo.setDimensionScores(dimensionVo);

      //获取各个部门
      List<Integer> partIds = qtCheckAnswerMapper.selectAllDeptName(taskId, taskVo.getHotelId());
      List<DepartmentScoresVo> departmentScoresVoList = new ArrayList<>();
      partIds.stream().forEach(id -> {
        DepartmentScoresVo departmentScoresVo = qtCheckDetailsMapper.calculateDeptScore(taskId,
            taskVo.getHotelId(), id);
        if (departmentScoresVo != null) {
          //获取部门中有问题的数据
          List<QuestionVo> questionVo = questionVoList.stream()
              .filter(item -> id.equals(item.getDeptId()))
              .collect(
                  Collectors.toList());
          if (!CollectionUtils.isEmpty(questionVo)) {
            questionVo.stream().forEach(questionVo1 -> {
              if (questionVo1.getId() != null) {
                //获取整改信息
                String type = null;
                if (AgencyTypeEnum.COMPLETED.name().equals(questionVo1.getStatus())) {
                  type = MyTypeEnum.FINISH.name();
                }
                QtRectificationProcess processes = processMapper.selectByRectificationId(
                    questionVo1.getId(), type);
                questionVo1.setRectificationRemark(processes.getDisposeContent());
                questionVo1.setRectificationUrl(processes.getUrl());
                questionVo1.setRectificationType(processes.getResourceType());
                List<UserNameVo> user = qtRectificationUserMapper.getRectificationUser(
                    questionVo1.getId());
                if (!CollectionUtils.isEmpty(user)) {
                  user.stream().forEach(user1 -> {
                    if (InsPersonTypeEnum.RECTIFIER_PERSON.name().equals(user1.getType())) {
                      questionVo1.setPersonName(user1.getName());
                    }
                    if (InsPersonTypeEnum.REVIEWER_PERSON.name().equals(user1.getType())) {
                      questionVo1.setReviewName(user1.getName());
                    }
                  });
                }
              }
            });
            //分组
           /* Map<String, List<QuestionVo>> listMap =
                questionVo.stream().collect(Collectors.groupingBy(QuestionVo::getProductName));*/
            departmentScoresVo.setDepatQuestionList(questionVo);
          }
          departmentScoresVoList.add(departmentScoresVo);
        }

      });
      previewReportVo.setDepartmentScores(departmentScoresVoList);
      //各个区域的得分
      List<RegionVo> allArea = qtCheckDetailsMapper.selectAllArea(taskId, taskVo.getHotelId());
      List<RegionVo> regionVos = new ArrayList<>();
      allArea.stream().forEach(area -> {
        //各个区域的分类得分
        RegionVo regionVo = new RegionVo();
        regionVo.setAreaName(area.getAreaName());
        regionVo.setAreaId(area.getAreaId());
        List<RegionScoresVo> scoresVoList = new ArrayList<>();
        List<Integer> classIds = qtCheckDetailsMapper.selectClassification(taskId,
            taskVo.getHotelId(), area.getAreaId());
        classIds.stream().forEach(classId -> {
          RegionScoresVo scoresVo = qtCheckDetailsMapper.selectClassificationScore(taskId,
              taskVo.getHotelId(), area.getAreaId(), classId);
          if (scoresVo != null) {
            scoresVoList.add(scoresVo);
          }

        });
        regionVo.setRegionScoresVos(scoresVoList);
        regionVos.add(regionVo);

      });
      previewReportVo.setRegionScores(regionVos);
      List<String> summaryList = qtCheckDetailsMapper.selectSummary(taskId, taskVo.getHotelId());
      if (CollectionUtils.isEmpty(summaryList)) {
        previewReportVo.setSummary("");
      } else {
        StringBuilder stringBuilder = new StringBuilder();
        summaryList.stream().forEach(s -> {
          stringBuilder.append(s).append("。");
        });
        previewReportVo.setSummary(stringBuilder.toString());
      }
      return previewReportVo;
    } catch (ServiceException e) {
      throw new ServiceException(e.getErrorMessage());
    } catch (Exception e) {
      throw new ServiceException("数据异常");
    }
  }

  @Override
  public void reviseBlocPreviewReport(ReviewBlocReportBo reportBo) {
    QtCheckTask task = qtCheckTaskMapper.queryById(reportBo.getTaskId());
    task.setSuggest(reportBo.getSuggest());
    if (qtCheckTaskMapper.update(task) <= 0) {
      throw new ServiceException("更新集团报告错误");
    }
  }

  @Override
  public BigDecimal calculatePassRate(Integer taskId, Integer hotelId) {
    BigDecimal passRate = qtCheckDetailsMapper.calculatePassRate(taskId, hotelId);
    return passRate == null ? new BigDecimal(0) : passRate;
  }

  @Override
  public Map<Integer, BigDecimal> calculatePassRateMap(List<Integer> taskIdList) {
    List<TaskPassRateVo> taskPassRateVos = qtCheckDetailsMapper.calculatePassRateList(taskIdList);
    if (CollectionUtils.isEmpty(taskPassRateVos)) {
      return Collections.emptyMap();
    }
    return taskPassRateVos.stream().collect(Collectors.toMap(item -> item.getTaskId(), item -> item.getPassRate(),
        (oldVal, newVal) -> oldVal));
  }

  /*
   * <AUTHOR>
   * @Description //TODO 获取task总分
   * @date 2022/4/15 16:53
   * @Param
   * @return
   **/

  /**
   * 1. 任务id获取预览报告表数据
   * 2. 任务id和酒店id获取维度列表
   * 3. 百分比维度记录保留
   * 4. 每个百分比维度记录查找对应的维度得分。
   * 5. 非百分比项目检查是否触及红线，触及红线直接返回0分
   * 6. 根据各个维度得分情况累加求和：百分比类型累加求和,非百分比维度不合格项目分数扣减。最终得分根据乘上对应维度的权重值
   */
  @Override
  public BigDecimal getTotalScore(Integer taskId) {

    TaskVo taskVo = qtCheckTaskMapper.selectPreviewReport(taskId);
    List<String> falt = new ArrayList<>();
    if (taskVo == null || !CheckTypeEnum.BLOC_INSPECTION.name().equals(taskVo.getCheckType())) {
      throw new ServiceException("任务id不正确");
    }
    //各维度得分概览
    DimensionVo dimensionVo = new DimensionVo();
    List<DimensionScoresVo> dimensionScoresVoList = new ArrayList<>();
    //获取有哪些维度
    List<AddCheckWeightsBo> qtCheckAnswerList = qtCheckAnswerMapper.selectdictDimensionList(
        taskId,
        taskVo.getHotelId());
    //维度筛选百分比
    List<AddCheckWeightsBo> percentage = qtCheckAnswerList.stream()
        .filter(weight -> WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(weight.getWeightsType()))
        .collect(
            Collectors.toList());
    percentage.stream().forEach(weightsBo -> {
      DimensionScoresVo dimensionScoresVos = qtCheckDetailsMapper.getDimensionPercentage(taskId,
          taskVo.getHotelId(), weightsBo.getDimensionTypeId());
      if (dimensionScoresVos != null) {
        dimensionScoresVoList.add(dimensionScoresVos);
      }
    });
    // 查出所有taskId和hotelId
    // 查出[taskIdList],[hotelIdList],[typeIdList],
    //触及直接0分和按个数扣分的项目
    List<AddCheckWeightsBo> notPercentage = qtCheckAnswerList.stream()
        .filter(
            weight -> !WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(weight.getWeightsType()))
        .collect(
            Collectors.toList());
    notPercentage.stream().forEach(weightsBo -> {
      DimensionScoresVo dimensionScoresVos = qtCheckDetailsMapper.getDimensionNotPercentage(
          taskId,
          taskVo.getHotelId(), weightsBo.getDimensionTypeId());
      if (dimensionScoresVos != null) {
        dimensionScoresVoList.add(dimensionScoresVos);
        if (WeightTypeEnum.WEIGHT_TOUCH.name().equals(dimensionScoresVos.getWeightsType())
            && dimensionScoresVos.getScore().compareTo(BigDecimal.ZERO) == 1) {
          falt.add("安全红线");
        }
      }
    });
    //总分合格率
    dimensionVo.setDimensionScoresVos(dimensionScoresVoList);
    //判断是否有触及红线的
    if (!CollectionUtils.isEmpty(falt)) {
      return new BigDecimal(0);
    } else {
      BigDecimal sum = BigDecimal.ZERO;
      for (DimensionScoresVo scoresVo : dimensionScoresVoList) {
        if (WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(scoresVo.getWeightsType())) {
          sum = sum.add(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
        }
        if (WeightTypeEnum.WEIGHT_NUMBER.name().equals(scoresVo.getWeightsType())) {
          sum = sum.subtract(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
        }
      }
      return (sum.setScale(1, BigDecimal.ROUND_HALF_UP));
    }
  }

  /**
   * 原有每个taskId查询时计算改为查询taskId对应的所有记录，然后再进行对应的分组计算
   * @param taskIdList
   * @return
   */
  @Override
  public Map<Integer, BigDecimal> getTotalScoreMap(List<Integer> taskIdList) {
    Map<Integer, BigDecimal> result = new HashMap<>();
    List<TaskVo> taskVoList = qtCheckTaskMapper.selectPreviewReportList(taskIdList);

    //获取有哪些维度
    List<Integer> hotelIdList = taskVoList.stream().map(item -> item.getHotelId()).collect(Collectors.toList());
    List<AddCheckWeightsBo> qtCheckAnswerList = qtCheckAnswerMapper.selectdictDimensionListWithList(taskIdList, hotelIdList);
    List<Integer> dimensionTypeIdList = qtCheckAnswerList.stream().map(item -> item.getDimensionTypeId())
        .collect(Collectors.toList());
    //
    List<DimensionScoresVo> dimensionPercentageList = qtCheckDetailsMapper.getDimensionPercentageList(
        taskIdList,
        hotelIdList, dimensionTypeIdList);
    // 根据任务ID、酒店id、dimensionId计算分别计算得分
    Map<String, List<DimensionScoresVo>> groupScoreList = dimensionPercentageList.stream()
        .collect(Collectors.groupingBy(
            item -> item.getTaskId() + "-" + item.getHotelId() + "-" + item.getDimensionId()));
    Map<Integer, BigDecimal> res = new HashMap<>();
    List<DimensionScoresVo> resultDimensionScoreVoList = new ArrayList<>();
    for (Map.Entry<String, List<DimensionScoresVo>> entry : groupScoreList.entrySet()) {
      List<DimensionScoresVo> list = entry.getValue();
      String key = entry.getKey();
      String[] split = key.split("-");
      Integer taskId = Integer.valueOf(split[0]);
      DimensionScoresVo dimensionScoresVo = list.get(0);
      if (!CollectionUtils.isEmpty(list)) {
        int total = list.size();
        // 百分比维度的计算得分
        long percentCount = list.stream()
            .filter(item ->
              WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(item.getWeightsType())
                  && (("CHOOSE".equals(item.getTopicType()) && "QUALIFIED".equals(item.getAnswer())) ||
                      ("GRADE".equals(item.getTopicType()) && "5".equals(item.getAnswer()))
                    )
            )
            .count();
        DimensionScoresVo percentDimensionScoresVo = list.stream()
            .filter(item ->
                WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(item.getWeightsType())).findFirst()
            .orElse(null);
        float percentScore = Math.round(1f * percentCount / total * 100);
        if (percentDimensionScoresVo != null) {
          percentDimensionScoresVo.setScore(new BigDecimal(percentScore));
          resultDimensionScoreVoList.add(percentDimensionScoresVo);
        }
        // 非百分比维度的计算得分
        long notPercentCount = list.stream()
            .filter((item -> (!WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(item.getWeightsType()))
            &&  (("CHOOSE".equals(item.getTopicType()) && "NOT_QUALIFIED".equals(item.getAnswer())) ||
                ("GRADE".equals(item.getTopicType()) && "5".equals(item.getAnswer())))
            ))
            .count();
        float noPercentScore = Math.round(1f * notPercentCount / total * 100);
        DimensionScoresVo noPercentDimensionDimensionScoresVo = list.stream()
            .filter(item -> !WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(item.getWeightsType()))
            .findFirst().orElse(null);
        if (noPercentDimensionDimensionScoresVo != null) {
          noPercentDimensionDimensionScoresVo.setScore(new BigDecimal(noPercentScore));
          resultDimensionScoreVoList.add(noPercentDimensionDimensionScoresVo);
        }
        // 非百分比得分计算为0，直接返回0
        if (Float.compare(noPercentScore, 0) > 0) {
          res.put(taskId, BigDecimal.ZERO);
        }
      }
    }
    // 计算各个taskId的最终得分
    Map<Integer, List<DimensionScoresVo>> taskIdGroupingDimensionScoreList = resultDimensionScoreVoList.stream()
        .collect(Collectors.groupingBy(item -> item.getTaskId()));
    for (Map.Entry<Integer, List<DimensionScoresVo>> entry : taskIdGroupingDimensionScoreList.entrySet()) {
      List<DimensionScoresVo> list = entry.getValue();
      Integer taskId = entry.getKey();
      BigDecimal sum = BigDecimal.ZERO;
      for (DimensionScoresVo scoresVo : list) {
        if (WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(scoresVo.getWeightsType())) {
          sum = sum.add(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
        }
        if (WeightTypeEnum.WEIGHT_NUMBER.name().equals(scoresVo.getWeightsType())) {
          sum = sum.subtract(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
        }
      }
      result.put(taskId, (sum.setScale(1, BigDecimal.ROUND_HALF_UP)));
    }
    return result;
  }

  @Override
  public void exportBlocPreviewReport(HttpServletResponse response, Integer checkId,String startTime,String endTime) {
    List<QtCheckTask> qtCheckTasks = qtCheckTaskMapper.queryListByCheckId(checkId,startTime,endTime);

    List<BlocPreviewReportVo> blocPreviewReportVoList = new ArrayList<>(qtCheckTasks.size());
    qtCheckTasks.stream().forEach(task -> {
      BlocPreviewReportVo blocPreviewReportVo = searchBlocPreviewReportOptimize(task.getId());
      List<RegionScoresVo> regionScoresVoList = new ArrayList<>();
      blocPreviewReportVo.getRegionScores().forEach(area->{
        regionScoresVoList.addAll(area.getRegionScoresVos());
      });
      blocPreviewReportVo.setGroupScores(regionScoresVoList);
      blocPreviewReportVoList.add(blocPreviewReportVo);
    });

    List<OverallSituationVo> situationVos = new ArrayList<>(qtCheckTasks.size());
    List<DepartScoreVo> departScoreVoList = new ArrayList<>(qtCheckTasks.size());
    List<GroupScoreVo> groupScoreVos = new ArrayList<>(qtCheckTasks.size());

    List<DimensionScoreVo> dimensionScoreVoList = new ArrayList< >(qtCheckTasks.size());
    AtomicInteger atomicInteger = new AtomicInteger(1);
    blocPreviewReportVoList.stream().forEach(report -> {
      String owner = dictionaryMapper.getDictionaryName(report.getTaskVo().getOwnershipName());
      String coopera = dictionaryMapper.getDictionaryName(report.getTaskVo().getCooperationName());
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      String startDate = sdf.format(report.getTaskVo().getTaskStartTime()) + "-" + sdf.format(
          report.getTaskVo().getTaskEndTime());
      OverallSituationVo overallSituationVo = OverallSituationVo.builder()
          .id(atomicInteger.toString())
          .checkPeople(report.getTaskVo().getCheckPeople())
          .hotelCode(report.getTaskVo().getHotelCode())
          .passRate(report.getDimensionScores().getPassRate())
          .totalScore(report.getDimensionScores().getTotalScore())
          .hotelName(report.getTaskVo().getHotelName())
          .taskStartTime(startDate)
          .ownershipName(owner)
          .cooperationName(coopera)
          .build();
      situationVos.add(overallSituationVo);
      //部门
      List<DepartmentScoresVo> departmentScores = report.getDepartmentScores();
      List<DepartmentScoresVo> deptScore = departmentScores.stream()
          .filter(depar -> depar.getDeptName().contains("安全")).collect(Collectors.toList());
      List<DepartmentScoresVo> kitchen = departmentScores.stream()
          .filter(depar -> depar.getDeptName().contains("厨房")).collect(Collectors.toList());
      List<DepartmentScoresVo> engineering = departmentScores.stream()
          .filter(depar -> depar.getDeptName().contains("工程")).collect(Collectors.toList());
      List<DepartmentScoresVo> housekeeper = departmentScores.stream()
          .filter(depar -> depar.getDeptName().contains("管家")).collect(Collectors.toList());
      List<DepartmentScoresVo> recreation = departmentScores.stream()
          .filter(depar -> depar.getDeptName().contains("康乐")).collect(Collectors.toList());
      List<DepartmentScoresVo> resources = departmentScores.stream()
          .filter(depar -> depar.getDeptName().contains("人资")).collect(Collectors.toList());
      DepartScoreVo departScoreVo = DepartScoreVo.builder()
          .id(atomicInteger.toString())
          .hotelCode(report.getTaskVo().getHotelCode())
          .hotelName(report.getTaskVo().getHotelName())
          .taskStartTime(startDate)
          .ownershipName(owner)
          .cooperationName(coopera)
          .security(CollectionUtils.isEmpty(deptScore) ? "-" : deptScore.get(0).getDeptScore())
          .kitchen(CollectionUtils.isEmpty(kitchen) ? "-" : kitchen.get(0).getDeptScore())
          .engineering(
              CollectionUtils.isEmpty(engineering) ? "-" : engineering.get(0).getDeptScore())
          .housekeeper(
              CollectionUtils.isEmpty(housekeeper) ? "-" : housekeeper.get(0).getDeptScore())
          .recreation(CollectionUtils.isEmpty(recreation) ? "-" : recreation.get(0).getDeptScore())
          .resources(CollectionUtils.isEmpty(resources) ? "-" : resources.get(0).getDeptScore())
          .build();
      departScoreVoList.add(departScoreVo);
      //维度
      List<DimensionScoresVo> dimensionScoresVos = report.getDimensionScores()
          .getDimensionScoresVos();
      List<DimensionScoresVo> safety = dimensionScoresVos.stream()
          .filter(depar -> depar.getDictDimensionName().contains("运营安全"))
          .collect(Collectors.toList());
      List<DimensionScoresVo> publicSecurity = dimensionScoresVos.stream()
          .filter(depar -> depar.getDictDimensionName().contains("治安管理"))
          .collect(Collectors.toList());
      List<DimensionScoresVo> fireSafety = dimensionScoresVos.stream()
          .filter(depar -> depar.getDictDimensionName().contains("消防生命安全"))
          .collect(Collectors.toList());
      List<DimensionScoresVo> health = dimensionScoresVos.stream()
          .filter(depar -> depar.getDictDimensionName().contains("职业安全和健康"))
          .collect(Collectors.toList());
      List<DimensionScoresVo> training = dimensionScoresVos.stream()
          .filter(depar -> depar.getDictDimensionName().contains("酒店安全培训"))
          .collect(Collectors.toList());
      List<DimensionScoresVo> red = dimensionScoresVos.stream()
          .filter(depar -> depar.getDictDimensionName().contains("安全红线"))
          .collect(Collectors.toList());
      DimensionScoreVo dimensionScoreVo = DimensionScoreVo.builder()
          .id(atomicInteger.toString())
          .hotelCode(report.getTaskVo().getHotelCode())
          .hotelName(report.getTaskVo().getHotelName())
          .taskStartTime(startDate)
          .ownershipName(owner)
          .cooperationName(coopera)
          .safety(CollectionUtils.isEmpty(safety) ? "-" : safety.get(0).getScore().toString())
          .publicSecurity(CollectionUtils.isEmpty(publicSecurity) ? "-"
              : publicSecurity.get(0).getScore().toString())
          .fireSafety(
              CollectionUtils.isEmpty(fireSafety) ? "-" : fireSafety.get(0).getScore().toString())
          .health(CollectionUtils.isEmpty(health) ? "-" : health.get(0).getScore().toString())
          .training(CollectionUtils.isEmpty(training) ? "-" : training.get(0).getScore().toString())
          .red(CollectionUtils.isEmpty(red) ? "-" : red.get(0).getScore().toString())
          .build();
      //分类
      
      List<RegionScoresVo> grouVo = report.getGroupScores();

      List<RegionScoresVo> g = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("各个子系统"))
          .collect(Collectors.toList());
      List<RegionScoresVo> h = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("消防应急物资配置标准"))
          .collect(Collectors.toList());
      List<RegionScoresVo> i = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("防暴恐物资配置标准"))
          .collect(Collectors.toList());
      List<RegionScoresVo> j = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("钥匙管理"))
          .collect(Collectors.toList());
      List<RegionScoresVo> k = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("消防设备设施"))
          .collect(Collectors.toList());
      List<RegionScoresVo> l = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("环境安全"))
          .collect(Collectors.toList());
      List<RegionScoresVo> m = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("突发事件和应急物资"))
          .collect(Collectors.toList());
      List<RegionScoresVo> n = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("安全培训"))
          .collect(Collectors.toList());
      List<RegionScoresVo> o = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("电瓶车安全管理"))
          .collect(Collectors.toList());
      List<RegionScoresVo> p = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("消防相关证照及报告"))
          .collect(Collectors.toList());
      List<RegionScoresVo> q = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("安全管理委员会"))
          .collect(Collectors.toList());
      List<RegionScoresVo> r = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("管理台账及报告"))
          .collect(Collectors.toList());
      List<RegionScoresVo> s = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("外来人员和物资出门"))
          .collect(Collectors.toList());
      List<RegionScoresVo> t = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("重大事件报告"))
          .collect(Collectors.toList());
      List<RegionScoresVo> u = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("歇业检查"))
          .collect(Collectors.toList());
      List<RegionScoresVo> v = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("厨房安全检查"))
          .collect(Collectors.toList());
      List<RegionScoresVo> w = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("工程设备区域"))
          .collect(Collectors.toList());
      List<RegionScoresVo> x = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("动火作业审批许可"))
          .collect(Collectors.toList());
      List<RegionScoresVo> y = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("康乐安全检查"))
          .collect(Collectors.toList());
      List<RegionScoresVo> z = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("室内外无动力设备设施"))
          .collect(Collectors.toList());
      List<RegionScoresVo> aa = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("环境安全（康乐区域）"))
          .collect(Collectors.toList());
      List<RegionScoresVo> ab = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("环境安全（客房区域）"))
          .collect(Collectors.toList());
      List<RegionScoresVo> ac = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("残疾人设施"))
          .collect(Collectors.toList());
      List<RegionScoresVo> ad = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("客房设施安全检查"))
          .collect(Collectors.toList());
      List<RegionScoresVo> ae = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("防疫物资"))
          .collect(Collectors.toList());
      List<RegionScoresVo> af = grouVo.stream()
          .filter(depar -> depar.getClassificationName().contains("虫害消杀"))
          .collect(Collectors.toList());
      GroupScoreVo areaScoreVo = GroupScoreVo.builder()
          .id(atomicInteger.toString())
          .hotelCode(report.getTaskVo().getHotelCode())
          .hotelName(report.getTaskVo().getHotelName())
          .taskStartTime(startDate)
          .ownershipName(owner)
          .cooperationName(coopera)
          .g(CollectionUtils.isEmpty(g) ? "-" : g.get(0).getClassScore())
          .h(CollectionUtils.isEmpty(h) ? "-" : h.get(0).getClassScore())
          .i(CollectionUtils.isEmpty(i) ? "-" : i.get(0).getClassScore())
          .j(CollectionUtils.isEmpty(j) ? "-" : j.get(0).getClassScore())
          .k(CollectionUtils.isEmpty(k) ? "-" : k.get(0).getClassScore())
          .l(CollectionUtils.isEmpty(l) ? "-" : l.get(0).getClassScore())
          .m(CollectionUtils.isEmpty(m) ? "-" : m.get(0).getClassScore())
          .n(CollectionUtils.isEmpty(n) ? "-" : n.get(0).getClassScore())
          .o(CollectionUtils.isEmpty(o) ? "-" : o.get(0).getClassScore())
          .p(CollectionUtils.isEmpty(p) ? "-" : p.get(0).getClassScore())
          .q(CollectionUtils.isEmpty(q) ? "-" : q.get(0).getClassScore())
          .r(CollectionUtils.isEmpty(r) ? "-" : r.get(0).getClassScore())
          .s(CollectionUtils.isEmpty(s) ? "-" : s.get(0).getClassScore())
          .t(CollectionUtils.isEmpty(t) ? "-" : t.get(0).getClassScore())
          .u(CollectionUtils.isEmpty(u) ? "-" : u.get(0).getClassScore())
          .v(CollectionUtils.isEmpty(v) ? "-" : v.get(0).getClassScore())
          .w(CollectionUtils.isEmpty(w) ? "-" : w.get(0).getClassScore())
          .x(CollectionUtils.isEmpty(x) ? "-" : x.get(0).getClassScore())
          .y(CollectionUtils.isEmpty(y) ? "-" : y.get(0).getClassScore())
          .z(CollectionUtils.isEmpty(z) ? "-" : z.get(0).getClassScore())
          .aa(CollectionUtils.isEmpty(aa) ? "-" : aa.get(0).getClassScore())
          .ab(CollectionUtils.isEmpty(ab) ? "-" : ab.get(0).getClassScore())
          .ac(CollectionUtils.isEmpty(ac) ? "-" : ac.get(0).getClassScore())
          .ad(CollectionUtils.isEmpty(ad) ? "-" : ad.get(0).getClassScore())
          .ae(CollectionUtils.isEmpty(ae) ? "-" : ae.get(0).getClassScore())
          .af(CollectionUtils.isEmpty(af) ? "-" : af.get(0).getClassScore())
          .build();

      groupScoreVos.add(areaScoreVo);
      dimensionScoreVoList.add(dimensionScoreVo);
      atomicInteger.incrementAndGet();

    });

    List<PatrolTopicVo> patrolTopicVo = qtCheckDetailsMapper.selecteachTopic(checkId);
    AtomicInteger index = new AtomicInteger();
    List<SheetDTO> sheetList = new ArrayList<>(5);
    Integer size = 5;
    for (int a = 0; a < size; a++) {
      if (a == 0) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        // 设置只导出有别名的字段
        map.put("id", "序号");
        map.put("hotelCode", "酒店编码");
        map.put("hotelName", "酒店名称");
        map.put("cooperationName", "归属事业部");
        map.put("ownershipName", "管理方式");
        map.put("taskStartTime", "任务时间");
        map.put("checkPeople", "巡检员");
        map.put("passRate", "检查合格率");
        map.put("totalScore", "报告得分");

        SheetDTO sheet = new SheetDTO();
        sheet.setFieldAndAlias(map);
        sheet.setSheetName(index.toString());
        sheet.setCollection(situationVos);
        sheet.setSheetName("整体情况");
        sheetList.add(sheet);
        index.getAndIncrement();
      }
      if (a == 1) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        // 设置只导出有别名的字段
        map.put("id", "序号");
        map.put("hotelCode", "酒店编码");
        map.put("hotelName", "酒店名称");
        map.put("cooperationName", "归属事业部");
        map.put("ownershipName", "管理方式");
        map.put("taskStartTime", "任务时间");
        map.put("security", "安全部");
        map.put("kitchen", "厨房");
        map.put("engineering", "工程部");
        map.put("housekeeper", "管家部");
        map.put("recreation", "康乐部");
        map.put("resources", "人资部");

        SheetDTO sheet = new SheetDTO();
        sheet.setFieldAndAlias(map);
        sheet.setSheetName(index.toString());
        sheet.setCollection(departScoreVoList);
        sheet.setSheetName("部门得分");
        sheetList.add(sheet);
        index.getAndIncrement();
      }
      if (a == 2) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        // 设置只导出有别名的字段
        map.put("id", "序号");
        map.put("hotelCode", "酒店编码");
        map.put("hotelName", "酒店名称");
        map.put("cooperationName", "归属事业部");
        map.put("ownershipName", "管理方式");
        map.put("taskStartTime", "任务时间");
        map.put("safety", "运营安全");
        map.put("publicSecurity", "治安管理");
        map.put("fireSafety", "消防生命安全");
        map.put("health", "职业安全和健康");
        map.put("training", "酒店安全培训");
        map.put("red", "安全红线（个数）");

        SheetDTO sheet = new SheetDTO();
        sheet.setFieldAndAlias(map);
        sheet.setSheetName(index.toString());
        sheet.setCollection(dimensionScoreVoList);
        sheet.setSheetName("维度得分");
        sheetList.add(sheet);
        index.getAndIncrement();
      }
      if (a == 3) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        // 设置只导出有别名的字段
        map.put("id", "序号");
        map.put("hotelCode", "酒店编码");
        map.put("hotelName", "酒店名称");
        map.put("cooperationName", "归属事业部");
        map.put("ownershipName", "管理方式");
        map.put("taskStartTime", "任务时间");
        map.put("g", "各个子系统");
        map.put("h", "消防应急物资配置标准");
        map.put("i", "防暴恐物资配置标准");
        map.put("j", "钥匙管理");
        map.put("k", "消防设备设施");
        map.put("l", "环境安全（安全区域）");
        map.put("m", "突发事件和应急物资（安全区域）");
        map.put("n", "安全培训");
        map.put("o", "电瓶车安全管理");
        map.put("p", "消防相关证照及报告");
        map.put("q", "安全管理委员会");
        map.put("r", "管理台账及报告");
        map.put("s", "外来人员和物资出门");
        map.put("t", "重大事件报告");
        map.put("u", "歇业检查");
        map.put("v", "厨房安全检查");
        map.put("w", "突发事件和应急物资（工程设备区域）");
        map.put("x", "高空作业/动火作业审批许可");
        map.put("y", "康乐安全检查");
        map.put("z", "室内外无动力设备设施");
        map.put("aa", "环境安全（康乐区域）");
        map.put("ab", "环境安全（客房区域）");
        map.put("ac", "残疾人设施");
        map.put("ad", "客房设施安全检查");
        map.put("af", "防疫物资");
        map.put("ae", "虫害消杀");

        SheetDTO sheet = new SheetDTO();
        sheet.setFieldAndAlias(map);
        sheet.setCollection(groupScoreVos);
        sheet.setSheetName("分类得分");
        sheetList.add(sheet);
        index.getAndIncrement();
      }

      if (a == 4) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        // 设置只导出有别名的字段
        map.put("id", "序号");
        map.put("departmentName", "部门");
        map.put("dimensionName", "维度");
        map.put("areaName", "区域");
        map.put("projectName", "题目");
        map.put("className", "分类");
        map.put("qualifiedSize", "合格题目数量");
        map.put("failedSize", "不合格题目数量");
        map.put("notApplication", "不适用题目数量");
        map.put("passRate", "合格率");

        SheetDTO sheet = new SheetDTO();
        sheet.setFieldAndAlias(map);
        sheet.setSheetName(index.toString());
        sheet.setCollection(patrolTopicVo);
        sheet.setSheetName("题项明细");
        sheetList.add(sheet);
        index.getAndIncrement();
      }
    }
    try {
      HuExcelUtils.exportExcel(response, sheetList, "安全巡检明细样式");
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  @Resource
  private QtRectificationTaskMapper qtRectificationTaskMapper;

  @Override
  public List<MultiPlateVo> selectBlocAreaPlateVo(BlocAnalysisPlateBo blocAnalysisPlateBo){
    List<HotelTaskVo> hotelTaskVos = qtCheckTaskMapper.selectBlocTaskRecord(blocAnalysisPlateBo);
    List<Integer> taskIds = hotelTaskVos.stream()
        .map(HotelTaskVo::getTaskId)
        .distinct().collect(Collectors.toList());
    List<MultiPlateVo> multiPlateVos = null;
    if (taskIds.size() > 0){
      // 创建整改任务
      List<RectifiedTaskVo> rectifiedTaskVos = qtRectificationTaskMapper.selectAreaDetails(taskIds,blocAnalysisPlateBo.getType());
      if (null != rectifiedTaskVos && rectifiedTaskVos.size() > 0) {
        // 区域
        multiPlateVos = setMultiPlateVos(rectifiedTaskVos);
      }
    }
    return multiPlateVos;
  }

  @Override
  public List<MultiPlateVo> selectBlocClassifyPlateVo(BlocAnalysisPlateBo blocAnalysisPlateBo){
    List<HotelTaskVo> hotelTaskVos = qtCheckTaskMapper.selectBlocTaskRecord(blocAnalysisPlateBo);
    List<Integer> taskIds = hotelTaskVos.stream()
        .map(HotelTaskVo::getTaskId)
        .distinct().collect(Collectors.toList());
    List<MultiPlateVo> multiPlateVos = null;
    if (taskIds.size() > 0){
      // 创建整改任务
      List<RectifiedTaskVo> rectifiedTaskVos = qtRectificationTaskMapper.selectClassifyDetails(taskIds,blocAnalysisPlateBo.getType());
      if (null != rectifiedTaskVos && rectifiedTaskVos.size() > 0) {
        // 区域
        multiPlateVos = setMultiPlateVos(rectifiedTaskVos);
      }
    }
    return multiPlateVos;
  }
  @Override
  public List<MultiPlateVo> selectBlocDeptPlateVo(BlocAnalysisPlateBo blocAnalysisPlateBo){
    List<HotelTaskVo> hotelTaskVos = qtCheckTaskMapper.selectBlocTaskRecord(blocAnalysisPlateBo);
    List<Integer> taskIds = hotelTaskVos.stream()
        .map(HotelTaskVo::getTaskId)
        .distinct().collect(Collectors.toList());
    List<MultiPlateVo> multiPlateVos = null;
    if (taskIds.size() > 0){
      // 创建整改任务
      List<RectifiedTaskVo> rectifiedTaskVos = qtRectificationTaskMapper.selectDeptDetails(taskIds,blocAnalysisPlateBo.getType());
      if (null != rectifiedTaskVos && rectifiedTaskVos.size() > 0) {
        // 区域
        multiPlateVos = setMultiPlateVos(rectifiedTaskVos);
      }
    }
    return multiPlateVos;
  }

  @Override
  public BlocAnalysisPlateVo selectBlocDimensionPlateVo(BlocAnalysisPlateBo blocAnalysisPlateBo){
    BlocAnalysisPlateVo blocAnalysisPlateVo = new BlocAnalysisPlateVo();
    List<HotelTaskVo> hotelTaskVos = qtCheckTaskMapper.selectBlocTaskRecord(blocAnalysisPlateBo);
    List<Integer> taskIds = hotelTaskVos.stream()
        .map(HotelTaskVo::getTaskId)
        .distinct().collect(Collectors.toList());
    // 创建自查任务：筛选条件下创建的自查任务数量，不含已取消的
    long createTaskNum = hotelTaskVos.stream().filter(a -> !a.getTaskFinishStatus().equals(
        TaskFinishStatusTypeEnum.CANCEL.name())).count();
    // 完成巡查任务：筛选条件下完成的自查任务数量
    long completeBlocTaskNum = hotelTaskVos.stream()
        .filter(a -> a.getTaskFinishStatus().equals(TaskFinishStatusTypeEnum.FINISH.name()))
        .count();
    blocAnalysisPlateVo.setCreateTaskNum(createTaskNum);
    blocAnalysisPlateVo.setCompleteBlocTaskNum(completeBlocTaskNum);
    long createRectificationTaskNum = 0;
    long completedTaskNum = 0;
    long unCompletedTaskNum = 0;
    int redNum = 0;
    if (taskIds.size() > 0){
      // 触及红线任务数量
      redNum = qtRectificationTaskMapper.selectRedLineNum(taskIds);
      // 创建整改任务
      List<RectifiedTaskVo> rectifiedTaskVos = qtRectificationTaskMapper.selectDimensionDetails(taskIds);
      if (null != rectifiedTaskVos && rectifiedTaskVos.size() > 0){
        // 问题点
        createRectificationTaskNum = rectifiedTaskVos.size();
        // 完成整改任务
        completedTaskNum = rectifiedTaskVos.stream()
            .filter(a -> a.getStatus().equals(AgencyTypeEnum.COMPLETED.name()))
            .count();
        // 未完成整改任务
        unCompletedTaskNum = rectifiedTaskVos.stream()
            .filter(a -> !a.getStatus().equals(AgencyTypeEnum.COMPLETED.name()))
            .count();
        // 维度
        blocAnalysisPlateVo.setMultiPlateVos(setMultiPlateVos(rectifiedTaskVos));
      }
    }
    blocAnalysisPlateVo.setCreateTaskNum(createTaskNum);
    blocAnalysisPlateVo.setCompleteBlocTaskNum(completeBlocTaskNum);
    blocAnalysisPlateVo.setCreateRectificationTaskNum(createRectificationTaskNum);
    blocAnalysisPlateVo.setCompletedTaskNum(completedTaskNum);
    blocAnalysisPlateVo.setUnCompletedTaskNum(unCompletedTaskNum);
    blocAnalysisPlateVo.setRedLineNum(redNum);
    return blocAnalysisPlateVo;
  }

  public List<MultiPlateVo> setMultiPlateVos(List<RectifiedTaskVo> rectifiedTaskVos){
    List<MultiPlateVo> multiPlateVos = new ArrayList<>();
    // 区域
    Map<Integer, List<RectifiedTaskVo>> map = rectifiedTaskVos.stream()
        .collect(Collectors.groupingBy(RectifiedTaskVo::getId));
    List<Integer> nums = map.keySet().stream().distinct().collect(Collectors.toList());
    nums.forEach(a ->{
      MultiPlateVo multiPlateVo = new MultiPlateVo();
      long rectifiedNum = map.get(a).stream()
          .filter(b -> b.getStatus().equals(AgencyTypeEnum.COMPLETED.name())).count();
      long unRectifiedNum = map.get(a).stream()
          .filter(b -> !b.getStatus().equals(AgencyTypeEnum.COMPLETED.name())).count();
      multiPlateVo.setModuleName(map.get(a).get(0).getName());
      multiPlateVo.setErrorPointNum(map.get(a).size());
      multiPlateVo.setRectifiedNum(rectifiedNum);
      multiPlateVo.setUnRectifiedNum(unRectifiedNum);
      multiPlateVos.add(multiPlateVo);
    });
    return multiPlateVos;
  }

  @Resource
  private QtBlocCheckMapper qtBlocCheckMapper;

  @Override
  public List<CheckOutVo> searchBlocCheck(){
    return qtBlocCheckMapper.searchBlocCheck();
  }



  public BlocPreviewReportVo searchBlocPreviewReportOptimize(Integer taskId) {
    try {
      BlocPreviewReportVo previewReportVo = new BlocPreviewReportVo();
      TaskVo taskVo = qtCheckTaskMapper.selectPreviewReport(taskId);
      List<String> falt = new ArrayList<>();
      if (taskVo == null || !CheckTypeEnum.BLOC_INSPECTION.name().equals(taskVo.getCheckType())) {
        throw new ServiceException("任务id不正确");
      }
      previewReportVo.setTaskVo(taskVo);
      //各维度得分概览
      DimensionVo dimensionVo = new DimensionVo();
      List<DimensionScoresVo> dimensionScoresVoList = new ArrayList<>();
      //获取有哪些维度
      List<AddCheckWeightsBo> qtCheckAnswerList = qtCheckAnswerMapper.selectdictDimensionList(
        taskId,
        taskVo.getHotelId());
      //获取所有的问题
      List<QuestionVo> questionVoList = qtCheckDetailsMapper.selectWrong(taskId,
        taskVo.getHotelId());
      //维度筛选百分比
      List<AddCheckWeightsBo> percentage = qtCheckAnswerList.stream()
        .filter(weight -> WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(weight.getWeightsType()))
        .collect(
          Collectors.toList());
      percentage.stream().forEach(weightsBo -> {
        DimensionScoresVo dimensionScoresVos = qtCheckDetailsMapper.getDimensionPercentage(taskId,
          taskVo.getHotelId(), weightsBo.getDimensionTypeId());
        if (dimensionScoresVos != null) {
          dimensionScoresVoList.add(dimensionScoresVos);
          if (Boolean.TRUE.equals(dimensionScoresVos.getIfCore())) {
            List<QuestionVo> questionVo = questionVoList.stream()
              .filter(item -> weightsBo.getDimensionTypeId().equals(item.getDimensionId()))
              .collect(
                Collectors.toList());
          }
        }
      });
      //触及直接0分和按个数扣分的项目
      List<AddCheckWeightsBo> notPercentage = qtCheckAnswerList.stream()
        .filter(
          weight -> !WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(weight.getWeightsType()))
        .collect(
          Collectors.toList());
      notPercentage.stream().forEach(weightsBo -> {
        DimensionScoresVo dimensionScoresVos = qtCheckDetailsMapper.getDimensionNotPercentage(
          taskId,
          taskVo.getHotelId(), weightsBo.getDimensionTypeId());
        if (dimensionScoresVos != null) {
          dimensionScoresVoList.add(dimensionScoresVos);
          if (Boolean.TRUE.equals(dimensionScoresVos.getIfCore())) {
            List<QuestionVo> questionVo = questionVoList.stream()
              .filter(item -> weightsBo.getDimensionTypeId().equals(item.getDimensionId()))
              .collect(
                Collectors.toList());
          }
          if (WeightTypeEnum.WEIGHT_TOUCH.name().equals(dimensionScoresVos.getWeightsType())
            && dimensionScoresVos.getScore().compareTo(BigDecimal.ZERO) == 1) {
            falt.add("安全红线");
          }
        }
      });
      //总分合格率
      dimensionVo.setDimensionScoresVos(dimensionScoresVoList);
      //计算合格率
      BigDecimal passRate = qtCheckDetailsMapper.calculatePassRate(taskId, taskVo.getHotelId());
      dimensionVo.setPassRate(passRate == null ? new BigDecimal(0) : passRate);
      //判断是否有触及红线的
      if (!CollectionUtils.isEmpty(falt)) {
        dimensionVo.setTotalScore(new BigDecimal(0));
      } else {
        BigDecimal sum = BigDecimal.ZERO;
        for (DimensionScoresVo scoresVo : dimensionScoresVoList) {
          if (WeightTypeEnum.WEIGHT_PERCENTAGE.name().equals(scoresVo.getWeightsType())) {
            sum = sum.add(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
          }
          if (WeightTypeEnum.WEIGHT_NUMBER.name().equals(scoresVo.getWeightsType())) {
            sum = sum.subtract(scoresVo.getScore().multiply(scoresVo.getWeightsValue()));
          }
        }
        dimensionVo.setTotalScore(sum.setScale(1, BigDecimal.ROUND_HALF_UP));
      }
      previewReportVo.setDimensionScores(dimensionVo);

      //获取各个部门
      List<Integer> partIds = qtCheckAnswerMapper.selectAllDeptName(taskId, taskVo.getHotelId());
      List<DepartmentScoresVo> departmentScoresVoList = new ArrayList<>();
      partIds.stream().forEach(id -> {
        DepartmentScoresVo departmentScoresVo = qtCheckDetailsMapper.calculateDeptScore(taskId,
          taskVo.getHotelId(), id);
        if (departmentScoresVo != null) {
          //获取部门中有问题的数据
          List<QuestionVo> questionVo = questionVoList.stream()
            .filter(item -> id.equals(item.getDeptId()))
            .collect(
              Collectors.toList());
          departmentScoresVoList.add(departmentScoresVo);
        }

      });
      previewReportVo.setDepartmentScores(departmentScoresVoList);
      //各个区域的得分
      List<RegionVo> allArea = qtCheckDetailsMapper.selectAllArea(taskId, taskVo.getHotelId());
      List<RegionVo> regionVos = new ArrayList<>();
      allArea.stream().forEach(area -> {
        //各个区域的分类得分
        RegionVo regionVo = new RegionVo();
        regionVo.setAreaName(area.getAreaName());
        regionVo.setAreaId(area.getAreaId());
        List<RegionScoresVo> scoresVoList = new ArrayList<>();
        List<Integer> classIds = qtCheckDetailsMapper.selectClassification(taskId,
          taskVo.getHotelId(), area.getAreaId());
        classIds.stream().forEach(classId -> {
          RegionScoresVo scoresVo = qtCheckDetailsMapper.selectClassificationScore(taskId,
            taskVo.getHotelId(), area.getAreaId(), classId);
          if (scoresVo != null) {
            scoresVoList.add(scoresVo);
          }

        });
        regionVo.setRegionScoresVos(scoresVoList);
        regionVos.add(regionVo);

      });
      previewReportVo.setRegionScores(regionVos);
      List<String> summaryList = qtCheckDetailsMapper.selectSummary(taskId, taskVo.getHotelId());
      if (CollectionUtils.isEmpty(summaryList)) {
        previewReportVo.setSummary("");
      } else {
        StringBuilder stringBuilder = new StringBuilder();
        summaryList.stream().forEach(s -> {
          stringBuilder.append(s).append("。");
        });
        previewReportVo.setSummary(stringBuilder.toString());
      }
      return previewReportVo;
    } catch (ServiceException e) {
      throw new ServiceException(e.getErrorMessage());
    } catch (Exception e) {
      throw new ServiceException("数据异常");
    }
  }

}