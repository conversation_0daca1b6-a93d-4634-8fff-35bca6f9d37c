package com.shands.mod.quality.service.v2.defaultrecipient.impl;

import cn.hutool.core.util.StrUtil;
import com.shands.mod.dao.mapper.quality.QtDefaultRecipientMapper;
import com.shands.mod.dao.mapper.quality.QtDefaultUserMapper;
import com.shands.mod.dao.model.enums.quality.InsPersonTypeEnum;
import com.shands.mod.dao.model.quality.bo.v2.DefaultSettingBo;
import com.shands.mod.dao.model.quality.bo.v2.DefaultUserSettingBo;
import com.shands.mod.dao.model.quality.bo.v2.UpdateRectificationTaskBo;
import com.shands.mod.dao.model.quality.po.QtDefaultRecipient;
import com.shands.mod.dao.model.quality.po.QtDefaultUser;
import com.shands.mod.dao.model.quality.vo.v2.DefaultRecipientListVo;
import com.shands.mod.dao.model.quality.vo.v2.DefaultRecipientVo;
import com.shands.mod.dao.model.quality.vo.v2.ToDefaultSettingUserVo;
import com.shands.mod.dao.model.quality.vo.v2.ToDefaultSettingVo;
import com.shands.mod.quality.service.v2.defaultrecipient.DefaultRecipientService;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 整改任务默认配置相关接口实现类
 */

@Slf4j
@Service
public class DefaultRecipientServiceImpl implements DefaultRecipientService {

  @Resource
  private QtDefaultRecipientMapper qtDefaultRecipientMapper;
  @Resource
  private QtDefaultUserMapper qtDefaultUserMapper;

  @Override public DefaultRecipientVo defaultRecipient(Integer hotelId,Integer deptId) {
    DefaultRecipientVo defaultRecipientVo=new DefaultRecipientVo();
    UpdateRectificationTaskBo updateRectificationTaskBo =new UpdateRectificationTaskBo();
    updateRectificationTaskBo.setHotelId(hotelId);
    QtDefaultRecipient qtDefaultRecipient = qtDefaultRecipientMapper.queryDeptIdAndHotelId(updateRectificationTaskBo);
    if(qtDefaultRecipient!=null){
      defaultRecipientVo.setDefaultPriority(qtDefaultRecipient.getDefaultPriority());
      defaultRecipientVo.setDefaultTaskTime(qtDefaultRecipient.getDefaultTaskTime());
    }
    defaultRecipientVo.setRectificationPeople(qtDefaultUserMapper.queryDefaultUserIdByTaskId(deptId,hotelId,InsPersonTypeEnum.RECTIFIER_PERSON.name()));
    defaultRecipientVo.setReviewPeople(qtDefaultUserMapper.queryDefaultUserIdByTaskId(deptId,hotelId,InsPersonTypeEnum.REVIEWER_PERSON.name()));
    return defaultRecipientVo;
  }

  @Override
  public DefaultRecipientVo defaultRecipientList(Integer hotelId,Integer judge) {
    DefaultRecipientVo defaultRecipientVo=new DefaultRecipientVo();
    UpdateRectificationTaskBo updateRectificationTaskBo =new UpdateRectificationTaskBo();
    updateRectificationTaskBo.setHotelId(hotelId);
    QtDefaultRecipient qtDefaultRecipient = qtDefaultRecipientMapper.queryDeptIdAndHotelId(updateRectificationTaskBo);
    if(qtDefaultRecipient!=null){
      defaultRecipientVo.setDefaultPriority(qtDefaultRecipient.getDefaultPriority());
      defaultRecipientVo.setDefaultTaskTime(qtDefaultRecipient.getDefaultTaskTime());
    }
    List<DefaultRecipientListVo> defaultRecipientListVos = qtDefaultUserMapper.defaultRecipientList(hotelId,judge);
    defaultRecipientVo.setDefaultRecipientListVoList(defaultRecipientListVos);
    return defaultRecipientVo;
  }

  @Override
  public ToDefaultSettingVo toDefaultSetting(Integer hotelId, Integer deptId) {
    ToDefaultSettingVo toDefaultSettingVo=new ToDefaultSettingVo();
    List<ToDefaultSettingUserVo> rectificationPeople = qtDefaultUserMapper.selectDefaultUser(deptId, hotelId,
        InsPersonTypeEnum.RECTIFIER_PERSON.name());
    List<ToDefaultSettingUserVo> reviewPeople = qtDefaultUserMapper.selectDefaultUser(deptId, hotelId,
      InsPersonTypeEnum.REVIEWER_PERSON.name());
    toDefaultSettingVo.setRectificationPeople(rectificationPeople);
    toDefaultSettingVo.setReviewPeople(reviewPeople);
    return toDefaultSettingVo;
  }

  @Override
  public int defaultSetting(DefaultSettingBo defaultSettingBo) {
    UpdateRectificationTaskBo updateRectificationTaskBo =new UpdateRectificationTaskBo();
    updateRectificationTaskBo.setHotelId(defaultSettingBo.getHotelId());
    QtDefaultRecipient qtDefaultRecipient = qtDefaultRecipientMapper.queryDeptIdAndHotelId(updateRectificationTaskBo);
    if(qtDefaultRecipient!=null){
      //修改
      qtDefaultRecipient.setUpdateTime(new Date());
      qtDefaultRecipient.setUpdateUser(defaultSettingBo.getCreateUser());
      qtDefaultRecipient.setDefaultPriority(defaultSettingBo.getDefaultPriority());
      qtDefaultRecipient.setDefaultTaskTime(defaultSettingBo.getDefaultTaskTime());
      int update = qtDefaultRecipientMapper.update(qtDefaultRecipient);
      return update;
    }else {
      //添加
      qtDefaultRecipient=new QtDefaultRecipient();
      qtDefaultRecipient.setIfDelete(false);
      qtDefaultRecipient.setCreateTime(new Date());
      qtDefaultRecipient.setHotelId(defaultSettingBo.getHotelId());
      qtDefaultRecipient.setDefaultPriority(defaultSettingBo.getDefaultPriority());
      qtDefaultRecipient.setDefaultTaskTime(defaultSettingBo.getDefaultTaskTime());
      int insert = qtDefaultRecipientMapper.insert(qtDefaultRecipient);
      return insert;
    }
  }

  @Override
  public int defaultUserSetting(DefaultUserSettingBo defaultUserSettingBo) {
    try {
      if(StrUtil.isNotEmpty(defaultUserSettingBo.getRectificationPeople())) {
        //删除之前的默认数据
        qtDefaultUserMapper.delectDefaultUser(defaultUserSettingBo.getDeptId(), defaultUserSettingBo.getHotelId(),InsPersonTypeEnum.RECTIFIER_PERSON.name());
        //默认检查人
        String[] rectificationPeoples = defaultUserSettingBo.getRectificationPeople().split(",");
        for (String rectificationPeople : rectificationPeoples) {
          saveUser(InsPersonTypeEnum.RECTIFIER_PERSON.name(), rectificationPeople,
            defaultUserSettingBo);
        }
      }
      if(StrUtil.isNotEmpty(defaultUserSettingBo.getReviewPeople())){
        //删除之前的默认数据
        qtDefaultUserMapper.delectDefaultUser(defaultUserSettingBo.getDeptId(), defaultUserSettingBo.getHotelId(),InsPersonTypeEnum.REVIEWER_PERSON.name());
        //默认复查人
        String[] reviewPeoples = defaultUserSettingBo.getReviewPeople().split(",");
        for (String reviewPeople : reviewPeoples) {
          saveUser(InsPersonTypeEnum.REVIEWER_PERSON.name(),reviewPeople,defaultUserSettingBo);
        }
      }
    }catch (Exception e){
      log.error("设置默认整改人和复查人异常 ",e);
      return 0;
    }
    return 1;
  }

  private void saveUser(String type,String userId,DefaultUserSettingBo defaultUserSettingBo){
    QtDefaultUser qtDefaultUser=new QtDefaultUser();
    qtDefaultUser.setCreateTime(new Date());
    qtDefaultUser.setCreateUser(defaultUserSettingBo.getCreateUser());
    qtDefaultUser.setIfDelete(false);
    qtDefaultUser.setType(type);
    qtDefaultUser.setUserId(Integer.valueOf(userId));
    qtDefaultUser.setDeptId(defaultUserSettingBo.getDeptId());
    qtDefaultUser.setHotelId(defaultUserSettingBo.getHotelId());
    qtDefaultUserMapper.insert(qtDefaultUser);
  }
}
