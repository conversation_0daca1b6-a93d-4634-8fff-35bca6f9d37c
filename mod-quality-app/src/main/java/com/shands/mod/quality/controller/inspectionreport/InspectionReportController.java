package com.shands.mod.quality.controller.inspectionreport;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.quality.bo.AnalyticalBo;
import com.shands.mod.dao.model.quality.bo.CountTimesBo;
import com.shands.mod.dao.model.quality.bo.InspectionReportBo;
import com.shands.mod.dao.model.quality.bo.UpcomingListBo;
import com.shands.mod.dao.model.quality.vo.AbnormalWarningVo;
import com.shands.mod.dao.model.quality.vo.AnalyticalVo;
import com.shands.mod.dao.model.quality.vo.CheckOutVo;
import com.shands.mod.dao.model.quality.vo.CountTimesVo;
import com.shands.mod.dao.model.quality.vo.DetailsCheckVo;
import com.shands.mod.dao.model.quality.vo.InspectionReportVo;
import com.shands.mod.dao.model.quality.vo.PreviewAllVo;
import com.shands.mod.dao.model.quality.vo.PreviewReportVo;
import com.shands.mod.dao.model.quality.vo.UpcomingListVo;
import com.shands.mod.quality.service.inspectionreport.InspectionReportService;
import com.shands.mod.util.StringReplaceUtils;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/3/17
 **/

@RestController
@Slf4j
@Api(value = "inspectionReport", tags = "酒店检查报告")
@RequestMapping("/inspectionReport")
public class InspectionReportController {

  private final InspectionReportService inspectionReportService;

  public InspectionReportController(
      InspectionReportService inspectionReportService) {
    this.inspectionReportService = inspectionReportService;
  }

  @PostMapping("/queryInspectionReportRecord")
  @ApiOperation("酒店检查报告报表分页查询")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = InspectionReportVo.class)
  })
  public ResultVO<PageInfo<InspectionReportVo>> queryInspectionReportRecord(@Valid @RequestBody InspectionReportBo inspectionReportBo){
    return ResultVO.success(inspectionReportService.queryInspectionReportRecord(inspectionReportBo));
  }

  @PostMapping("/getHotelAnalyticalPlate")
  @ApiOperation("分析看板（单店）")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = AnalyticalVo.class)
  })
  public ResultVO<PageInfo<AnalyticalVo>> getHotelAnalyticalPlate(@Valid @RequestBody AnalyticalBo analyticalBo){
    return ResultVO.success(inspectionReportService.getHotelAnalyticalPlate(analyticalBo));
  }

  @PostMapping("/selectAbnormalWarningRecord")
  @ApiOperation("异常预警")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = AbnormalWarningVo.class)
  })
  public ResultVO<PageInfo<AbnormalWarningVo>> selectAbnormalWarningRecord(@Valid @RequestBody AnalyticalBo analyticalBo){
    return ResultVO.success(inspectionReportService.selectAbnormalWarningRecord(analyticalBo));
  }

  @PostMapping("/selectUpcomingListById")
  @ApiOperation("待办清单查询")
  @ApiResponse(code = 0, message = "成功", response = UpcomingListVo.class)
  public ResultVO<PageInfo<UpcomingListVo>> selectUpcomingListById(@Valid @RequestBody UpcomingListBo upcomingListBo){

    PageInfo<UpcomingListVo> upcomingListVoPageInfo = inspectionReportService
        .selectUpcomingListById(upcomingListBo);
    List<UpcomingListVo> voList = upcomingListVoPageInfo.getList();
    if (CollectionUtil.isNotEmpty(voList)) {
      for (UpcomingListVo vo : voList) {
        vo.setUrl(StringReplaceUtils.
            replaceNotNull(vo.getUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
        vo.setRectificationUrl(StringReplaceUtils
            .replaceNotNull(vo.getRectificationUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
      }
    }

    return ResultVO.success(upcomingListVoPageInfo);
  }

  @PostMapping("selectCheckTaskDetails")
  @ApiOperation("检查详情完成次数统计")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = CountTimesVo.class)
  })
  public ResultVO<PageInfo<CountTimesVo>> selectCheckTaskDetails(@Valid @RequestBody CountTimesBo countTimesBo){
    return ResultVO.success(inspectionReportService.selectCheckTaskDetails(countTimesBo));
  }

  @PostMapping("/selectTaskDetails")
  @ApiOperation("检查详情检查任务明细")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = DetailsCheckVo.class)
  })
  public ResultVO<PageInfo<DetailsCheckVo>> selectTaskDetails(@Valid @RequestBody CountTimesBo countTimesBo){
    return ResultVO.success(inspectionReportService.selectTaskDetails(countTimesBo));
  }

  @PostMapping("/selectCheckTable")
  @ApiOperation("检查表下拉框")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = CheckOutVo.class)
  })
  public ResultVO<List<CheckOutVo>> selectCheckTable(){
    return ResultVO.success(inspectionReportService.selectCheckTable());
  }

  @PostMapping("/selectBlocCheckTable")
  @ApiOperation("集团巡检表检查表下拉框")
  @ApiResponses({
      @ApiResponse(code = 0, message = "成功", response = CheckOutVo.class)
  })
  public ResultVO<List<CheckOutVo>> selectBlocCheckTable(){
    return ResultVO.success(inspectionReportService.selectBlocCheckTable());
  }

  @PostMapping("/getPreviewReportByTaskId")
  @ApiOperation("酒店预览/检查报告")
  @ApiResponse(code = 0, message = "成功", response = PreviewReportVo.class)
  public ResultVO<PreviewReportVo> getPreviewReportByTaskId(@RequestBody Map<String,Integer> map){

    PreviewReportVo vo = inspectionReportService.getPreviewReportByTaskId(map.get("taskId"));
    List<PreviewAllVo> previewAllVos = vo.getPreviewAllVos();
    if (CollectionUtil.isNotEmpty(previewAllVos)) {
      for (PreviewAllVo vo1 : previewAllVos) {
        vo1.setUrl(StringReplaceUtils.
            replaceNotNull(vo1.getUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
        vo1.setRectificationUrl(StringReplaceUtils
            .replaceNotNull(vo1.getRectificationUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
      }
    }

    List<UpcomingListVo> upcomingListVos = vo.getUpcomingListVos();
    if (CollectionUtil.isNotEmpty(upcomingListVos)) {
      for (UpcomingListVo upcomingListVo : upcomingListVos) {
        upcomingListVo.setUrl(StringReplaceUtils.
            replaceNotNull(upcomingListVo.getUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
        upcomingListVo.setRectificationUrl(StringReplaceUtils
            .replaceNotNull(upcomingListVo.getRectificationUrl(),"oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
      }
    }

    return ResultVO.success(vo);
  }
}