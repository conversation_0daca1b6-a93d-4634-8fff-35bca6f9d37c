package com.shands.mod.workorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.mapper.workorder.NwRemindMapper;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderMapper;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderScheduleMapper;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.workorder.bo.SendWorkTaskBo;
import com.shands.mod.dao.model.workorder.enums.MessageTemplateEnum;
import com.shands.mod.dao.model.workorder.enums.NoticeUserEnum;
import com.shands.mod.dao.model.workorder.enums.TriggerModeEnum;
import com.shands.mod.dao.model.workorder.enums.TriggerRuleEnum;
import com.shands.mod.dao.model.workorder.enums.TriggerTypeEnum;
import com.shands.mod.dao.model.workorder.po.NwRemind;
import com.shands.mod.dao.model.workorder.po.NwWorkOrder;
import com.shands.mod.dao.model.workorder.po.NwWorkOrderSchedule;
import com.shands.mod.workorder.service.IWorkOrderMessageService;
import com.shands.mod.workorder.service.SendQueueService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/17 14:59
 */
@Service
@Slf4j
public class WorkOrderMessageServiceImpl implements IWorkOrderMessageService {

  @Resource
  private NwWorkOrderMapper nwWorkOrderMapper;
  @Resource
  private NwRemindMapper nwRemindMapper;
  @Resource
  private ModUserDao modUserDao;
  @Resource
  private SendQueueService sendQueueService;
  @Resource
  private NwWorkOrderScheduleMapper nwWorkOrderScheduleMapper;
  @Resource
  private ThreadPoolTaskExecutor threadPoolExecutor;


  /**
   * @param wordId      工单ID
   * @param typeEnum    工单操作类型
   * @param triggerRule 工单触发器状态
   * @param problemId   节点ID
   * @param currentId   当前节点处理人
   * @param beforeId    上一个节点处理人
   * @return
   */
  @Override
  public void send(Integer wordId, TriggerTypeEnum typeEnum, TriggerRuleEnum triggerRule,
      Integer problemId, Integer currentId, Integer beforeId) {

    log.info("工单ID{},工单操作类型{},单触发器状态{},节点ID{},当前节点处理人{},上一个节点处理人{}", wordId,
        typeEnum.getCode(), triggerRule.getCode(), problemId, currentId, beforeId);

    // 获取工单
    NwWorkOrder nwWorkOrder = nwWorkOrderMapper.queryById(wordId);
    if (nwWorkOrder == null) {
      log.error("【消息发送失败】工单不存在：工单id为{}", wordId);
      return;
    }
    if (typeEnum == TriggerTypeEnum.ADD) {
      // 保存新建工单数据和工单节点数据（）
      getOutWork(wordId, null);
    }
    // 获取工单触发器规则
    NwRemind nwRemind = new NwRemind();
    nwRemind.setWorkOrderId(wordId);
    nwRemind.setConditionType(typeEnum.getCode());
    nwRemind.setEventType(triggerRule.getCode());
    List<NwRemind> nwReminds = nwRemindMapper.queryAllByLimit(nwRemind);
    if (CollectionUtils.isEmpty(nwReminds)) {
      log.error("【消息发送失败】触发器规则不存在：工单id为{},事件为{},规则{}", wordId, typeEnum.getCode(),
          triggerRule.getCode());
      return;
    }
    // 当前处理人
    ModUser acceptUser;
    // 工单发起人
    ModUser createUser;
    // 关联客户
    ModUser customerMobile;
    // 当前节点处理人
    ModUser currentUser;
    // 上一个节点处理人或节点变更前受理人
    ModUser beforeUser;
    // 工单转交接受人
    ModUser forwardUser;
    String content;
    for (NwRemind remind : nwReminds) {

      if (remind.getConditionType().equals(TriggerTypeEnum.ADD.getCode())) {
        if (remind.getEventType().equals(TriggerRuleEnum.ADD_WORD.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_1.getCode())) {
            acceptUser = modUserDao.queryById(nwWorkOrder.getAcceptUser());
            createUser = modUserDao.queryById(nwWorkOrder.getCreateUser());
            content = MessageTemplateEnum.TEXT_01.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{priority}}", nwWorkOrder.getPriority())
                .replace("#{{acceptUser}}", acceptUser.getName());
            getSendWorkTaskBo("工单服务通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_01.getUrl(), createUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_2.getCode())) {
            acceptUser = modUserDao.queryById(nwWorkOrder.getAcceptUser());
            createUser = modUserDao.queryById(nwWorkOrder.getCreateUser());
            content = MessageTemplateEnum.TEXT_02.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{priority}}", nwWorkOrder.getPriority())
                .replace("#{{createUser}}", createUser.getName());
            getSendWorkTaskBo("工单服务通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_02.getUrl(), acceptUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_3.getCode())) {
            customerMobile = modUserDao.queryByMobile(nwWorkOrder.getCustomerMobile());
            if (customerMobile == null) {
              log.info("数据不存在,{}", NoticeUserEnum.WORD_USER_3.getCode());
              continue;
            }
            content = MessageTemplateEnum.TEXT_03.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{customer}}", customerMobile.getName());
            getSendWorkTaskBo("工单服务通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_03.getUrl(), customerMobile.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_4.getCode())) {
            createUser = modUserDao.queryById(nwWorkOrder.getCreateUser());
            content = MessageTemplateEnum.TEXT_04.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{priority}}", nwWorkOrder.getPriority())
                .replace("#{{createUser}}", createUser.getName());
            String finalContent = content;
            // 工单关注人
            List<ModUser> copyUserList = getCopyUser(nwWorkOrder);
            copyUserList.forEach(modUser -> {
              getSendWorkTaskBo("工单服务通知", finalContent, remind.getMessageType(),
                  MessageTemplateEnum.TEXT_04.getUrl(), modUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            });
            continue;
          }
        }
      }
      if (remind.getConditionType().equals(TriggerTypeEnum.UPDATE.getCode())) {

        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_1.getCode())) {
          getOutWork(nwWorkOrder.getId(), TriggerRuleEnum.WORD_STATUS_6.getCode());
          currentUser = modUserDao.queryById(currentId);
          beforeUser = modUserDao.queryById(beforeId);
          content = MessageTemplateEnum.TEXT_05.getContent()
              .replace("#{{title}}", nwWorkOrder.getTitle())
              .replace("#{{priority}}", nwWorkOrder.getPriority())
              .replace("#{{backAcceptUser}}", currentUser.getName());
          getSendWorkTaskBo("工单节点更新通知", content, remind.getMessageType(),
              MessageTemplateEnum.TEXT_05.getUrl(), beforeUser.getId(), nwWorkOrder.getId(),remind.getEventType());
          continue;
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_2.getCode())) {
          createUser = modUserDao.queryById(nwWorkOrder.getCreateUser());
          content = MessageTemplateEnum.TEXT_06.getContent()
              .replace("#{{title}}", nwWorkOrder.getTitle());
          getSendWorkTaskBo("工单节点处理完成", content, remind.getMessageType(),
              MessageTemplateEnum.TEXT_06.getUrl(), createUser.getId(), nwWorkOrder.getId(),remind.getEventType());
          continue;
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_3.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_5.getCode())) {
            currentUser = modUserDao.queryById(currentId);
            beforeUser = modUserDao.queryById(beforeId);
            content = MessageTemplateEnum.TEXT_07.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", currentUser.getName());
            getSendWorkTaskBo("工单节点变更通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_07.getUrl(), beforeUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_2.getCode())) {
            createUser = modUserDao.queryById(nwWorkOrder.getCreateUser());
            currentUser = modUserDao.queryById(currentId);
            content = MessageTemplateEnum.TEXT_08.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{createUser}}", createUser.getName());
            getSendWorkTaskBo("工单节点变更通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_08.getUrl(), currentUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_4.getCode())) {
          acceptUser = modUserDao.queryById(nwWorkOrder.getAcceptUser());
          content = MessageTemplateEnum.TEXT_09.getContent()
              .replace("#{{title}}", nwWorkOrder.getTitle());
          getSendWorkTaskBo("工单关闭通知", content, remind.getMessageType(),
              MessageTemplateEnum.TEXT_09.getUrl(), acceptUser.getId(), nwWorkOrder.getId(),remind.getEventType());
          continue;
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_5.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_1.getCode())) {
            createUser = modUserDao.queryById(nwWorkOrder.getCreateUser());
            content = MessageTemplateEnum.TEXT_10.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{priority}}", nwWorkOrder.getPriority());
            getSendWorkTaskBo("工单服务已完成通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_01.getUrl(), createUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_3.getCode())) {
            customerMobile = modUserDao.queryByMobile(nwWorkOrder.getCustomerMobile());
            content = MessageTemplateEnum.TEXT_11.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{customer}}", customerMobile.getName());
            getSendWorkTaskBo("工单服务已完成通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_11.getUrl(), customerMobile.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_10.getCode())) {
          acceptUser = modUserDao.queryById(nwWorkOrder.getAcceptUser());
          content = MessageTemplateEnum.TEXT_18.getContent()
              .replace("#{{title}}", nwWorkOrder.getTitle());
          getSendWorkTaskBo("工单信息变更通知", content, remind.getMessageType(),
              MessageTemplateEnum.TEXT_18.getUrl(), acceptUser.getId(), nwWorkOrder.getId(),remind.getEventType());
          continue;
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_11.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_6.getCode())) {
            currentUser = modUserDao.queryById(currentId);
            forwardUser = modUserDao.queryById(beforeId);
            content = MessageTemplateEnum.TEXT_19.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", currentUser.getName());
            getSendWorkTaskBo("工单转交通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_19.getUrl(), forwardUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_12.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_5.getCode())) {
            currentUser = modUserDao.queryById(currentId);
            forwardUser = modUserDao.queryById(beforeId);
            content = MessageTemplateEnum.TEXT_20.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", forwardUser.getName());
            getSendWorkTaskBo("工单转交同意通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_20.getUrl(), currentUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_13.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_2.getCode())) {
            currentUser = modUserDao.queryById(currentId);
            forwardUser = modUserDao.queryById(beforeId);
            content = MessageTemplateEnum.TEXT_21.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", forwardUser.getName());
            getSendWorkTaskBo("工单转交拒绝通知", content, remind.getMessageType(),
                MessageTemplateEnum.TEXT_21.getUrl(), currentUser.getId(), nwWorkOrder.getId(),remind.getEventType());
            continue;
          }
        }
      }
    }
  }

  private List<ModUser> getCopyUser(NwWorkOrder nwWorkOrder) {
    List<ModUser> copyUserList = new ArrayList<>();
    if (!StringUtils.isEmpty(nwWorkOrder.getCopyUser())) {
      List<String> list = Arrays.asList(nwWorkOrder.getCopyUser().split(","));
      for (String s : list) {
        ModUser modUser = modUserDao.queryById(Integer.valueOf(s));
        if (modUser != null) {
          copyUserList.add(modUser);
        }
      }
    }
    return copyUserList;
  }

  @Override
  public void getOutWork(Integer workId, String triggerRule) {

//    this.threadPoolExecutor.execute(() -> {
    try {
      NwWorkOrder nwWorkOrder = nwWorkOrderMapper.queryById(workId);
      // 获取工单触发器规则
      NwRemind nwRemind = new NwRemind();
      nwRemind.setWorkOrderId(workId);
      nwRemind.setConditionType(TriggerTypeEnum.OUT_TIME.getCode());
      if (!StringUtils.isEmpty(triggerRule)) {
        nwRemind.setEventType(triggerRule);
      }
      List<NwRemind> nwReminds = nwRemindMapper.queryAllByLimit(nwRemind);
      if (CollectionUtils.isEmpty(nwReminds)) {
        log.error("【延时消息发送失败】触发器规则不存在：工单id为{},事件为{}", workId, TriggerTypeEnum.OUT_TIME.getCode());
        return;
      }
      ModUser acceptUser;
      String content;

      for (NwRemind remind : nwReminds) {
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_6.getCode())) {
          NwWorkOrderSchedule nwWorkOrderSchedule = nwWorkOrderScheduleMapper.queryAllById(workId);
          if (nwWorkOrderSchedule == null) {
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_2.getCode())) {
            content = MessageTemplateEnum.TEXT_12.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle());
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_12.getUrl(), nwWorkOrderSchedule.getUser(),
                    nwWorkOrderSchedule.getId(),
                    TriggerRuleEnum.WORD_STATUS_6.getCode(), i * remind.getForTime(),
                    nwWorkOrder.getId());
              }
            } else {
              getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                  MessageTemplateEnum.TEXT_12.getUrl(), nwWorkOrderSchedule.getUser(),
                  nwWorkOrderSchedule.getId(),
                  TriggerRuleEnum.WORD_STATUS_6.getCode(), remind.getTime(), nwWorkOrder.getId());
            }
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_1.getCode())) {
            acceptUser = modUserDao.queryById(nwWorkOrderSchedule.getUser());
            content = MessageTemplateEnum.TEXT_14.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", acceptUser.getName());
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_14.getUrl(), nwWorkOrder.getCreateUser(),
                    nwWorkOrderSchedule.getId(),
                    TriggerRuleEnum.WORD_STATUS_6.getCode(), i * remind.getForTime(),
                    nwWorkOrder.getId());
              }
            } else {
              getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                  MessageTemplateEnum.TEXT_14.getUrl(), nwWorkOrder.getCreateUser(),
                  nwWorkOrderSchedule.getId(),
                  TriggerRuleEnum.WORD_STATUS_6.getCode(), remind.getTime(), nwWorkOrder.getId());
            }
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_4.getCode())) {
            acceptUser = modUserDao.queryById(nwWorkOrderSchedule.getUser());
            List<ModUser> copyUserList = getCopyUser(nwWorkOrder);
            content = MessageTemplateEnum.TEXT_16.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", acceptUser.getName());
            String finalContent = content;
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                int finalI = i;
                copyUserList.forEach(modUser -> {
                  getSendWorkOutTaskBo("工单超时通知", finalContent, remind.getMessageType(),
                      MessageTemplateEnum.TEXT_16.getUrl(), modUser.getId(),
                      nwWorkOrderSchedule.getId(),
                      TriggerRuleEnum.WORD_STATUS_6.getCode(), finalI * remind.getForTime(),
                      nwWorkOrder.getId());
                });
              }
            } else {
              copyUserList.forEach(modUser -> {
                getSendWorkOutTaskBo("工单超时通知", finalContent, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_16.getUrl(), modUser.getId(),
                    nwWorkOrderSchedule.getId(),
                    TriggerRuleEnum.WORD_STATUS_6.getCode(), remind.getTime(),
                    nwWorkOrder.getId());
              });
            }
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_8.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_1.getCode())) {
            acceptUser = modUserDao.queryById(nwWorkOrder.getAcceptUser());
            content = MessageTemplateEnum.TEXT_13.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
                .replace("#{{acceptUser}}", nwWorkOrder.getTitle());
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_13.getUrl(), acceptUser.getId(), nwWorkOrder.getId(),
                    TriggerRuleEnum.WORD_STATUS_8.getCode(), i * remind.getForTime(),
                    nwWorkOrder.getId());
              }
            } else {
              getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                  MessageTemplateEnum.TEXT_13.getUrl(), acceptUser.getId(), nwWorkOrder.getId(),
                  TriggerRuleEnum.WORD_STATUS_8.getCode(), remind.getTime(), nwWorkOrder.getId());
            }
            continue;
          }
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_4.getCode())) {
            List<ModUser> copyUserList = getCopyUser(nwWorkOrder);
            content = MessageTemplateEnum.TEXT_17.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle());
            String finalContent = content;
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                int finalI = i;
                copyUserList.forEach(modUser -> {
                  getSendWorkOutTaskBo("工单超时通知", finalContent, remind.getMessageType(),
                      MessageTemplateEnum.TEXT_17.getUrl(), modUser.getId(), nwWorkOrder.getId(),
                      TriggerRuleEnum.WORD_STATUS_8.getCode(), finalI * remind.getForTime(),
                      nwWorkOrder.getId());
                });
              }
            } else {
              copyUserList.forEach(modUser -> {
                getSendWorkOutTaskBo("工单超时通知", finalContent, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_17.getUrl(), modUser.getId(), nwWorkOrder.getId(),
                    TriggerRuleEnum.WORD_STATUS_8.getCode(), remind.getTime(), nwWorkOrder.getId());
              });
            }
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_7.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_1.getCode())) {
            content = MessageTemplateEnum.TEXT_15.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle());
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_15.getUrl(), nwWorkOrder.getCreateUser(),
                    nwWorkOrder.getId(), TriggerRuleEnum.WORD_STATUS_7.getCode(),
                    i * remind.getForTime(),
                    nwWorkOrder.getId());
              }
            } else {
              getSendWorkOutTaskBo("工单超时通知", content, remind.getMessageType(),
                  MessageTemplateEnum.TEXT_15.getUrl(), nwWorkOrder.getCreateUser(),
                  nwWorkOrder.getId(), TriggerRuleEnum.WORD_STATUS_7.getCode(), remind.getTime(),
                  nwWorkOrder.getId());
            }
            continue;
          }
        }
        if (remind.getEventType().equals(TriggerRuleEnum.WORD_STATUS_14.getCode())) {
          if (remind.getUserType().equals(NoticeUserEnum.WORD_USER_2.getCode())) {
            ModUser modUser = modUserDao.queryById(nwWorkOrder.getForwardUser());
            if (modUser == null ){
              continue;
            }
            content = MessageTemplateEnum.TEXT_22.getContent()
                .replace("#{{title}}", nwWorkOrder.getTitle())
              .replace("#{{acceptUser}}", modUser.getName());
            if (TriggerModeEnum.REPEATEDLY.name().equals(remind.getTriggerMode())) {
              for (int i = 1; i <= remind.getForNum(); i++) {
                getSendWorkOutTaskBo("工单转单超时通知", content, remind.getMessageType(),
                    MessageTemplateEnum.TEXT_22.getUrl(), nwWorkOrder.getAcceptUser(),
                    nwWorkOrder.getId(), TriggerRuleEnum.WORD_STATUS_14.getCode(),
                    i * remind.getForTime(),
                    nwWorkOrder.getId());
              }
            } else {
              getSendWorkOutTaskBo("工单转单超时通知", content, remind.getMessageType(),
                  MessageTemplateEnum.TEXT_22.getUrl(), nwWorkOrder.getAcceptUser(),
                  nwWorkOrder.getId(), TriggerRuleEnum.WORD_STATUS_14.getCode(), remind.getTime(),
                  nwWorkOrder.getId());
            }
            continue;
          }
        }
      }
    } catch (Exception e) {
      log.error("getOutWork->" + workId, e.getMessage(), e);
    }
//    });

  }

  private void getSendWorkTaskBo(String title, String content, String messageType, String url,
      Integer id, Integer workMessageId,String ruleCode) {
    SendWorkTaskBo sendWorkTaskBo = new SendWorkTaskBo();
    sendWorkTaskBo.setTitle(title);
    sendWorkTaskBo.setContent(content);
    sendWorkTaskBo.setType(ruleCode);
    sendWorkTaskBo.setMessageType(messageType);
    sendWorkTaskBo.setUrl(url);
    sendWorkTaskBo.setUserId(id);
    sendWorkTaskBo.setWorkMessageId(workMessageId);
    log.info("getSendWorkTaskBo--->消息内容：{}", JSON.toJSONString(sendWorkTaskBo));
    sendQueueService.sendWorkQueue(sendWorkTaskBo);
  }

  private void getSendWorkOutTaskBo(String title, String content, String messageType, String url,
      Integer id, Integer workId, String type, Integer time, Integer workMessageId) {
    SendWorkTaskBo sendWorkTaskBo = new SendWorkTaskBo();
    sendWorkTaskBo.setTitle(title);
    sendWorkTaskBo.setContent(content);
    sendWorkTaskBo.setMessageType(messageType);
    sendWorkTaskBo.setUrl(url);
    sendWorkTaskBo.setUserId(id);
    sendWorkTaskBo.setWorkId(workId);
    sendWorkTaskBo.setType(type);
    sendWorkTaskBo.setWorkMessageId(workMessageId);
    sendWorkTaskBo.setTime(time);
    log.info("getSendWorkOutTaskBo--->消息内容：{},延时时间{}", JSON.toJSONString(sendWorkTaskBo), time);
    sendQueueService.sendOutWorkQueue(sendWorkTaskBo, time == null ? 0 : (long) (time * 60 * 1000));
  }

}
