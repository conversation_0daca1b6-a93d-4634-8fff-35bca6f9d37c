package com.shands.mod.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.workorder.NwDictInfoMapper;
import com.shands.mod.dao.mapper.workorder.NwTemplateDictMapper;
import com.shands.mod.dao.mapper.workorder.NwTemplateTriggerMapper;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderNodeMapper;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderTemplateMapper;
import com.shands.mod.dao.mapper.workorder.NwWorkOrderTriggerMapper;
import com.shands.mod.dao.model.enums.UserRightsTypeEnum;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.workorder.bo.NwTemplateDictAdd;
import com.shands.mod.dao.model.workorder.bo.NwWorkOrderNodeAdd;
import com.shands.mod.dao.model.workorder.bo.NwWorkOrderTemplateAdd;
import com.shands.mod.dao.model.workorder.bo.NwWorkOrderTemplateQuery;
import com.shands.mod.dao.model.workorder.po.NwTemplateDict;
import com.shands.mod.dao.model.workorder.po.NwTemplateTrigger;
import com.shands.mod.dao.model.workorder.po.NwWorkOrderNode;
import com.shands.mod.dao.model.workorder.po.NwWorkOrderTemplate;
import com.shands.mod.dao.model.workorder.po.NwWorkOrderTrigger;
import com.shands.mod.dao.model.workorder.vo.NwDictInfoVo;
import com.shands.mod.dao.model.workorder.vo.NwDictVo;
import com.shands.mod.dao.model.workorder.vo.NwNodeVo;
import com.shands.mod.dao.model.workorder.vo.NwWorkOrderTemplateVo;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.workorder.service.INwOpterDetailService;
import com.shands.mod.workorder.service.INwWorkOrderTemplateService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.shands.uc.base.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Slf4j
@Service("nwWorkOrderTemplateService")
public class NwWorkOrderTemplateServiceImpl implements INwWorkOrderTemplateService {

  private final NwWorkOrderTemplateMapper nwWorkOrderTemplateMapper;
  private final NwWorkOrderNodeMapper nwWorkOrderNodeMapper;
  private final NwTemplateDictMapper nwTemplateDictMapper;
  private final NwTemplateTriggerMapper nwTemplateTriggerMapper;
  private final ModHotelInfoDao modHotelInfoDao;
  private final NwDictInfoMapper nwDictInfoMapper;
  private final INwOpterDetailService nwOpterDetailService;
  private final NwWorkOrderTriggerMapper nwWorkOrderTriggerMapper;
  private final ModUserCommonService modUserCommonService;

  @Autowired
  public NwWorkOrderTemplateServiceImpl(NwWorkOrderTemplateMapper nwWorkOrderTemplateMapper,
      NwWorkOrderNodeMapper nwWorkOrderNodeMapper, NwTemplateDictMapper nwTemplateDictMapper,
      NwTemplateTriggerMapper nwTemplateTriggerMapper, ModHotelInfoDao modHotelInfoDao,
      NwDictInfoMapper nwDictInfoMapper, INwOpterDetailService nwOpterDetailService,
      NwWorkOrderTriggerMapper nwWorkOrderTriggerMapper,ModUserCommonService modUserCommonService) {
    this.nwWorkOrderTemplateMapper = nwWorkOrderTemplateMapper;
    this.nwWorkOrderNodeMapper = nwWorkOrderNodeMapper;
    this.nwTemplateDictMapper = nwTemplateDictMapper;
    this.nwTemplateTriggerMapper = nwTemplateTriggerMapper;
    this.modHotelInfoDao = modHotelInfoDao;
    this.nwDictInfoMapper = nwDictInfoMapper;
    this.nwOpterDetailService = nwOpterDetailService;
    this.nwWorkOrderTriggerMapper = nwWorkOrderTriggerMapper;
    this.modUserCommonService = modUserCommonService;
  }

  @Transactional
  @Override
  public boolean add(NwWorkOrderTemplateAdd nwWorkOrderTemplateAdd) {
//    try {
    String acceptHotel = nwWorkOrderTemplateAdd.getAcceptHotel();
    String[] split = acceptHotel.split(",");
    boolean group = false;
    boolean hotel = false;
    for (String s : split) {
      ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(Integer.valueOf(s));
      if (modHotelInfo.getHotelCode().equals("000001") || modHotelInfo.getHotelCode().equals("HOTELGO")) {
        group = true;
      } else {
        hotel = true;
      }
    }
//    if (group && hotel){
//      throw new ServiceException("受理机构中不能同时存在集团和酒店");
//    }
    NwWorkOrderTemplate nwWorkOrderTemplate = NwWorkOrderTemplate.builder()
        .name(nwWorkOrderTemplateAdd.getName())
        .platform(nwWorkOrderTemplateAdd.getPlatform())
        .transfer(nwWorkOrderTemplateAdd.getTransfer())
        .remark(nwWorkOrderTemplateAdd.getRemark())
        .serviceTypeId(nwWorkOrderTemplateAdd.getServiceTypeId())
        .acceptHotel(nwWorkOrderTemplateAdd.getAcceptHotel())
        .deleted(0)
        .status(true)
        .createTime(new Date())
        .createUser(BaseThreadLocalHelper.getUser().getId())
        .updateTime(new Date())
        .updateUser(BaseThreadLocalHelper.getUser().getId())
        .build();
    int insert = nwWorkOrderTemplateMapper.insert(nwWorkOrderTemplate);

    try {
      List<NwWorkOrderNodeAdd> nwWorkOrderNodeAddList = nwWorkOrderTemplateAdd
          .getNwNodeVoList();
      if (nwWorkOrderNodeAddList != null && nwWorkOrderNodeAddList.size() > 0) {
        nwWorkOrderNodeAddList.forEach(nwWorkOrderNodeAdd -> {
          NwWorkOrderNode nwWorkOrderNode = NwWorkOrderNode.builder()
              .bussinessName(nwWorkOrderNodeAdd.getBussinessName())
              .acceptType(nwWorkOrderNodeAdd.getAcceptType().getCode())
              .acceptUser(nwWorkOrderNodeAdd.getAcceptUser())
              .acceptUserId(nwWorkOrderNodeAdd.getAcceptUserId())
              .replaceAcceptUser(nwWorkOrderNodeAdd.getReplaceAcceptUser())
              .templateId(nwWorkOrderTemplate.getId())
              .deleted(0)
              .createTime(new Date())
              .createUser(BaseThreadLocalHelper.getUser().getId())
              .updateTime(new Date())
              .updateUser(BaseThreadLocalHelper.getUser().getId())
              .build();
          int insert1 = nwWorkOrderNodeMapper.insert(nwWorkOrderNode);
        });
      }
    } catch (Exception e) {
      log.info(e.getMessage());
      throw new ServiceException("工单模板节点添加失败");
    }

    try {
      List<NwTemplateDictAdd> dictList = nwWorkOrderTemplateAdd.getDictVoList();
      if (dictList != null && dictList.size() > 0) {
        dictList.forEach(dict -> {
          NwTemplateDict nwTemplateDict = NwTemplateDict.builder()
              .dictId(dict.getDictId())
              .ifNull(dict.isIfNull())
              .templateId(nwWorkOrderTemplate.getId())
              .build();
          int insert1 = nwTemplateDictMapper.insert(nwTemplateDict);
        });
      }
    } catch (Exception e) {
      log.info(e.getMessage());
      throw new ServiceException("工单模板配置字段添加失败");
    }

    try {
      List<Integer> triggerList = nwWorkOrderTemplateAdd.getTriggerList();
      if (triggerList != null & triggerList.size() > 0) {
        triggerList.forEach(triggerId -> {
          NwTemplateTrigger nwTemplateTrigger = NwTemplateTrigger.builder()
              .templateId(nwWorkOrderTemplate.getId())
              .triggerId(triggerId)
              .build();
          int insert1 = nwTemplateTriggerMapper.insert(nwTemplateTrigger);
        });
      }
    } catch (Exception e) {
      log.info(e.getMessage());
      throw new ServiceException("工单模板触发器添加失败");
    }
    return true;
  }

  @Transactional
  @Override
  public boolean update(NwWorkOrderTemplateAdd nwWorkOrderTemplateAdd) {
    String acceptHotel = nwWorkOrderTemplateAdd.getAcceptHotel();
    String[] split = acceptHotel.split(",");
    boolean group = false;
    boolean hotel = false;
    for (String s : split) {
      ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(Integer.valueOf(s));
      if (modHotelInfo.getHotelCode().equals("000001") || modHotelInfo.getHotelCode().equals("HOTELGO")) {
        group = true;
      } else {
        hotel = true;
      }
    }
//    if (group && hotel){
//      throw new ServiceException("受理机构中不能同时存在集团和酒店");
//    }
    NwWorkOrderTemplate nwWorkOrderTemplate = nwWorkOrderTemplateMapper
        .queryById(nwWorkOrderTemplateAdd.getId());
    NwWorkOrderTemplateVo nwWorkOrderTemplateVo = this.selectById(nwWorkOrderTemplateAdd.getId());
    if (nwWorkOrderTemplate == null) {
      return false;
    }
    nwWorkOrderTemplate.setName(nwWorkOrderTemplateAdd.getName());
    nwWorkOrderTemplate.setPlatform(nwWorkOrderTemplateAdd.getPlatform());
    nwWorkOrderTemplate.setTransfer(nwWorkOrderTemplateAdd.getTransfer());
    nwWorkOrderTemplate.setRemark(nwWorkOrderTemplateAdd.getRemark());
    nwWorkOrderTemplate.setServiceTypeId(nwWorkOrderTemplateAdd.getServiceTypeId());
    nwWorkOrderTemplate.setAcceptHotel(nwWorkOrderTemplateAdd.getAcceptHotel());
    nwWorkOrderTemplate.setStatus(true);
    nwWorkOrderTemplate.setUpdateTime(new Date());
    nwWorkOrderTemplate.setUpdateUser(BaseThreadLocalHelper.getUser().getId());
    nwWorkOrderTemplateMapper.update(nwWorkOrderTemplate);

    try {
      //删除模板节点
      nwWorkOrderNodeMapper.deleteByTemplateId(nwWorkOrderTemplate.getId());

      List<NwWorkOrderNodeAdd> nwWorkOrderNodeAddList = nwWorkOrderTemplateAdd
          .getNwNodeVoList();
      if (nwWorkOrderNodeAddList != null && nwWorkOrderNodeAddList.size() > 0) {
        nwWorkOrderNodeAddList.forEach(nwWorkOrderNodeAdd -> {
          NwWorkOrderNode nwWorkOrderNode = NwWorkOrderNode.builder()
              .bussinessName(nwWorkOrderNodeAdd.getBussinessName())
              .acceptType(nwWorkOrderNodeAdd.getAcceptType().getCode())
              .acceptUser(nwWorkOrderNodeAdd.getAcceptUser())
              .acceptUserId(nwWorkOrderNodeAdd.getAcceptUserId())
              .replaceAcceptUser(nwWorkOrderNodeAdd.getReplaceAcceptUser())
              .templateId(nwWorkOrderTemplate.getId())
              .deleted(0)
              .createTime(new Date())
              .createUser(BaseThreadLocalHelper.getUser().getId())
              .updateTime(new Date())
              .updateUser(BaseThreadLocalHelper.getUser().getId())
              .build();
          nwWorkOrderNodeMapper.insert(nwWorkOrderNode);
        });
      }
    } catch (Exception e) {
      log.info(e.getMessage());
      throw new ServiceException("模板节点更新失败");
    }

    try {
      //删除模板-字段关联
      nwTemplateDictMapper.deleteByTemplateId(nwWorkOrderTemplate.getId());
      //新增模板-字段关联
      List<NwTemplateDictAdd> dictList = nwWorkOrderTemplateAdd.getDictVoList();
      if (dictList != null && dictList.size() > 0) {
        dictList.forEach(dict -> {
          NwTemplateDict nwTemplateDict = NwTemplateDict.builder()
              .dictId(dict.getDictId())
              .ifNull(dict.isIfNull())
              .templateId(nwWorkOrderTemplate.getId())
              .build();
          int insert1 = nwTemplateDictMapper.insert(nwTemplateDict);
        });
      }
    } catch (Exception e) {
      log.info(e.getMessage());
      throw new ServiceException("模板字段更新失败");
    }

    try {
      //删除模板-触发器关联
      nwTemplateTriggerMapper.deleteByTemplateId(nwWorkOrderTemplate.getId());
      //新增模板-触发器关联
      List<Integer> triggerList = nwWorkOrderTemplateAdd.getTriggerList();
      if (triggerList != null && triggerList.size() > 0) {
        triggerList.forEach(triggerId -> {
          NwTemplateTrigger nwTemplateTrigger = NwTemplateTrigger.builder()
              .templateId(nwWorkOrderTemplate.getId())
              .triggerId(triggerId)
              .build();
          int insert1 = nwTemplateTriggerMapper.insert(nwTemplateTrigger);
        });
      }
    } catch (Exception e) {
      log.info(e.getMessage());
      throw new ServiceException("模板触发器更新失败");
    }
    nwOpterDetailService.saveLog("TEMPLATE", nwWorkOrderTemplateAdd.getId(),
        JSON.toJSONString(nwWorkOrderTemplateVo), JSON.toJSONString(nwWorkOrderTemplateAdd));
    return true;
  }

  @Override
  public boolean delete(Integer templateId) {
    NwWorkOrderTemplate nwWorkOrderTemplate = nwWorkOrderTemplateMapper.queryById(templateId);
    nwWorkOrderTemplate.setDeleted(1);
    nwWorkOrderTemplateMapper.update(nwWorkOrderTemplate);
    return true;
  }

  @Override
  public boolean updateStatus(Integer templateId) throws ServiceException {
    NwWorkOrderTemplate nwWorkOrderTemplate = nwWorkOrderTemplateMapper.queryById(templateId);
    if (nwWorkOrderTemplate.isStatus()) {
      nwWorkOrderTemplate.setStatus(false);
    } else {
      boolean status = true;
      List<NwDictVo> nwDictVos = nwTemplateDictMapper.selectByTemplateId(templateId);
      List<NwWorkOrderTrigger> nwWorkOrderTriggers = nwWorkOrderTriggerMapper
          .selectByTemplateId(templateId);
      for (NwDictVo nwDictVo : nwDictVos) {
        if (!nwDictVo.isStatus()) {
          status = false;
        }
      }
      for (NwWorkOrderTrigger nwWorkOrderTrigger : nwWorkOrderTriggers) {
        if (nwWorkOrderTrigger.getStatus() == 0) {
          status = false;
        }
      }
      if (status) {
        nwWorkOrderTemplate.setStatus(true);
      } else {
        throw new ServiceException("该模版下存在已禁用字段/已禁用触发器/已禁用业务类型，不可启用。");
      }
    }
    nwWorkOrderTemplateMapper.update(nwWorkOrderTemplate);
    return true;
  }

  @Override
  public List<NwWorkOrderTemplateVo> queryPage(NwWorkOrderTemplateQuery
      nwWorkOrderTemplateQuery, Integer pageSize,
      Integer pageNo) {
    if (pageNo == null) {
      pageNo = 1;
    }
    if (pageSize == null) {
      pageSize = 10;
    }
    PageHelper.startPage(pageNo, pageSize);
    List<NwWorkOrderTemplateVo> nwWorkOrderTemplateVos = nwWorkOrderTemplateMapper
        .pageList(nwWorkOrderTemplateQuery);
    return nwWorkOrderTemplateVos;
  }

  @Override
  public NwWorkOrderTemplateVo selectById(Integer templateId) {
    NwWorkOrderTemplate nwWorkOrderTemplate = nwWorkOrderTemplateMapper.queryById(templateId);
    NwWorkOrderTemplateVo nwWorkOrderTemplateVo = new NwWorkOrderTemplateVo();
    BeanUtil.copyProperties(nwWorkOrderTemplate, nwWorkOrderTemplateVo);

    String acceptHotel = nwWorkOrderTemplateVo.getAcceptHotel();
    if (!StringUtils.isEmpty(acceptHotel)) {
      String[] split = acceptHotel.split(",");
      if (split.length > 0) {
        List<ModHotelInfo> hotelInfoList = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
          ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(Integer.valueOf(split[i]));
          hotelInfoList.add(modHotelInfo);
        }
        nwWorkOrderTemplateVo.setModHotelInfoList(hotelInfoList);
      }
    }

    List<Integer> triggerList = nwTemplateTriggerMapper.selectByTemplateId(templateId);
    List<NwDictVo> nwDictVos = nwTemplateDictMapper.selectByTemplateId(templateId);
    nwDictVos.forEach(nwDictVo -> {
      List<NwDictInfoVo> nwDictInfoVos = nwDictInfoMapper.selectByDictId(nwDictVo.getDictId());
      nwDictVo.setInfoList(nwDictInfoVos);
    });

    List<NwNodeVo> nwNodeVos = nwWorkOrderNodeMapper.selectByTemplateId(templateId);
    nwWorkOrderTemplateVo.setNwNodeVoList(nwNodeVos);
    nwWorkOrderTemplateVo.setDictVoList(nwDictVos);
    nwWorkOrderTemplateVo.setTriggerList(triggerList);
    return nwWorkOrderTemplateVo;
  }

  @Override
  public List<NwWorkOrderTemplateVo> list(Integer hotelId) {
    ModUser userById = modUserCommonService.getUserById(BaseThreadLocalHelper.getUser().getId());
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryByUcCompanyId(userById.getUcCompanyId());
    List<NwWorkOrderTemplateVo> list = new ArrayList<>();
    List<String> userRights = modUserCommonService
        .getUserRights(BaseThreadLocalHelper.getUser().getId(),
            modHotelInfo.getHotelId(),
            UserRightsTypeEnum.ALL);
    boolean flag = userRights.contains("work_order_template_list");
    if (flag){
      list = nwWorkOrderTemplateMapper.list(null);
    }else {
      list = nwWorkOrderTemplateMapper.list(hotelId);
    }

    return list;
  }
}
