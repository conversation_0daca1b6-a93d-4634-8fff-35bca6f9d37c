package com.shands.mod.workorder.service.impl;

import com.shands.mod.dao.mapper.workorder.NwOpterDetailMapper;
import com.shands.mod.dao.model.workorder.po.NwOpterDetail;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.workorder.service.INwOpterDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;

@Service("nwOpterDetailService")
public class NwOpterDetailImpl implements INwOpterDetailService {

  private final NwOpterDetailMapper nwOpterDetailMapper;

  @Autowired
  public NwOpterDetailImpl(NwOpterDetailMapper nwOpterDetailMapper) {
    this.nwOpterDetailMapper = nwOpterDetailMapper;
  }

  @Override
  public void saveLog(String table, Integer tableId, String beforeData, String afterData) {
    NwOpterDetail nwOpterDetail = NwOpterDetail.builder()
        .createTime(new Date())
        .opterUser(BaseThreadLocalHelper.getUser().getId())
        .tableType(table)
        .tableId(tableId)
        .beforeData(beforeData)
        .afterData(afterData).build();
    nwOpterDetailMapper.insert(nwOpterDetail);
  }
}
