package com.shands.mod.gateway.util;

import com.shands.mod.gateway.filter.BaseConstants;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;

/**
 * 工具类
 *
 * <AUTHOR>
 */
public class Tools {

  private static final Logger LOG = LoggerFactory.getLogger(Tools.class);
  private static String HOSTNAME = null;


  /**
   * 获取请求头信息Map
   *
   * @param headers
   * @return
   */
  public static Map<String, String> getHeaderMap(HttpHeaders headers) {
    Map<String, String> result = new HashMap<>();
    if (headers != null && !headers.isEmpty()) {
      String val;
      Iterator<Entry<String, List<String>>> it = headers.entrySet().iterator();
      while (it.hasNext()) {
        Entry<String, List<String>> en = it.next();
        val = StringUtils.EMPTY;
        if (en.getValue() != null && !en.getValue().isEmpty()) {
          val = StringUtils.join(en.getValue(), ',');
        }
        result.put(en.getKey(), val);
      }
    }
    return result;
  }

  /**
   * key构建，字符串之间用“:”分隔
   *
   * @param params
   * @return
   */
  public static String buildKey(String... params) {
    String result = org.apache.commons.lang.StringUtils.EMPTY;
    if (params != null && params.length > 0) {
      result = org.apache.commons.lang.StringUtils.join(params, BaseConstants.CACHE_KEY_SEPARATOR);
    }
    return result;
  }


  /**
   * 获取请求token
   *
   * @return
   */
  public static String getToken(ServerHttpRequest request) {

    Map<String, String> headers = getHeaderMap(request.getHeaders());
    // 2. header
    String result = headers.get(BaseConstants.HEADER_TOKEN);
    if (StringUtils.isBlank(result)) {
      result = headers.get(BaseConstants.HEADER_UC_TOKEN);
    }
    return result;
  }

  /**
   * 获取请求token
   *
   * @return
   */
  public static String getHotelCode(ServerHttpRequest request) {
    Map<String, String> headers = getHeaderMap(request.getHeaders());
    String hotelCode = headers.get(BaseConstants.HEADER_HOTEL_CODE);
    if (StringUtils.isBlank(hotelCode)) {
      hotelCode = headers.get(BaseConstants.HEADER_HOTEL_CODE_LOWER);
    }
    return hotelCode;
  }


  /**
   * 获取querystring
   *
   * @param request
   * @return
   */
  public static String getQuerystring(ServerHttpRequest request) {
    StringBuffer result = new StringBuffer();
    if (request.getQueryParams() != null && !request.getQueryParams().isEmpty()) {
      for (String key : request.getQueryParams().keySet()) {
        if (result.length() > 0) {
          result.append("&");
        }
        result.append(key + "=" + request.getQueryParams().get(key));
      }

    }
    return result.toString();
  }


  private static final String START_DIGIT_SPAN_ID = "1";
  private static final String SPAN_ID_SEPARATOR = ".";

  /**
   * 请求头获取ip用
   */
  public static final String[] HEADER_IPS = new String[]{"X-Original-Forwarded-For",
      "X-Forwarded-For",
      "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "X-Real-IP"};


  /**
   * 获取头信息
   *
   * @param headers
   * @param key
   * @return
   */
  public static String getHeader(Map<String, String> headers, String key) {
    String result = StringUtils.EMPTY;
    if (headers != null && org.apache.commons.lang.StringUtils.isNotBlank(key)
        && headers.containsKey(key)) {
      //正常key
      result = headers.get(key);
      if (org.apache.commons.lang.StringUtils.isNotBlank(result)) {
        return result;
      }

      //小写key
      result = headers.get(key.toLowerCase());
      if (org.apache.commons.lang.StringUtils.isNotBlank(result)) {
        return result;
      }

      //大写key
      result = headers.get(key.toUpperCase());
      if (org.apache.commons.lang.StringUtils.isNotBlank(result)) {
        return result;
      }
    }
    return result;
  }

  /**
   * 获取请求token
   *
   * @return
   */
  public static String getDevice(ServerHttpRequest request) {

    Map<String, String> headers = getHeaderMap(request.getHeaders());
    // 2. header
    String result = headers.get(BaseConstants.HEADER_DEVICE);
    if (StringUtils.isBlank(result)) {
      result = headers.get(BaseConstants.HEADER_DEVICE_NUMBER);
    }
    return result;
  }


  /**
   * 获取异常信息
   *
   * @param throwable 异常类
   * @param maxLines  最大行数，0全部
   * @return
   */
  public static String getStackTrace(Throwable throwable, int maxLines) {
    String result = ExceptionUtils.getStackTrace(throwable);
    if (maxLines > 0) {
      String[] lines = result.split(System.lineSeparator());
      StringBuilder sb = new StringBuilder();
      for (int i = 0; i < Math.min(maxLines, lines.length); i++) {
        sb.append(lines[i]).append(System.lineSeparator());
      }
      result = sb.toString();
    }
    return result;
  }
}
