package com.shands.mod.gateway.filter;

import java.nio.charset.Charset;

/**
 * 系统公共字段
 *
 * <AUTHOR>
 */
public class BaseConstants {
  public static final String DEFAULT_CHARSET = "UTF-8";
  public static final Charset CHARSET_UTF8 = Charset.forName(DEFAULT_CHARSET);
  public static final String FORMAT_DATE = "yyyy-MM-dd";
  public static final String FORMAT_DATE2 = "yyyy/MM/dd";
  public static final String FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";
  public static final String FORMAT_SHORT_DATE = "yyyyMMdd";
  public static final String FORMAT_MONTH = "yyyy-MM";
  public static final String FORMAT_SHORT_TIME = "HH:mm:ss";
  public static final String HEADER_TOKEN = "mod-token";
  public static final String HEADER_DEVICE = "mod-device";
  public static final String HEADER_DEVICE_NUMBER = "DeviceNumber";
  public static final String HEADER_HOTEL_CODE = "X-HOTEL-CODE";
  public static final String HEADER_HOTEL_CODE_LOWER = "x-hotel-code";
  public static final String HEADER_UC_TOKEN = "uc-token";
  public static final String CACHE_KEY_SEPARATOR = ":";
  public static final String[] DEFAULT_ARRAY_DATE_FORMAT =
      new String[] {FORMAT_DATE, FORMAT_SHORT_DATE, FORMAT_TIME, "yyyyMMddHHmmss"};

  public static final String HEADER_CONTENT_TYPE = "Content-Type";
  public static final String CONTENT_TYPE_JSON = "application/json;charset=utf-8";

  public static final String CACHE_PMS_TOKEN = "mod3:user:pms_token";
  public static final String CACHE_PMS_TOKEN_LOCK = "mod3:user:pms_token:lock";

  public static final String LINE_SEPARATOR = System.getProperty("line.separator");
  // DB常量数据
  public static final int DATA_VERSION = 1;
  public static final int DATA_DELETED = 1;
  public static final int DATA_UNDELETED = 0;
  public static final int DATA_ACTIVATED = 1;
  // 平台企业ID
  public static final int PLATFORM_COMPANY = 1;
  // 系统用户ID
  public static final int SYSTEM_USER = 1;
  //集团id
  public static final int GROUPID = 2;
  // 缓存
  public static final String CACHE_PREFIX = "mod3";
  public static final String CACHE_USER = "mod3:user";
  public static final String CACHE_USER_PMS_HOTEL = "mod3:user:pms:hotel";
  public static final String CACHE_USER_HOTELS = "mod3:user:hotels";
  public static final String CACHE_USER_DATAS = "mod3:user:datas";
  public static final String CACHE_USER_INFO = "mod3:user:info";
  public static final String CACHE_USER_TOKEN = "mod3:user:token";
  public static final String CACHE_REGION = "mod3:region";
  public static final String CACHE_APPLICATION = "mod3:application";
  public static final String CACHE_DICT = "mod3:dict";
  public static final String CACHE_ROLE = "mod3:role";
  public static final String CACHE_PERMISSION = "mod3:permission";
  public static final String CACHE_LOGIN = "mod3:login";
  public static final String UPDATE_PASSWORD="mod3:updatePasswordCode";
  /** 手机号注册缓存验证码 */
  public static final String CACHE_REGISTER = "mod3:register";
  /** 重置sks会员密码缓存 */
  public static final String CACHE_RESETPWD = "mod3:reset_pwd";
  public static final String CACHE_LAST_COMPANY_MOBILE = "mod3:last_company:mobile";
  public static final String CACHE_LAST_COMPANY_MANAGE = "mod3:last_company:manage";
  public static final String CACHE_SEQUENCE_GOODS_TYPE = "mod3:seq_goods_type";
  public static final String CACHE_SEQUENCE_GOODS_STOCK = "mod3:seq_goods_stock";
  public static final String CACHE_CUSTOMER = "mod3:customer";
  //华客云token缓存
  public static final String CACHE_VOCUST_TOKEN = "mod3:vocust_cloud:token";
  //绿云房态会话缓存
  public static final String CACHE_CRS_SESSIONID = "mod3:crs:session_id";
  //绿云房态会话缓存
  public static final String CACHE_CRS_COMPANYINFO = "mod3:crs:companyinfo";
  //房型缓存
  public static final String CACHE_CRS_ROOMTYPES = "mod3:crs:roomtypes";
  //楼层楼宇缓存
  public static final String CACHE_CRS_FLOOR = "mod3:crs:floor";
  //缓存房态信息
  public static final String CACHE_CRS_ROOMSTA = "mod3:crs:roomsta";
  //新酒店信息缓存
  public static final String CACHE_HOTELINFO = "mod3:hotelinfo";
  //官网
  public static final String CACHE_GW_TOKEN = "mod3:gwtoken";
  //app消息推送缓存uuid
  public static final String CACHE_YM_MESSAGE = "mod3:ym:message:uuid";
  //pc端做房分配清扫人员缓存
  public static final String CACHE_CLEAN_USER = "mod3:clean:user";

  public static final String COMMENT_HOTEL = "mod3:comment:";
  // 字典-行业
  public static final String DICT_TRADE = "TRADE";
  // 字典-服务类型
  public static final String DICT_TOS = "TOS";
  // 字典-派单方式
  public static final String DICT_DISPATCH = "DISPATCH";
  // 字典-系统角色
  public static final String DICT_ROLE = "ROLE";
  public static final String DATA_ADMIN = "admin";
  public static final String IT_MANAGER = "It_maintainer";
  public static final String DATA_ORDER_MANAGER = "order_manager";
  public static final String DATA_PATROL_MANAGER = "patrol_manager";
  public static final String DATA_COUNTRY_CHINESE = "CN";
  public static final String ARRAY_SEPARATOR = ",";
  public static final String WELL_NUMBER = "#";
  public static final String SLASH_SEPARATOR = "/";
  public static final String MESSAGE_TYPE_NEWS = "News";
  public static final String MESSAGE_TYPE_NOTIFICATION = "Notification";
  public static final String MESSAGE_TYPE_LOGIN_CODE = "LoginCode";
  public static final String MESSAGE_TYPE_WELCOME = "Welcome";
  public static final String MESSAGE_TYPE_CHECKIN = "CheckIn";
  public static final String MESSAGE_SUBTYPE_WORK_ORDER = "WorkOrder";
  public static final String MESSAGE_SUBTYPE_PATROL = "Patrol";
  public static final String MESSAGE_SUBTYPE_ACTIVITY = "Activity";
  public static final String SEND_TYPE_APP = "APP";
  public static final String SEND_TYPE_SMS = "SMS";
  // 消息接收类型：客人（入住人）
  public static final int RECEIVE_TYPE_CUSTOMER = 1;
  // 消息接收类型：工作人员
  public static final int RECEIVE_TYPE_USER = 0;
  // 未派单
  public static final int NOT_ORDER = 1;
  // 待接单
  public static final int WAIT_ORDER = 2;
  // 已接单
  public static final int HAVE_ORDER = 11;
  // 处理中
  public static final int HAND_ORDER = 12;
  // 挂起
  public static final int UP_ORDER = 13;
  // 完毕
  public static final int OVER_ORDER = 21;
  // 拒单
  public static final int REFUSE_ORDER = 22;
  // 接口消息成功代码
  public static final int RESULT_CODE_SUCCESS = 0;
  // 消息读取状态
  public static final int DATA_MESSAGE_READ = 2;
  /** 外部房间加密key */
  public static final String ROOM_AES_KEY = "company_room_key";

  public static final String BEAN_REDIS_TEMPLATE = "redisTemplate";
  // 分页配置
  public static final int PAGE_SIZE = 20;
  public static final int PAGET_FRIST = 1;
  // customer
  public static final String CUSTOMER_USER_TYPE_KAIYUAN = "kaiyuan";

  /** 支付单号标记 */
  public static final String ORDER_PAY_TYPE_ACTIVITY = "fw_activity_order";

  /** 支付单号标记 */
  public static final String ORDER_PAY_TYPE_CUSTOMER = "customer_order";

  /** 餐桌过期前缀 */
  public static final String EXPIRE_EAT_TABLE = "mod3:eat:table:";

  /** 开始时间的锁 */
  public static final String EXPIRE_EAT_TABLE_START = EXPIRE_EAT_TABLE + "START:";
  /** 结束时间的锁 */
  public static final String EXPIRE_EAT_TABLE_END = EXPIRE_EAT_TABLE + "END:";

  /** 餐桌过期的hash key */
  public static final String EXPIRE_EAT_TABLE_HASH = "mod3:eat:table_hash";

  /**
   * 用户点击uv key
   */
  public static final String CLICK_UV = "mod3:clickUv:";

  /**
   * 在redis存储用户订单
   */
  public static final String OFFICIAL_ORDER = "mod3:officialOrder:";

  /**
   * 在redis中存储天气 按照酒店存储 存储15天天气
   */
  public static final String WEATHER_FIFTEN_DAYS = "mod3:weather:fiftenDays";

  /**
   * 在redis中存储实时天气
   */
  public static final String  CONDITION = "mod3:weather:condition";

  /**
   * 在redis中存储 已经和wetsocket连接的用户
   */
  public static final String wetsocketUserList="mod3:wetsocket:";

  public static final String workOrderSendList = "mod3:workOrder:id:";

  /**
   * pos系统获得菜信息，存入redis
   */
  public static final String posFood = "mod3:pos:food:";

  /** 菜项数据报表项常量  010：食品  020：酒水*/
  public static final String POSPLU_010 = "010";
  public static final String POSPLU_020 = "020";

  public static final String NUMBER_PLATE = "mod3:numberPlate:companyId:";
}