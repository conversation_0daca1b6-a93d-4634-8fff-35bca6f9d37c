package com.shands.mod.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.shands.mod.gateway.config.IgnoreSignWhiteProperties;
import com.shands.mod.gateway.util.Tools;
import com.shands.mod.gateway.vo.ResultVO;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * pms token过滤器
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
@Slf4j
public class TokenFilter implements GlobalFilter {


  private static final String PMS_ROUTE_ID = "mod-pms";

  @Autowired
  private RedisTemplate redisTemplate;

  @Autowired
  private IgnoreSignWhiteProperties ignoreWhite;


  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    Route route = (Route) exchange.getAttributes().get(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
    String id = route.getId();
    if (PMS_ROUTE_ID.equals(id)) {
      return chain.filter(exchange);
    }
    String url = exchange.getRequest().getURI().getPath();
    //token白名单, 属于白名单 不校验
    if (!needValidateToken(url)) {
      return chain.filter(exchange);
    }

    ServerHttpRequest request = exchange.getRequest();
    String token = Tools.getToken(request);
    if (StringUtils.isEmpty(token)) {
      String device = Tools.getDevice(request);
      log.warn("请求未带token device:{} url:{} ", device, exchange.getRequest().getURI().getPath());
      return error(exchange, ResultVO.failed(3,"登录超时，请重新登录"));
    }

    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    Object obj = this.redisTemplate.opsForValue().get(key);
    if (null == obj) {
      log.warn("token缓存查询为空 key {}", key);
      return error(exchange, ResultVO.failed(3,"登录超时，请重新登录"));
    }

    return chain.filter(exchange);
  }

  /**
   * 返回错误信息
   *
   * @param exchange
   * @param result
   * @return
   */
  private Mono<Void> error(ServerWebExchange exchange, ResultVO<Object> result) {
    ServerHttpResponse response = exchange.getResponse();
    byte[] bits = JSON.toJSONString(result).getBytes(StandardCharsets.UTF_8);
    DataBuffer buffer = response.bufferFactory().wrap(bits);
    response.setStatusCode(HttpStatus.OK);
    response.getHeaders().add(BaseConstants.HEADER_CONTENT_TYPE, BaseConstants.CONTENT_TYPE_JSON);
    return response.writeWith(Mono.just(buffer));
  }

  /**
   * 是否校验token
   *
   * @return true : 需要;  false : 不需要
   */
  public boolean needValidateToken(String url) {
    return !stringContains(url, ignoreWhite.getTokenWhites());
  }

  public boolean stringContains(String str, List<String> list) {
    for (String s : list) {
      if (str.contains(s)) {
        return true;
      }
    }
    return false;
  }


}