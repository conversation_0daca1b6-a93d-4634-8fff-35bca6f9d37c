package com.shands.mod.pay.service.channel;

import com.shands.mod.dao.model.v0701.pojo.PayWxConfig;

/**
 * @ClassName ChannelConfigService
 * @Description 渠道信息管理
 * <AUTHOR>
 * @Date 2021/4/6 17:02
 * @Version 1.0
 */
public interface ChannelConfigService {

  /**
   * <AUTHOR>
   * @Description 根据渠道标识查询渠道支付配置信息
   * @Date 2021/4/6 15:30
   * @Param channel: 渠道标识
   * @return: PayWxConfig 配置信息对象
   **/
  public PayWxConfig qurPayWxConfig(String channel);
}
