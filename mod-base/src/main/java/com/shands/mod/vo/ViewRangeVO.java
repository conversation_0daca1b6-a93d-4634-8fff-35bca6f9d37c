package com.shands.mod.vo;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * 角色视图范围<br>
 * 巡检管理：3所有；2本人参与<br>
 * 工单管理：3全部；2本人所在部门；1本人创建、处理、抄送的工单<br>
 * 统计报表-巡检报表：3全部；2部门及下级部门；1所在部门<br>
 * 统计报表-工单报表：3全部；2本人参与
 *
 * <AUTHOR>
 */
public class ViewRangeVO implements Serializable {
  private static final long serialVersionUID = -6385244903990783133L;
  /** 工单管理：3全部；2本人所在部门；1本人创建、处理、抄送的工单 */
  private Integer orderManage;
  /** 巡检管理：3所有；2本人参与； */
  private Integer patrolManage;
  /** 统计报表-工单报表：3全部；2部门及下级部门；1所在部门 */
  private Integer orderReport;
  /** 统计报表-巡检报表：3全部；2本人参与； */
  private Integer patrolReport;

  public ViewRangeVO() {
    this(1, 2, 1, 2);
  }

  public ViewRangeVO(
      Integer orderManage, Integer patrolManage, Integer orderReport, Integer patrolReport) {
    this.orderManage = orderManage;
    this.patrolManage = patrolManage;
    this.orderReport = orderReport;
    this.patrolReport = patrolReport;
  }

  /**
   * 获取系统预设角色视图范围
   *
   * @param type 3：工单中心；2：工单主管、巡检主管；1：工单服务员、巡检服务员；
   * @return
   */
  public static ViewRangeVO createSystemViewRange(int type) {
    ViewRangeVO result = new ViewRangeVO();
    int patrol = type == 3 ? 3 : 2;
    result.setOrderManage(type);
    result.setPatrolManage(patrol);
    result.setOrderReport(type);
    result.setPatrolReport(patrol);
    return result;
  }

  /**
   * 从json创建视图范围，默认都为1
   *
   * @param json json配置
   * @return
   */
  public static ViewRangeVO toViewRange(String json) {
    ViewRangeVO result = null;

    if (StringUtils.isNotBlank(json)) {
      result = JSON.parseObject(json, ViewRangeVO.class);
    }

    if (result == null) {
      result = new ViewRangeVO();
    }
    return result;
  }

  public Integer getOrderManage() {
    return orderManage;
  }

  public void setOrderManage(Integer orderManage) {
    this.orderManage = orderManage;
  }

  public Integer getPatrolManage() {
    return patrolManage;
  }

  public void setPatrolManage(Integer patrolManage) {
    this.patrolManage = patrolManage;
  }

  public Integer getOrderReport() {
    return orderReport;
  }

  public void setOrderReport(Integer orderReport) {
    this.orderReport = orderReport;
  }

  public Integer getPatrolReport() {
    return patrolReport;
  }

  public void setPatrolReport(Integer patrolReport) {
    this.patrolReport = patrolReport;
  }

  @Override
  public String toString() {
    return "ViewRangeVO{"
        + "orderManage="
        + orderManage
        + ", patrolManage="
        + patrolManage
        + ", orderReport="
        + orderReport
        + ", patrolReport="
        + patrolReport
        + '}';
  }

  /**
   * 合并视图范围（取视图最大值）
   *
   * @param bean 需要合并的视图配置
   */
  public void merge(ViewRangeVO bean) {
    if (bean != null) {
      if (bean.getOrderManage() > this.getOrderManage()) {
        this.setOrderManage(bean.getOrderManage());
      }
      if (bean.getOrderReport() > this.getOrderReport()) {
        this.setOrderReport(bean.getOrderReport());
      }
      if (bean.getPatrolManage() > this.getPatrolManage()) {
        this.setPatrolManage(bean.getPatrolManage());
      }
      if (bean.getPatrolReport() > this.getPatrolReport()) {
        this.setPatrolReport(bean.getPatrolReport());
      }
    }
  }
}
