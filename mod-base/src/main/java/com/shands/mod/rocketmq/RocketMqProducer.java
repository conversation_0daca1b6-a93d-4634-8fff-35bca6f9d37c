package com.shands.mod.rocketmq;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * rcmq消息生产者
 * <AUTHOR>
 * @date 2023/9/5
 **/
@Slf4j
@Component
public class RocketMqProducer {

  @Autowired
  private RocketMQTemplate rocketMqTemplate;

  /**
   * 同步投递消息
   * @param topic 主题
   * @param msgBody 消息体
   * @return 发送结果
   */
  public SendResult syncSendMessage(String topic, String msgBody) {
    log.info("同步投递rcmq消息: 主题[{}]->消息体[{}]", topic, msgBody);
    Message<String> message = MessageBuilder.withPayload(msgBody).build();
    SendResult sendResult = rocketMqTemplate.syncSend(topic,message);
    log.info("同步投递rcmq消息响应结果：{}", sendResult);
    return sendResult;

  }

  /**
   * 同步投递延迟消息
   * @param topic 主题
   * @param msgBody 消息体
   * @param delayTime 延迟时间（ms）
   * @return 发送结果
   */
  public SendResult syncSendDelayMessage(String topic, String msgBody, long delayTime) {
    long futureTime = System.currentTimeMillis() + delayTime;
    log.info("同步投递rcmq延迟消息: 主题[{}]->消息体[{}]-延迟时间戳[{}]", topic, msgBody,futureTime);
    Message<String> message = MessageBuilder.withPayload(msgBody).build();
    SendResult sendResult = rocketMqTemplate.syncSendDeliverTimeMills(topic, message, futureTime);
    log.info("同步投递rcmq延迟消息响应结果：{}", sendResult);
    return sendResult;
  }
}
