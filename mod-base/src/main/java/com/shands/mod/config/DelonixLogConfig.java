package com.shands.mod.config;

import com.betterwood.log.filter.TraceFilter;
import com.betterwood.log.util.LogConstants;
import com.betterwood.log.util.LogUtils;
import com.betterwood.log.interceptor.FeignTraceInterceptor;
import feign.RequestInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.springframework.core.annotation.Order;

/**
 * 日志配置
 * 配置参数：
 * 环境: app.log.env=dev
 * 记录请求地址：app.log.ip=false
 * 记录请求报文：app.log.request=true
 * 记录响应报文：app.log.response=true
 * 记录访问日志：app.log.response=true
 * <AUTHOR>
 * @date 2022-9-20
 */
public class DelonixLogConfig {
  private static final Logger LOG = LoggerFactory.getLogger(DelonixLogConfig.class);

  @Value("${spring.application.name}")
  private String logApp;
  @Value("${app.log.env:dev}")
  private String logEnv;
  @Value("${app.log.ip:true}")
  private String logIp;
  @Value("${app.log.request:true}")
  private String logRequest;
  @Value("${app.log.response:true}")
  private String logResponse;
  @Value("${app.log.access:true}")
  private String logAccess;

  @Order(Ordered.LOWEST_PRECEDENCE)
  @Bean
  public RequestInterceptor requestInterceptor() {
    return new FeignTraceInterceptor();
  }

  @Bean
  public FilterRegistrationBean<TraceFilter> logFilterFilterRegistrationBean() {
    FilterRegistrationBean<TraceFilter> bean = new FilterRegistrationBean();
    bean.setFilter(new TraceFilter());
    bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
    bean.setUrlPatterns(Arrays.asList("/*"));
    Map<String, String> params = new HashMap(6);
    params.put(LogConstants.CONFIG_LOG_IP, this.logIp);
    params.put(LogConstants.CONFIG_LOG_REQUEST, this.logRequest);
    params.put(LogConstants.CONFIG_LOG_RESPONSE, this.logResponse);
    params.put(LogConstants.CONFIG_LOG_ACCESS, this.logAccess);
    params.put(LogConstants.CONFIG_CLASS, Boolean.TRUE.toString());
    params.put(LogConstants.CONFIG_FUNCTION, Boolean.TRUE.toString());
    bean.setInitParameters(params);
    LogConstants.LOG_APP = this.logApp;
    LogConstants.LOG_ENV = this.logEnv;
    LogConstants.LOG_LOCAL = LogUtils.getHostname();
    LOG.info("[BDW-LOG] Initialization completed! app={}, env={}, host={}, log(ip={}, request={}, response={}, access={})",
        new Object[]{this.logApp, this.logEnv, LogConstants.LOG_LOCAL, this.logIp, this.logRequest, this.logResponse, this.logAccess});
    return bean;
  }
}