package com.shands.mod.config;

import feign.Request;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/6/17
 */
@Configuration
public class FeignConfigure {

  /** 链接超时时间 */
  private static final int CONNECT_TIME_OUT_MILLIS = 30000;
  /** 返回超时时间 */
  private static final int READ_TIMEOUT_MILLIS = 30000;

  @Bean
  public Request.Options options() {
    return new Request.Options(CONNECT_TIME_OUT_MILLIS, READ_TIMEOUT_MILLIS);
  }

  @Bean
  public Retryer feignRetryer() {
    return new Retryer.Default();
  }
}
