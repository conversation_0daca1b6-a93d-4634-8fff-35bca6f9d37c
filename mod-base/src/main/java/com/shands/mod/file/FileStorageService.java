package com.shands.mod.file;

import com.shands.uc.base.exception.NormalException;
import java.io.File;
import java.io.InputStream;

public interface FileStorageService {

  InputStream getInputStream(String key) throws NormalException;

  void getObjectToFile(String key, File file) throws NormalException ;

  void putObject(String key, File file) throws NormalException ;

  String putInputStreamMetadata(String key, InputStream inputStream) throws Exception;

  String putInputStream(String key, InputStream inputStream) throws Exception;

}
