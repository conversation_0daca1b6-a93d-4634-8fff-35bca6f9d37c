package com.shands.mod.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/** Spring容器持有者 */
@Component
public class SpringContextHolder implements ApplicationContextAware {
  /** 用静态变量保存ApplicationContext,可在任意代码中取出ApplicaitonContext. */
  private static ApplicationContext CONTEXT;

  public static ApplicationContext getApplicationContext() {
    return CONTEXT;
  }

  /** 实现ApplicationContextAware接口的context注入函数, 将其存入静态变量. */
  @Override
  public void setApplicationContext(ApplicationContext context) throws BeansException {
    CONTEXT = context;
  }

  /**
   * 通过ID获取Bean
   *
   * @param name
   * @return
   */
  public static <T> T getBean(String name) {
    return (T) CONTEXT.getBean(name);
  }

  /**
   * 通过接口类型获取Bean
   *
   * @param clazz
   * @return
   */
  public static <T> T getBean(Class<T> clazz) {
    return CONTEXT.getBean(clazz);
  }
}
