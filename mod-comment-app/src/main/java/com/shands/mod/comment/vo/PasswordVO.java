package com.shands.mod.comment.vo;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;

public class PasswordVO implements Serializable {
  private static final long serialVersionUID = -1088473502498955253L;

  @NotBlank(message = "原密码不能为空")
  private String password;

  @NotBlank(message = "新密码不能为空")
  private String newPassword;

  @Override
  public String toString() {
    return "PasswordVO{"
        + "password='"
        + password
        + '\''
        + ", newPassword='"
        + newPassword
        + '\''
        + '}';
  }

  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public String getNewPassword() {
    return newPassword;
  }

  public void setNewPassword(String newPassword) {
    this.newPassword = newPassword;
  }
}
