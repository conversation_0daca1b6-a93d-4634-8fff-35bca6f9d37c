package com.shands.mod.comment.service;

import com.shands.mod.dao.model.req.comment.CommentReplyAddReq;
import com.shands.mod.dao.model.req.comment.CommentReplyReq;
import com.shands.mod.dao.model.wp.PageResult;

/**
 *
 */
public interface IModCommentReplyService {

  PageResult bdwCommentList(CommentReplyReq req) throws Exception;

  Integer notReply(String hotelCode);

  boolean reply(CommentReplyAddReq req);
}
