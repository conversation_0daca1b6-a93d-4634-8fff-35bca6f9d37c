package com.shands.mod.comment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/4
 * @desc 调整德胧生态返回Vo
*/
@Data
@ApiModel(value = "调整德胧生态返回Vo")
public class RectShandsVO implements Serializable {

  @ApiModelProperty(value = "集团编码")
  private Integer groupId;

  @ApiModelProperty(value = "酒店编码")
  private Integer companyId;

  @ApiModelProperty(value = "酒店名称")
  private String hotelName;

  @ApiModelProperty(value = "用户名称")
  private String userName;

  @ApiModelProperty(value = "用户手机号")
  private String userPhone;

  @ApiModelProperty(value = "入住时间")
  private String inTime;

  @ApiModelProperty(value = "离店时间")
  private String outTime;

  @ApiModelProperty(value = "房间号")
  private String roomNum;

  @ApiModelProperty(value = "用户登录信息")
  private CustomerInfoVO customerInfoVO;

  @ApiModelProperty(value = "酒店编码")
  private String hotelCode;

}
