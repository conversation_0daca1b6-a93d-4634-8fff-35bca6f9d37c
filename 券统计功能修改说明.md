# 券统计功能修改说明

## 🎯 修改概述
1. **券统计方法增强**：`queryVoucherDeptData` 方法已更新为处理所有券类型，并从新数据源 `ads_power_dl_eco_app_employee_perf_d` 获取发放和核销数据。
2. **百达卡统计方法增强**：`queryBdwCardDeptData` 方法已更新为处理所有百达卡类型，并从新数据源 `ads_power_dl_eco_app_employee_perf_d` 获取售卡和奖金数据。

## 📁 修改的文件

### 1. AdsPowerDlEcoAppEmployeePerfDMapper.java
**路径**: `mod-dao/src/main/java/com/delonix/bi/dao/mapper/AdsPowerDlEcoAppEmployeePerfDMapper.java`

**修改内容**:
- 新增 `queryVoucherDataByEmployee` 方法：查询各类券统计数据
- 新增 `queryBdwCardDataByEmployee` 方法：查询各类百达卡统计数据

### 2. AdsPowerDlEcoAppEmployeePerfDMapper.xml
**路径**: `mod-dao/src/main/java/com/delonix/bi/dao/mapper/AdsPowerDlEcoAppEmployeePerfDMapper.xml`

**修改内容**:
- 新增 `queryVoucherDataByEmployee` SQL查询：支持不同券类型的查询
- 新增 `queryBdwCardDataByEmployee` SQL查询：支持不同百达卡类型的查询
- 修复 `sumPerformanceByHotel` 中缺失的逗号语法错误

### 3. AdsHotelPerformanceHDTO.java
**路径**: `mod-dao/src/main/java/com/shands/mod/dao/model/res/AdsHotelPerformanceHDTO.java`

**新增字段**:
- `hotelCode`：酒店代码
- `employeeId`：员工通宝ID
- `voucherSendCount`：券发放数量（Long类型）
- `voucherUseCount`：券核销数量（Long类型）

**使用的现有字段**:
- `bdCardCnt`：百达卡售卡数量（Long类型）
- `bonusAmt`：百达卡预计奖金（Double类型）

### 4. VoucherStatisticServiceImpl.java
**路径**: `mod-main-app/src/main/java/com/shands/mod/main/service/statistics/impl/VoucherStatisticServiceImpl.java`

**修改内容**:
- 完全重写 `queryVoucherDeptData` 方法
- 移除旧数据源依赖
- 新增私有方法 `isVoucherType` 用于券类型判断
- 支持所有券类型：`HOTEL_VOUCHER`、`APP_DISCOUNT_VOUCHER`、`PMS_DISCOUNT_VOUCHER`、`BREAKFAST_VOUCHER`、`UPGRADE_ROOM_VOUCHER`、`DELAY_CHECKOUT_VOUCHER`
- 添加过滤条件：排除发放和核销数都为0的记录

### 5. BdwCardStatisticServiceImpl.java
**路径**: `mod-main-app/src/main/java/com/shands/mod/main/service/statistics/impl/BdwCardStatisticServiceImpl.java`

**修改内容**:
- 完全重写 `queryBdwCardDeptData` 方法
- 移除旧数据源依赖 `AdsTradeConsumerMemberMapper`
- 注入新数据源 `AdsPowerDlEcoAppEmployeePerfDMapper`
- 新增私有方法 `isBdwCardType` 用于百达卡类型判断
- 支持所有百达卡类型：`BETTERWOOD_CARD_TOTAL`、`QIHANG_CARD`、`MANLU_CARD`、`KAITUO_CARD`
- 添加过滤条件：排除售卡数和奖金都为0的记录

## 🎫 券类型和数据库字段映射

### 券类型对应关系
| 券类型 | 发放字段 | 核销字段 |
|--------|----------|----------|
| HOTEL_VOUCHER | magic_hotel_send_n | magic_hotel_use_n |
| APP_DISCOUNT_VOUCHER | magic_app_send_n | magic_app_use_n |
| PMS_DISCOUNT_VOUCHER | magic_pms_send_n | magic_pms_use_n |
| BREAKFAST_VOUCHER | magic_breakfast_send_n | magic_breakfast_use_n |
| UPGRADE_ROOM_VOUCHER | magic_upgrade_send_n | magic_upgrade_use_n |
| DELAY_CHECKOUT_VOUCHER | magic_delay_send_n | magic_delay_use_n |

### 百达卡类型对应关系
| 卡类型 | 售卡数字段 | 奖金字段 |
|--------|------------|----------|
| QIHANG_CARD | mem_card_one_n | mem_card_one_bonus_amt |
| MANLU_CARD | mem_card_second_n | mem_card_second_bonus_amt |
| KAITUO_CARD | mem_card_third_n | mem_card_third_bonus_amt |
| BETTERWOOD_CARD_TOTAL | 所有卡类型汇总 | 所有奖金汇总 |

**注意**：百达卡查询结果映射到 `AdsHotelPerformanceHDTO` 的 `bdCardCnt`（售卡数）和 `bonusAmt`（奖金）字段。

## 📊 返回数据格式
### 券统计数据
- 第一个值（firstValue）：发放数量
- 第二个值（secondValue）：核销数量
- 按部门分组，按发放数量倒序排列

### 百达卡统计数据
- 第一个值（firstValue）：售卡数量
- 第二个值（secondValue）：预计奖金
- 按部门分组，按售卡数量倒序排列

## 🗄️ 数据源表结构
**表名**: `ads_power_dl_eco_app_employee_perf_d`

**关键字段**:
- `hotel_code`：酒店代码
- `employee_nc_id`：员工通宝ID
- `employee_name`：员工姓名
- `employee_dept`：员工部门
- `biz_date`：业务日期
- **券相关字段**：`magic_xxx_send_n`（发放）、`magic_xxx_use_n`（核销）
- **百达卡相关字段**：`mem_card_xxx_n`（售卡数）、`mem_card_xxx_bonus_amt`（奖金）

## 📝 注意事项
1. 所有统计数据都是基于员工维度的聚合统计
2. 过滤掉部门名称为空的记录：`employee_dept is not null and employee_dept != ''`
3. 使用 `COALESCE` 函数处理 NULL 值，确保计算准确性
4. 支持通过 `startDate` 和 `endDate` 参数进行日期范围查询
5. 数据过滤：排除无意义的零值记录（发放/核销数为0或售卡数/奖金为0）
6. 排序规则：按部门内和部门间的主要指标降序排列
7. 百达卡数据中，奖金字段使用 `BigDecimal` 类型确保精度 