package com.shands.mod.main;

import com.shands.mod.util.AliOssUtils;
import java.io.File;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 飞书开发api测试类
 *
 * <AUTHOR>
 * @date 2023/4/4
 */

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class AliOssTest {

  @Resource
  private AliOssUtils aliOssUtil;

  private final String appId = "cli_a497ffe134bc9013";

  @Test
  public void testGetTenantAccessToken() {
    String path = "/Users/<USER>/Downloads/Feishu_Knowledge_Template_i18n_zh.xlsx";
    File file = new File(path);
    String fileUrl = aliOssUtil.upLoad(file,"common/Feishu_Knowledge_Template_i18n_zh.xlsx");
    System.out.println(fileUrl);
  }

  @Test
  public void urlTest() {
    String modelUrl = aliOssUtil.getObject("hr/model/salaryTransfer-common.xlsx").getResponse().getUri();
    modelUrl = "http://delonix-sxe.oss-cn-shenzhen.aliyuncs.com/hr/model/salaryTransfer-common.xlsx";
    modelUrl = modelUrl.replace("oss-cn-shenzhen.aliyuncs.com", "img.betterwood.com");
  }

}
