package com.shands.mod.main;

import com.shands.mod.dao.mapper.hs.HsThirdNoticeMapper;
import com.shands.mod.dao.model.hs.HsThirdNotice;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/18
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class HsThirdNoticeMapperTest {

  @Autowired
  private HsThirdNoticeMapper thirdNoticeMapper;

  @Test
  public void insertTest() {

    HsThirdNotice thirdNotice = new HsThirdNotice();
    thirdNotice.setMessageType("YM_APP");
    thirdNotice.setMessageTitle("新工单提醒");
    thirdNotice.setMessageContent("你有一个新的工单222222");
    thirdNotice.setNoticeInfo("111,222");
    thirdNotice.setThirdId("msg_id:11111");
    thirdNotice.setWorkId("11111");
    thirdNotice.setSendStatus(0);
    thirdNotice.setReceiveStatus(0);
    thirdNotice.setDeleted(0);
    thirdNotice.setCreateUser("yrc");
    thirdNotice.setCreateTime(new Date());
    thirdNotice.setUpdateUser("yrc");
    thirdNotice.setUpdateTime(new Date());
    thirdNotice.setDelayTime(-1L);
    thirdNotice.setDeviceType(1);
    int insertCount = thirdNoticeMapper.insert(thirdNotice);
    log.info("三方通知数量： " + insertCount);
  }

  public void selectByIdTest() {
    HsThirdNotice thirdNotice = thirdNoticeMapper.selectById(1);
    log.info("");
  }

  @Test
  public void updateSendStatusAndThirdIdTest() {
    thirdNoticeMapper.updateSendStatusAndThirdId(1,"msg_id:22222", 1);
  }

  @Test
  public void updateReceiveStatusTest() {

  }


}
