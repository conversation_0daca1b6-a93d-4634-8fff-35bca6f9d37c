spring:
  application:
    name: mod-main-app
  main:
    allow-bean-definition-overriding: true

  profiles:
    active: tx-dev


trace:
  filter:
    enable: true
    access: true
    skipPaths: /actuator/health, /actuator/health/readiness, /actuator/health/liveness, /userCenter/loginByUsername, /settleBill/download

---

spring:
  cloud:
    nacos:
      discovery:
        server-addr: mse-ad1e9d56-nacos-ans.mse.aliyuncs.com
        namespace: 25d76c8c-0559-4a0b-ab00-f4bd18c1f971
      config:
        server-addr: mse-ad1e9d56-nacos-ans.mse.aliyuncs.com
        namespace: 25d76c8c-0559-4a0b-ab00-f4bd18c1f971
        file-extension: yaml
        ext-config[0]:
          data-id: mod-main-app-dev.yaml
          refresh: true
  config:
    activate:
      on-profile: dev

---

spring:
  cloud:
    nacos:
      discovery:
        server-addr: **********
        namespace:
      config:
        server-addr: **********
        namespace:
        file-extension: yaml
        ext-config[0]:
          data-id: mod-main-app-test.yaml
          refresh: true
  config:
    activate:
      on-profile: test

---

spring:
  cloud:
    nacos:
      discovery:
        server-addr: 172.16.0.40
        namespace:
      config:
        server-addr: 172.16.0.40
        namespace:
        file-extension: yaml
        ext-config[0]:
          data-id: mod-main-app.yaml
          refresh: true
  config:
    activate:
      on-profile: pro

---

spring:
  cloud:
    nacos:
      discovery:
        server-addr: 10.95.16.19
        namespace:
      config:
        server-addr: 10.95.16.19
        namespace:
        file-extension: yaml
        ext-config[0]:
          data-id: mod-main-app-dev.yaml
          refresh: true
  config:
    activate:
      on-profile: tx-dev