package com.shands.mod.main.service.message.impl;

import com.shands.mod.dao.mapper.hs.HsPcMessageMapper;
import com.shands.mod.dao.mapper.hs.WorkOrderMapper;
import com.shands.mod.dao.model.hs.HsPcMessage;
import com.shands.mod.dao.model.hs.enums.WorkOrderStateEnum;
import com.shands.mod.dao.model.req.hs.consumer.WebSocketReq;
import com.shands.mod.main.service.message.IHsPcMessageService;
import com.shands.mod.main.service.message.ShandsMessageService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.util.BaseConstants;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class HsPcMessageServiceImpl implements IHsPcMessageService {

  @Resource
  HsPcMessageMapper hsPcMessageMapper;

  @Resource
  WorkOrderMapper workOrderMapper;

  @Resource
  RedisTemplate redisTemplate;

  @Autowired
  private ShandsMessageService shandsMessageService;

  @Value("${message.audio}")
  private String audio;


  @Override
  public int insertMessage(HsPcMessage hsPcMessage) {
    return hsPcMessageMapper.insertSelective(hsPcMessage);
  }

  @Override
  public void isSend(Integer companyId) {

    try{
      //查询是否有待发送的工单信息
      List<Integer> getAllIdByStatus = workOrderMapper
          .getAllIdByStatus(companyId,WorkOrderStateEnum.SENT_LIST.getCode(),ThreadLocalHelper.getUser().getId());

      boolean allId = false;
      HsPcMessage hsPcMessage = new HsPcMessage();
      hsPcMessage.setCompanyId(companyId);
      hsPcMessage.setMessageContent(audio);
      hsPcMessage.setUserId(ThreadLocalHelper.getUser().getId());
      hsPcMessage.setCreateTime(new Date());
      for (Integer id : getAllIdByStatus) {
        //说明没有发送 ，需要发送
        hsPcMessage.setWorkOrderId(Integer.valueOf(id));
        this.insertMessage(hsPcMessage);
        allId = true;
      }
      if (allId == true) {
        WebSocketReq req = new WebSocketReq();
        req.setCompanyId(companyId.toString());
        req.setMessage(audio);
        req.setUserId(ThreadLocalHelper.getUser().getId().toString());
        shandsMessageService.sendAudio(companyId,null,true,ThreadLocalHelper.getUser().getId());
      }
    }catch (Exception e){
    }
  }

  @Override
  public Boolean deleteRedisWorkOrderId(Integer workOrderId) {
    String w = workOrderId.toString();
    String key = BaseConstants.workOrderSendList + w;
    return redisTemplate.delete(key);
  }

}
