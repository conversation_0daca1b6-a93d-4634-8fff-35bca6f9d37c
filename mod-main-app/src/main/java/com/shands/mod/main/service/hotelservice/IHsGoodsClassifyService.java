package com.shands.mod.main.service.hotelservice;

import com.shands.mod.dao.model.hs.HsGoodsClassify;
import com.shands.mod.dao.model.req.hs.goods.FoodTypeReq;
import com.shands.mod.dao.model.req.hs.goods.HsGoodsClassifyReq;
import com.shands.mod.dao.model.req.hs.goods.SendAndLendReq;
import com.shands.mod.dao.model.res.hs.good.AllGoodsClassifyRes;
import com.shands.mod.dao.model.res.hs.good.GoodClassifyRes;
import com.shands.mod.dao.model.res.hs.good.SendOrLendAllRes;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2020/9/24
 * @desc 物品分类service
*/
public interface IHsGoodsClassifyService {

  int deleteByPrimaryKey(Integer id);

  int insert(HsGoodsClassify record);

  int insertSelective(HsGoodsClassifyReq record);

  HsGoodsClassify selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HsGoodsClassify record);

  int updateByPrimaryKey(HsGoodsClassify record);

  List<HsGoodsClassify> query(HsGoodsClassify req);

  List<GoodClassifyRes> typeByGoodsId(Map map);

  List<GoodClassifyRes> findBycompanyId(Map map);

  List<HsGoodsClassify> queryForY();

  List<AllGoodsClassifyRes> allClassifyForWork();

  List<SendOrLendAllRes> sendOrLendAll(SendAndLendReq req);
}
