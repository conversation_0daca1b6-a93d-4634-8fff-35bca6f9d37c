package com.shands.mod.main.service.message;

/**
 * <AUTHOR>
 * @date 2020/7/7
 * @desc 消息发送类型定义枚举类
*/
public enum MessageTypeEnum {


  MESSAGE_TYPE_APP("YM_APP","友盟APP消息推送"),
  /** 消息调用方 */
  MESSAGE_TYPE_CUSTOMER_ORDER("300","服务订单"),

  MESSAGE_TYPE_WORKCENTER_ORDER("200","工单中心"),
  /**
   * 员工状态
   */
  MESSAGE_TYPE_USER_STATUS("201","员工状态"),

  MESSAGE_TYPE_ASSESS("801","用户点评消息提醒-跳转评价列表"),

  MESSAGE_TYPE_CLEANWORK_CLEAN("802","做房任务提醒-跳转重新清扫任务详情"),

  MESSAGE_TYPE_CLEANWORK_CHECK("803","做房任务提醒-跳转检查任务详情"),

  MESSAGE_TYPE_CONSIGN_ORDER("804","寄存服务消息提醒-跳转到订单详情"),

  /** 消息接收者类型 */
  REJT_TYPE_CUSTOMER("CUSTOMER","用户"),

  REJT_TYPE_MANAGER("MANAGER","管理员"),

  REJT_TYPE_STAFF("STAFF","员工"),

  /** 声音文件类型 */
  REVIEW_SOUND_FILE("Review.caf","待审核"),

  NEWWORKORDER_SOUND_FILE("NewWorkOrder.caf","新建工单"),

  OVERDUE_SOUND_FILE("Overdue.caf","逾期"),
  WORK_ORDER_UPGRADE("WorkOrderUpgrade.caf", "工单升级提醒"),
  WORK_ORDER_COPY_IN("WorkOrderCopyIn.caf", "工单抄送提醒"),
  WORK_ORDER_TRANSFER("WorkOrderTransfer.caf", "工单转单提醒")

  ;

  /**
   * 类型码
   */
  private String typeCode;

  /**
   * 类型名称
   */
  private String typeName;

  MessageTypeEnum(String typeCode, String typeName) {
    this.typeCode = typeCode;
    this.typeName = typeName;
  }

  public String getTypeCode() {
    return typeCode;
  }

  public void setTypeCode(String typeCode) {
    this.typeCode = typeCode;
  }

  public String getTypeName() {
    return typeName;
  }

  public void setTypeName(String typeName) {
    this.typeName = typeName;
  }

  public static MessageTypeEnum getMessageTypeEnum(String typeCode) {
    for (MessageTypeEnum item : MessageTypeEnum.values()) {
      if (item.typeCode == typeCode) {
        return item;
      }
    }
    return null;
  }

  @Override
  public String toString() {
    return "MessageTypeEnum{" +
        "typeCode='" + typeCode + '\'' +
        ", typeName='" + typeName + '\'' +
        '}';
  }
}
