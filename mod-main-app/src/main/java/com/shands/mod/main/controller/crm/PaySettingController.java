package com.shands.mod.main.controller.crm;

import com.shands.mod.controller.BaseController;
import com.shands.mod.vo.ResultVO;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.buf.HexUtils;
import org.springframework.web.bind.annotation.PostMapping;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/** 支付配置 */
@Slf4j
@RestController
@RequestMapping("/paySetting")
public class PaySettingController extends BaseController {

  @Override
  public boolean isPublic() {
    return true;
  }

  /**
   * 配置
   * @param cert 证书
   * @return
   * @throws IOException
   */
  @PostMapping("/upload")
    @ResultLog(name = "PaySettingController.upload", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO upload(@RequestParam("cert") MultipartFile cert)
      throws IOException {
    if (cert != null && !cert.isEmpty()) {
      String s = HexUtils.toHexString(cert.getBytes());
      return ResultVO.success(s);
    }
    return ResultVO.failed();
  }

}
