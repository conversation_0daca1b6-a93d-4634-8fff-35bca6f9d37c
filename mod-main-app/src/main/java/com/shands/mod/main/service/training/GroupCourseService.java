package com.shands.mod.main.service.training;

import com.shands.mod.dao.model.training.bo.AddGroupBo;
import com.shands.mod.dao.model.training.bo.AssociationBo;
import com.shands.mod.dao.model.training.bo.ChooseCourseBo;
import com.shands.mod.dao.model.training.bo.GroupInfoBo;
import com.shands.mod.dao.model.training.bo.GroupSortBo;
import com.shands.mod.dao.model.training.bo.SearchGroupBo;
import com.shands.mod.dao.model.training.vo.CourseOfCollectionVo;
import com.shands.mod.dao.model.training.vo.FirstFolderVo;
import com.shands.mod.dao.model.training.vo.CourseGroupListVo;
import com.shands.mod.dao.model.training.vo.TrainingCenterVo;
import java.util.List;

/**
 * 课程分组相关接口
 * <AUTHOR>
 */
public interface GroupCourseService {
  /**
   * 查询课程分组数据（下拉框）
   */
  List<CourseGroupListVo> queryCourseGroupList();

  /**
   * 查询一级、二级目录
   * @return
   */
  List<FirstFolderVo> selectFolder(SearchGroupBo searchGroupBo);

  /**
   * 通过课程/合集名称模糊查询相应分组信息
   * @param groupInfoBo
   * @return
   */
  List<TrainingCenterVo> selectCourseGroupByName(GroupInfoBo groupInfoBo);

  /**
   * 添加分组
   * @param addGroupBo
   * @return
   */
  boolean insertCourseGroup(AddGroupBo addGroupBo);

  /**
   * 编辑分组
   * @param addGroupBo
   * @return
   */
  boolean updateCourseGroup(AddGroupBo addGroupBo);

  /**
   * 删除分组
   * @param groupId
   * @return
   */
  boolean deleteCourseGroup(Integer groupId);

  /**
   * 移除分组
   * @return
   */
  boolean removeCourseGroup(GroupSortBo groupSortBo);
  /**
   * 修改 课程，分组 排序
   * @param groupSortBo
   * @return
   */
  boolean editGroupCourseSort(GroupSortBo groupSortBo);

  /**
   * 分组中添加课程或合集
   * @param groupSortBo
   * @return
   */
  boolean insertCourseIntoGroup(GroupSortBo groupSortBo);

  /**
   * 分组排序
   * @param groupIdOne
   * @param groupIdTwo
   * @return
   */
  boolean updateGroupSort(Integer groupIdOne,Integer groupIdTwo,Integer upOrDown);

  /**
   * 选择课程
   * @return
   */
  CourseOfCollectionVo chooseCourse(ChooseCourseBo chooseCourseBo);

  /**
   * 批量添加课程/合集至分组
   * @param associationBo
   * @return
   */
  boolean bulkAddCourseToGroup(AssociationBo associationBo);
}
