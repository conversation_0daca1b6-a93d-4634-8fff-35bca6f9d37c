package com.shands.mod.main.service.remind;

import com.shands.mod.exception.NormalException;

public interface RemindExpireService {

  /**
   * 设置过期workOrderId
   *
   * @param workOrderId 工单id
   * @param workOrderStateEnumId 工单状态id
   * @param hotelServiceId 酒店服务
   * @param companyId
   * @param groupId
   * @param sec 超时秒
   * @throws NormalException
   */
  void setExpireWorkOrderId(
      String workOrderId,
      Integer workOrderStateEnumId,
      String hotelServiceId,
      Integer companyId,
      Integer groupId,
      Long sec,
      String workOrderType,
      Integer workOrderTypeInt,
      Integer workOrderAcceptDept)
      throws NormalException;

  /**
   * 删除 workOrderId
   *
   * @param workOrderId
   */
  void removeExpireWorkOrderId(String workOrderId);

}
