package com.shands.mod.main.service.betterwood;

import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord;
import com.shands.mod.dao.model.sales.tool.req.CompanyApplyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.res.CompanyMemberPageListRes;
import com.shands.mod.dao.model.sales.tool.res.CompanyPageListRes;
import com.shands.mod.dao.model.sales.tool.res.PublicEmailRes;
import com.shands.mod.dao.model.sales.tool.res.UploadBizLicenseRes;
import org.springframework.web.multipart.MultipartFile;

public interface CompanyMemberService {

  PageInfo<CompanyPageListRes> queryCompanyPageList(CompanyPageListReq req);

  PageInfo<CompanyMemberPageListRes> queryMemberPageList(CompanyMemberPageListReq req);

  UploadBizLicenseRes bizLicenseUpload(MultipartFile file, Integer uploadType);

  PublicEmailRes getPublicEmailSuffix();

  Boolean companyApply(CompanyApplyReq req);

  PageInfo<ModCompanyApplyRecord> getCompanyApplyRecordPage(Integer pageNo, Integer pageSize);
}
