package com.shands.mod.main.service.crs.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.betterwood.plugin.sensitive.v1.util.SensitiveHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.shands.mod.dao.mapper.hs.HotelFloorMapper;
import com.shands.mod.dao.mapper.hs.HotelRoomTypeMapper;
import com.shands.mod.dao.mapper.hs.HsRoomStatusRoleMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomTaskMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModRoleUcDao;
import com.shands.mod.dao.model.ModMenu;
import com.shands.mod.dao.model.enums.CleanStatusEnum;
import com.shands.mod.dao.model.enums.RoomStatusEnum;
import com.shands.mod.dao.model.enums.RoomStatusUriEnum;
import com.shands.mod.dao.model.hs.HotelFloor;
import com.shands.mod.dao.model.hs.HotelInfo;
import com.shands.mod.dao.model.hs.HotelRoomType;
import com.shands.mod.dao.model.hs.HsRoomStatusRole;
import com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomTask;
import com.shands.mod.dao.model.req.hs.RoomStaRoleReq;
import com.shands.mod.dao.model.req.hs.RoomStatusRoleReq;
import com.shands.mod.dao.model.res.hs.staff.AreaTreeRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.v0701.dto.RoomLockDto;
import com.shands.mod.dao.model.v0701.dto.RoomStatusDto;
import com.shands.mod.dao.model.v0701.dto.RoomsDto;
import com.shands.mod.dao.model.v0701.dto.TmpRoomDto;
import com.shands.mod.dao.model.v0701.vo.CompanyPmsVo;
import com.shands.mod.dao.model.v0701.vo.GuestInfoVo;
import com.shands.mod.dao.model.v0701.vo.RoomInfoDetailOldVo;
import com.shands.mod.dao.model.v0701.vo.RoomInfoDetailVo;
import com.shands.mod.dao.model.v0701.vo.RoomInfoVo;
import com.shands.mod.dao.model.v0701.vo.RoomStatuNum;
import com.shands.mod.external.model.dto.PMScancelRoomOS;
import com.shands.mod.external.model.dto.PMSsetRoomOS;
import com.shands.mod.external.model.dto.PMSupdateRoomSta;
import com.shands.mod.external.service.IGreenRoomStatusService;
import com.shands.mod.external.service.IPmsRoomStaService;
import com.shands.mod.main.annotation.CrsRoomStatus;
import com.shands.mod.main.service.common.HotelInfoCommonService;
import com.shands.mod.main.service.crs.ICrsRoomStatusService;
import com.shands.mod.main.util.HttpClientUtil;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.util.hs.DateUtils;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.PageVO;
import com.shands.mod.vo.ResultVO;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * 房态第三方接口工具类
 *
 * <AUTHOR>
 * @date 2021/04/29
 */
@Slf4j
@Service
public class CrsRoomStatusServiceImpl implements ICrsRoomStatusService {

  /**
   * sessionId过期时间
   */
  @Value("${crs.flashTimes:43200000}")
  private long flashtimes;

  @Value("${crs.appSecret:c5220a9da9c271b65456fba987bd6b3b}")
  private String appSecret;

  @Value("${crs.appKey:11202}")
  private String appKey;

  @Value("${crs.igroup.appKey:bff1c4b6873a4afa816309e05db1cf41}")
  private String igroupAppKey;

  @Value("${crs.igroup.writeAppKey:a92170a36d5e4d48f77ecdb7e084c371}")
  private String igroupWriteAppKey;

  @Value("${crs.hotelGroupCode:NEWCENTURY}")
  private String hotelGroupCode;

  @Value("${crs.usercode:shenGCBZG10}")
  private String usercode;

  @Value("${crs.password:shenGCBZG10}")
  private String password;

  @Value("${crs.crsUrl:http://10.10.1.113:8101}")
  private String crsUrl;

  @Value("${crs.igroup.url:http://10.10.1.113:8080}")
  private String igroupUrl;

  @Value("${crs.igroup.writeUrl:http://10.10.1.113:9090}")
  private String igroupWriteUrl;

  @Value("${crs.igroup.pmsstaurl:http://10.10.1.114:9090}")
  private String newPMSStaUrl;

  @Value("${crs.igroup.pmsseturl:http://10.10.1.114:9091}")
  private String newPMSSetUrl;

  private String sessionId;
  private long aliveTime;

  /**
   * 绿云接口固定参数
   **/
  private static final String VERSION = "3.0";
  private static final String REFRESH = "user.refresh";
  private static final String LOGIN = "user.login";
  private static final String LOCAL = "zh_CN";
  private static final String FORMAT = "json";

  @Resource
  private RedisTemplate redisTemplate;

  @Resource
  private HotelRoomTypeMapper roomTypeMapper;

  @Resource
  private HotelFloorMapper hotelFloorMapper;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Resource
  private IGreenRoomStatusService greenRoomStatusService;

  @Resource
  private HsCleanRoomTaskMapper taskMapper;

  @Resource
  private HsRoomStatusRoleMapper roomStatusRoleMapper;

  @Resource
  private ModRoleUcDao modRoleUcDao;

  @Resource
  private IPmsRoomStaService iPmsRoomStaService;

  @Resource
  private HotelInfoCommonService hotelInfoCommonService;

  @Override
  public String refreshSession() {
    Map<String, Object> signParams = new HashMap<>();
    signParams.put("hotelGroupCode", hotelGroupCode);
    signParams.put("v", VERSION);
    signParams.put("usercode", usercode);
    signParams.put("method", LOGIN);
    signParams.put("local", LOCAL);
    signParams.put("format", FORMAT);
    signParams.put("appKey", appKey);
    signParams.put("password", password);
    signParams.put("sign", getSign(signParams, appSecret));
    String reslutStr = HttpClientUtil
        .httpPostUtils(crsUrl + RoomStatusUriEnum.GET_SESSIONID.getUri(), signParams);
    log.info("[获取SessionId结果]：{}", reslutStr);

    if (StringUtils.hasText(reslutStr)){
      JSONObject reslut = JSONObject.parseObject(reslutStr);
      int code = reslut.containsKey("resultCode") ? (Integer) reslut.get("resultCode") : 99;
      if (code == 0) {
        sessionId = reslut.get("resultInfo") + "";
        aliveTime = System.currentTimeMillis();
        this.redisTemplate.opsForValue()
            .set(BaseConstants.CACHE_CRS_SESSIONID, sessionId, 24L, TimeUnit.HOURS);
      }

    }

    return sessionId;
  }

  @Override
  public List<Map<String, Object>> hotelFloor(Integer companyId) {
    List<Map<String, Object>> data = new ArrayList<>();
    String redisKey = BaseConstants.CACHE_CRS_FLOOR + BaseConstants.CACHE_KEY_SEPARATOR + companyId;
    data = (List) this.redisTemplate.opsForValue().get(redisKey);
    if (data != null && data.size() > 0) {
      return data;
    }
    List<HotelFloor> hotelFloors = hotelFloorMapper.allFloor(companyId, null);
    if (hotelFloors.isEmpty()) {
      return data;
    }
    List<AreaTreeRes> buildingAll = hotelFloorMapper.buildingAll(companyId);

    Map<String, List<HotelFloor>> listMap = hotelFloors.stream()
        .collect(Collectors.groupingBy(HotelFloor::getBuildingCode));

    data = new ArrayList<>();

    for (String key : listMap.keySet()) {
      Map<String, Object> floor = new HashMap<>();
      floor.put("building", key);
      floor.put("name", key);
      for (AreaTreeRes area : buildingAll) {
        if (key.equals(area.getCode())) {
          floor.put("name", area.getName());
          break;
        }
      }
      floor.put("floor",
          listMap.get(key).stream().sorted(Comparator
              .comparing(HotelFloor::getSort, Comparator.nullsLast(Integer::compareTo)))
              .collect(Collectors.toList()));
      data.add(floor);
    }

    //缓存楼层楼宇信息
    this.redisTemplate.opsForValue().set(redisKey, data, 24L, TimeUnit.HOURS);
    return data;
  }

  @Override
  public List<Map<String, Object>> listRoomSta(RoomStatusDto dto) {
    List<RoomInfoVo> allRooms = getAllRooms(dto);
    if (allRooms.size() <= 0) {
      return new ArrayList<>();
    }
    List<AreaTreeRes> buildingAll = hotelFloorMapper.buildingAll((int) dto.getCompanyId());

    //查询做房记录
    Integer cleanStatusCode = null;
    String cleanStatus = dto.getCleanStatus();
    if (StringUtils.hasText(cleanStatus)) {
      CleanStatusEnum statusEnum = CleanStatusEnum.valueOf(cleanStatus);
      cleanStatusCode = statusEnum.getCode();
    }
    List<HsCleanRoomTask> cleanRoomTasks = taskMapper
        .queryTodayTaskByStatus((int) dto.getCompanyId(), cleanStatusCode);

    //过滤列表数据
    List<RoomInfoVo> filterList = filterList(allRooms, dto);
    //处理单个房间数据信息
    processData(filterList, cleanRoomTasks);

    //通过清扫状态筛选数据
    if (StringUtils.hasText(cleanStatus)) {
      filterList = filterList.stream().filter(t -> t.getCleanStatus() != null)
          .collect(Collectors.toList());
    }

    List<Map<String, Object>> list = groupList(filterList);
    for (Map<String, Object> map : list) {
      for (AreaTreeRes area : buildingAll) {
        if (area.getCode().equals(map.get("building"))) {
          map.put("building", area.getName());
          break;
        }
      }
    }

    return orderList(list);
  }

  @Override
  public PageInfo<RoomInfoVo> listRoomByPage(RoomStatusDto dto, PageVO pageVO) {

    HotelInfo hotelInfo = new HotelInfo();
    hotelInfo.setCompanyId((int) dto.getCompanyId());
    CompanyPmsVo companyInfo = getCompanyInfo(hotelInfo);
    dto.setHotelId(companyInfo.getHotelId());

    Map<String, Object> params = new HashMap<>();
    params.put("hotelGroupId", dto.getHotelGroupId());
    params.put("hotelId", dto.getHotelId());
    params.put("hotelCode", companyInfo.getCode());
    params.put("appkey", igroupAppKey);

    //楼层筛选
    if (StringUtils.hasText(dto.getFloor())){
      params.put("floorList", dto.getFloor());
    }

    //房态筛选
    if (StringUtils.hasText(dto.getSta())){
      RoomStatusEnum sta = RoomStatusEnum.conversionStatu(dto.getSta());
      params.put("sta", sta != null ? sta.getCrsRoomStatu() : dto.getSta());
    }

    //预离、预抵
    if (StringUtils.hasText(dto.getGuestSta())){

      if ("L".equals(dto.getGuestSta())){
        params.put("isArr", "T");
      }else if ("T".equals(dto.getGuestSta())){
        params.put("isDep", "T");
      }

    }

    //房型
    if (StringUtils.hasText(dto.getRmtype())){
      params.put("rmtypeList", dto.getRmtype());
    }

    if (StringUtils.hasText(dto.getRmno())){
      params.put("rmno", dto.getRmno());
    }

    PageInfo<RoomInfoVo> roomsStaByPage = greenRoomStatusService
        .getRoomsStaByPage(igroupUrl, params, pageVO.getPageSize(), pageVO.getPageNo());

    List<RoomInfoVo> list = roomsStaByPage.getList();
    if (list != null && !list.isEmpty()){

      //替换本地房态
      list.forEach(t ->{
        RoomStatusEnum statusEnum = RoomStatusEnum.converLocalStatu(t.getSta());
        if (statusEnum != null) {
          t.setSta(statusEnum.getRoomStatu());
        }
      });

      //设置楼层楼宇中文名
      PageHelper.clearPage();
      setFloorInfo(companyInfo.getCompanyId(), list);

    }

    return roomsStaByPage;
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.SET_ROOMS_TMP)
  public ResultVO setRoomsTmp(TmpRoomDto dto) {
    Assert.notNull(dto.getCompanyId(), "酒店id不能为空");
    Assert.hasText(dto.getRmno(), "房间号不能为空");

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(dto.getCompanyId());

    if (modHotelInfo == null) {
      throw new RuntimeException("酒店信息查询为空");
    }
    dto.setHotelId(modHotelInfo.getPmsHotelId());

    Map<String, Object> params = new HashMap<>();

    params.put("hotelGroupId", dto.getHotelGroupId());
    params.put("hotelId", dto.getHotelId());

    List<String> list = Arrays.asList(dto.getRmno().split(BaseConstants.ARRAY_SEPARATOR));
    params.put("rooms", list);
    params.put("cause", dto.getCause());
    params.put("remark", dto.getRemark());

    boolean b = greenRoomStatusService.setRoomsTmp(igroupWriteUrl, params, igroupWriteAppKey,ThreadLocalHelper.getUser().getName());

    if (b){
      return ResultVO.success();
    }

    return ResultVO.failed("设置临时态失败");
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.CANCEL_ROOMS_TMP)
  public ResultVO cancelRoomsTmp(TmpRoomDto dto) {
    Assert.notNull(dto.getCompanyId(), "酒店id不能为空");
    Assert.hasText(dto.getRmno(), "房间号不能为空");

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(dto.getCompanyId());

    if (modHotelInfo == null) {
      throw new RuntimeException("酒店信息查询为空");
    }
    dto.setHotelId(modHotelInfo.getPmsHotelId());

    Map<String, Object> params = new HashMap<>();

    params.put("hotelGroupId", dto.getHotelGroupId());
    params.put("hotelId", dto.getHotelId());

    List<String> list = Arrays.asList(dto.getRmno().split(BaseConstants.ARRAY_SEPARATOR));
    params.put("rooms", list);
    params.put("belongDepts", dto.getBelongDepts());

    //判断是否为新PMS酒店
    if("IGROUP".equals(modHotelInfo.getPmsFlag())){
      if("R".equals(dto.getStatus())){
        dto.setStatus("C");
      }else if ("N".equals(dto.getStatus())){
        dto.setStatus(null);
      }
    }
    params.put("status", dto.getStatus());

    boolean b = false;
    try {
      b = greenRoomStatusService.cancelRoomsTmp(igroupWriteUrl, params, igroupWriteAppKey,ThreadLocalHelper.getUser().getName());

    } catch (Exception e) {
      log.error("[取消临时态异常]：{}", e.getMessage());
      return ResultVO.failed(e.getMessage());
    }

    if (b){
      return ResultVO.success();
    }

    return ResultVO.failed("取消临时态失败");
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.UPDATE_ROOMSTA)
  public ResultVO updateRoomSta(RoomStatusDto dto) {

    try {

      Integer companyId = ThreadLocalHelper.getCompanyId();
      ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(companyId);

      if (modHotelInfo == null) {
        throw new RuntimeException("酒店信息查询为空");
      }

      Map<String, Object> signParams = getParamsMap();
      signParams.put("hotelGroupId", dto.getHotelGroupId());
      signParams.put("hotelId", modHotelInfo.getPmsHotelId());
      Assert.notNull(signParams.get("hotelGroupId"), "hotelGroupId不能为空");
      Assert.notNull(signParams.get("hotelId"), "hotelId不能为空");
      Assert.isTrue(dto.getRooms().size() > 0, "房号不能为空");
      Assert.notNull(dto.getNewSta(), "房间状态不能为空");
      signParams.put("sta", dto.getNewSta());
      List<RoomsDto> rooms = dto.getRooms();
      StringBuilder message = new StringBuilder("房号：");
      boolean fals = false;
      for (RoomsDto room : rooms) {
        //判断是否为新PMS酒店
        if("IGROUP".equals(modHotelInfo.getPmsFlag())){

          PMSupdateRoomSta reqDto = new PMSupdateRoomSta();
          List<String> staRooms = new ArrayList<>();
          staRooms.add(room.getRmno());

          reqDto.setHotelId(modHotelInfo.getPmsHotelId());
          if("R".equals(dto.getNewSta())){
            reqDto.setSta("C");
          }else{
            reqDto.setSta(dto.getNewSta());
          }
          reqDto.setUpdateUser(ThreadLocalHelper.getUser().getUcId());
          reqDto.setUserName(ThreadLocalHelper.getUser().getName());
          reqDto.setRoomNoList(staRooms);

          boolean flag = iPmsRoomStaService.updateRoomSta(newPMSStaUrl + "/state_web/room/sta/updateRoomSta",reqDto);
          if(!flag){
            message.append("[").append(room.getRmno()).append("]修改房态失败 ");
            fals = true;
          }
        }else{
          signParams.remove("sign");
          signParams.put("rmno", room.getRmno());
          signParams.put("sign", getSign(signParams, appSecret));

          String resultStr = greenRoomStatusService.updateRoomSta(crsUrl, signParams);

          if (resultStr == null || "".equals(resultStr)) {
            message.append("[").append(room.getRmno()).append("] ");
            fals = true;
          } else {
            JSONObject result = JSONObject.parseObject(resultStr);
            if (result.containsKey("resultCode") && result.getInteger("resultCode") == -1) {
              message.append("[").append(room.getRmno()).append("] ");
              fals = true;
            }
          }
        }
      }
      if (fals) {
        message.append("状态更新失败");
        return ResultVO.failed(message.toString());
      }

      //异步刷新redis房态信息
      /*executor.execute(() -> {
        dto.setHotelId(modHotelInfo.getPmsHotelId());
        getAllRoomStaFromCrsNew(dto);
      });*/
    }catch (Exception e){
      log.error("[更新房态异常] ", e);
      return ResultVO.failed("更新房态异常: " + e.getMessage());
    }

    return ResultVO.success();
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.LOCK_ROOMSTA)
  public ResultVO lookRoom(RoomStatusDto dto) {
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    Map<String, Object> signParams = getParamsMap();
    signParams.put("hotelGroupId", dto.getHotelGroupId());
    signParams.put("hotelId", dto.getHotelId());
    Assert.notNull(signParams.get("hotelGroupId"), "hotelGroupId不能为空");
    Assert.notNull(signParams.get("hotelId"), "hotelId不能为空");
    Assert.isTrue(dto.getLockList() != null && dto.getLockList().size() > 0, "锁定房间列表不能为空");
    signParams.put("reasonCode", dto.getReasonCode());
    signParams.put("remark", dto.getRemark());
    signParams.put("dateBegin", dto.getDateBegin());
    signParams.put("dateEnd", dto.getDateEnd());
    List<RoomLockDto> lockList = dto.getLockList();
    StringBuilder message = new StringBuilder();
    boolean fals = false;
    for (RoomLockDto room : lockList) {

      //判断是否为新PMS酒店
      if("IGROUP".equals(modHotelInfo.getPmsFlag())){

        List<String> staRooms = new ArrayList<>();
        staRooms.add(room.getRmno());

        PMSsetRoomOS reqDto = new PMSsetRoomOS();
        try {
          reqDto.setDateBegin(DateUtils.strConverDateyyss(dto.getDateBegin().substring(0,11) + "00:00:00"));
          reqDto.setDateEnd(DateUtils.strConverDateyyss(dto.getDateEnd().substring(0,11) + "23:59:59"));
        } catch (ParseException e) {
          throw new RuntimeException(e);
        }
        reqDto.setHotelId(modHotelInfo.getPmsHotelId());
        reqDto.setHotelCode(modHotelInfo.getHotelCode());
        reqDto.setRemark(dto.getRemark());
        reqDto.setUpdateUser(ThreadLocalHelper.getUser().getUcId());
        reqDto.setUserName(ThreadLocalHelper.getUser().getName());
        reqDto.setRoomNoList(staRooms);

        boolean flag = iPmsRoomStaService.setRoomOS(newPMSSetUrl + "/state_web/room/sta/chg/setRoomOS",reqDto);
        if(!flag){
          message.append("[").append(room.getRmno()).append("]修改房态失败 ");
          fals = true;
        }
      }else{
        signParams.remove("sign");
        signParams.put("rmno", room.getRmno());
        signParams.put("sign", getSign(signParams, appSecret));

        String resultStr = greenRoomStatusService.lookRoom(crsUrl, signParams);

        if (resultStr != null && !"".equals(resultStr)) {
          JSONObject json = JSONObject.parseObject(resultStr);
          if (json.containsKey("resultCode") && json.getInteger("resultCode") != 0) {
            message.append(json.getString("resultMsg")).append("\r\n");
            fals = true;
          }
        }
      }
    }
    if (fals) {
      message.append("状态更新失败");
      return ResultVO.failed(message.toString());
    }
    //异步刷新redis房态信息
    /*executor.execute(() -> {
      getAllRoomStaFromCrsNew(dto);
    });*/
    return ResultVO.success();
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.UNLOCK_ROOMSTA)
  public ResultVO unLookRoom(RoomStatusDto dto) {
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    Map<String, Object> signParams = getParamsMap();
    signParams.put("hotelGroupId", dto.getHotelGroupId());
    signParams.put("hotelId", dto.getHotelId());
    Assert.notNull(signParams.get("hotelGroupId"), "hotelGroupId不能为空");
    Assert.notNull(signParams.get("hotelId"), "hotelId不能为空");
    Assert.isTrue(!dto.getLockList().isEmpty() && dto.getLockList().size() > 0, "解除锁定房间列表不能为空");
    List<RoomLockDto> lockList = dto.getLockList();
    StringBuilder message = new StringBuilder();
    boolean fals = false;
    for (RoomLockDto room : lockList) {

      //判断是否为新PMS酒店
      if("IGROUP".equals(modHotelInfo.getPmsFlag())){

        List<String> staRooms = new ArrayList<>();
        staRooms.add(room.getRmno());

        PMScancelRoomOS reqDto = new PMScancelRoomOS();
        reqDto.setHotelId(modHotelInfo.getPmsHotelId());
        reqDto.setHotelCode(modHotelInfo.getHotelCode());
        reqDto.setUpdateUser(ThreadLocalHelper.getUser().getUcId());
        reqDto.setUserName(ThreadLocalHelper.getUser().getName());
        reqDto.setRoomNoList(staRooms);

        boolean flag = iPmsRoomStaService.cancelRoomOS(newPMSSetUrl + "/state_web/room/sta/chg/cancelRoomOS",reqDto);
        if(!flag){
          message.append("[").append(room.getRmno()).append("]修改房态失败 ");
          fals = true;
        }
      }else{
        signParams.remove("sign");
        signParams.put("rmno", room.getRmno());
        signParams.put("sign", getSign(signParams, appSecret));

        String resultStr = greenRoomStatusService.unLookRoom(crsUrl, signParams);

        if (resultStr != null && !"".equals(resultStr)) {
          JSONObject json = JSONObject.parseObject(resultStr);
          if (json.containsKey("resultCode") && json.getInteger("resultCode") != 0) {
            message.append(json.getString("resultMsg")).append("\r\n");
            fals = true;
          }
        }
      }
    }
    if (fals) {
      message.append("状态更新失败");
      return ResultVO.failed(message.toString());
    }

    //异步刷新redis房态信息
    /*executor.execute(() -> {
      getAllRoomStaFromCrsNew(dto);
    });*/
    return ResultVO.success();
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.REPAIRE_ROOMSTA)
  public ResultVO repairRoom(RoomStatusDto dto) {
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    if (StringUtils.isEmpty(dto.getHotelId()) && !StringUtils.isEmpty(dto.getCompanyId())) {
      HotelInfo pms = new HotelInfo();
      pms.setCompanyId((int) dto.getCompanyId());
      CompanyPmsVo companyInfo = getCompanyInfo(pms);
      dto.setHotelId(companyInfo.getHotelId());
    }

    Map<String, Object> signParams = getParamsMap();
    signParams.put("hotelGroupId", dto.getHotelGroupId());
    signParams.put("hotelId", dto.getHotelId());
    Assert.notNull(signParams.get("hotelGroupId"), "hotelGroupId不能为空");
    Assert.notNull(signParams.get("hotelId"), "hotelId不能为空");
    Assert.isTrue(!dto.getRepairList().isEmpty() && dto.getRepairList().size() > 0, "维修房间列表不能为空");
    signParams.put("reasonCode", dto.getReasonCode());
    signParams.put("remark", dto.getRemark());
    signParams.put("beginDate", dto.getDateBegin());
    signParams.put("endDate", dto.getDateEnd());
    List<RoomLockDto> repairList = dto.getRepairList();
    StringBuilder message = new StringBuilder();
    boolean fals = false;
    for (RoomLockDto room : repairList) {
      //判断是否为新PMS酒店
      if("IGROUP".equals(modHotelInfo.getPmsFlag())){

        List<String> staRooms = new ArrayList<>();
        staRooms.add(room.getRmno());

        PMSsetRoomOS reqDto = new PMSsetRoomOS();
        try {
          reqDto.setDateBegin(DateUtils.strConverDateyyss(dto.getDateBegin().substring(0,11) + "00:00:00"));
          reqDto.setDateEnd(DateUtils.strConverDateyyss(dto.getDateEnd().substring(0,11) + "23:59:59"));
        } catch (ParseException e) {
          throw new RuntimeException(e);
        }
        reqDto.setHotelId(modHotelInfo.getPmsHotelId());
        reqDto.setHotelCode(modHotelInfo.getHotelCode());
        reqDto.setRemark(dto.getRemark());
        reqDto.setUpdateUser(ThreadLocalHelper.getUser().getUcId());
        reqDto.setUserName(ThreadLocalHelper.getUser().getName());
        reqDto.setRoomNoList(staRooms);

        boolean flag = iPmsRoomStaService.setRoomOO(newPMSSetUrl + "/state_web/room/sta/chg/setRoomOO",reqDto);
        if(!flag){
          message.append("[").append(room.getRmno()).append("]修改房态失败 ");
          fals = true;
        }
      }else{
        signParams.remove("sign");
        signParams.put("rmnos", room.getRmno());
        signParams.put("sign", getSign(signParams, appSecret));

        JSONObject result = greenRoomStatusService.repairRoom(crsUrl, signParams);

        if (result.containsKey("resultCode") && result.getInteger("resultCode") != 0) {
          message.append("房号[ ").append(room.getRmno()).append(" ]");
          message.append(result.getString("resultMsg")).append("\r\n");
          fals = true;
        }
      }
    }
    if (fals) {
      return ResultVO.failed(message.toString());
    }

    //异步刷新redis房态信息
    /*executor.execute(() -> {
      getAllRoomStaFromCrsNew(dto);
    });*/
    return ResultVO.success();
  }

  @Override
  @CrsRoomStatus(ROOM_STATUS_URI_ENUM = RoomStatusUriEnum.REMOVE_REPAIREROOMSTA)
  public ResultVO unRepairRoom(RoomStatusDto dto) {
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    Map<String, Object> signParams = getParamsMap();
    signParams.put("hotelGroupId", dto.getHotelGroupId());
    signParams.put("hotelId", dto.getHotelId());
    Assert.notNull(signParams.get("hotelGroupId"), "hotelGroupId不能为空");
    Assert.notNull(signParams.get("hotelId"), "hotelId不能为空");
    Assert.isTrue(!dto.getRepairList().isEmpty() && dto.getRepairList().size() > 0, "解除维修房间列表不能为空");
    try {
      Date today = new Date();
      signParams.put("beginDate", DateUtils.dateToString(today, DateUtils.DATE_FORMAT_YYYY_MM_DD));
      signParams.put("endDate", DateUtils
          .dateToString(DateUtils.strConverDate(DateUtils.addDays(30)),
              DateUtils.DATE_FORMAT_YYYY_MM_DD));
    } catch (Exception e) {
      log.error("[解除维修房时间转换错误] ", e);
    }
    List<RoomLockDto> repairList = dto.getRepairList();
    StringBuilder message = new StringBuilder();
    boolean fals = false;
    for (RoomLockDto room : repairList) {

      //判断是否为新PMS酒店
      if("IGROUP".equals(modHotelInfo.getPmsFlag())){

        List<String> staRooms = new ArrayList<>();
        staRooms.add(room.getRmno());

        PMScancelRoomOS reqDto = new PMScancelRoomOS();
        reqDto.setHotelId(modHotelInfo.getPmsHotelId());
        reqDto.setHotelCode(modHotelInfo.getHotelCode());
        reqDto.setUpdateUser(ThreadLocalHelper.getUser().getUcId());
        reqDto.setUserName(ThreadLocalHelper.getUser().getName());
        reqDto.setRoomNoList(staRooms);

        boolean flag = iPmsRoomStaService.cancelRoomOO(newPMSSetUrl + "/state_web/room/sta/chg/cancelRoomOO",reqDto);
        if(!flag){
          message.append("[").append(room.getRmno()).append("]修改房态失败");
          fals = true;
        }
      }else{
        signParams.remove("sign");
        signParams.put("rmnos", room.getRmno());
        signParams.put("sign", getSign(signParams, appSecret));

        JSONObject result = greenRoomStatusService.unRepairRoom(crsUrl, signParams);

        if (result.containsKey("resultCode") && result.getInteger("resultCode") != 0) {
          message.append("房号[ ").append(room.getRmno()).append(" ]");
          message.append(result.getString("resultMsg")).append("\r\n");
          fals = true;
        }
      }
    }
    if (fals) {
      return ResultVO.failed(message.toString());
    }

    //异步刷新redis房态信息
    /*executor.execute(() -> {
      getAllRoomStaFromCrsNew(dto);
    });*/
    return ResultVO.success();
  }

  @Override
  public CompanyPmsVo getCompanyInfo(HotelInfo company) {
    BoundHashOperations operations = this.redisTemplate
        .boundHashOps(BaseConstants.CACHE_CRS_COMPANYINFO);
    CompanyPmsVo pmsVo = getCompanyFormRedis(company);
    if (pmsVo != null) {
      return pmsVo;
    }
    if (Objects.requireNonNull(operations.entries()).isEmpty() || !Objects
        .requireNonNull(operations.entries()).containsKey(String.valueOf(company.getCompanyId()))) {
      getCompanyDbToRides(company);
    }
    return getCompanyFormRedis(company);
  }

  @Override
  public List<HotelRoomType> getRoomType(Integer companyId) {
    List<HotelRoomType> roomTypes;
    roomTypes = this.redisTemplate.opsForList()
        .range(BaseConstants.CACHE_CRS_ROOMTYPES + BaseConstants.CACHE_KEY_SEPARATOR + companyId, 0,
            -1);
    if (roomTypes != null && !roomTypes.isEmpty()) {
      return roomTypes;
    }
    roomTypes = roomTypeMapper.allRoomType(companyId);
    Assert.isTrue(!roomTypes.isEmpty(), "未查询到相关房型");
    this.redisTemplate.opsForList().rightPushAll(
        BaseConstants.CACHE_CRS_ROOMTYPES + BaseConstants.CACHE_KEY_SEPARATOR + companyId,
        roomTypes);
    this.redisTemplate
        .expire(BaseConstants.CACHE_CRS_ROOMTYPES + BaseConstants.CACHE_KEY_SEPARATOR + companyId,
            24L,
            TimeUnit.HOURS);
    return roomTypes;
  }

  @Override
  public List<RoomStatuNum> getRoomsNum(RoomStatusDto dto) {

    List<RoomStatuNum> statuNums = new ArrayList<>();

    List<RoomInfoVo> list = getAllRooms(dto);
    Assert.isTrue(list.size() > 0, "数据获取失败");
    Map<String, List<RoomInfoVo>> map = list.stream()
        .collect(Collectors.groupingBy(RoomInfoVo::getSta));

    //计算所有房态对应数量
    List<String> status = RoomStatusEnum.getAllStatus();

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById((int)dto.getCompanyId());

    for (String sta : status) {
      RoomStatuNum num = new RoomStatuNum();
      num.setSta(sta);
      RoomStatusEnum statusEnum = RoomStatusEnum.conversionStatu(sta);
      if("IGROUP".equals(modHotelInfo.getPmsFlag())){
        if (statusEnum != null && map.get(statusEnum.getRoomStatu()) != null) {
          num.setNum(map.get(statusEnum.getRoomStatu()).size());
        } else {
          num.setNum(0);
        }
      }else{
        if (statusEnum != null && map.get(statusEnum.getCrsRoomStatu()) != null) {
          num.setNum(map.get(statusEnum.getCrsRoomStatu()).size());
        } else {
          num.setNum(0);
        }
      }
      statuNums.add(num);
    }

    return statuNums;
  }

  @Override
  public List<RoomInfoVo> searchRoomsNew(RoomStatusDto dto) {

    Assert.notNull(dto.getCompanyId(), "酒店id不能为空");
    Assert.hasText(dto.getSearchStr(), "搜索条件为空");

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById((int) dto.getCompanyId());

    if (modHotelInfo == null) {
      throw new RuntimeException("酒店信息查询为空");
    }
    dto.setHotelId(modHotelInfo.getPmsHotelId());

    Map<String, Object> params = new HashMap<>();
    params.put("hotelGroupId", dto.getHotelGroupId());
    params.put("hotelId", dto.getHotelId());
    params.put("hotelCode", modHotelInfo.getHotelCode());
    params.put("searchStr", dto.getSearchStr());

    List<RoomInfoVo> list = greenRoomStatusService.searchRooms(igroupUrl, params, igroupAppKey);

    if (list != null && !list.isEmpty()){
      //替换本地房态
      list.forEach(t ->{
        RoomStatusEnum statusEnum = RoomStatusEnum.converLocalStatu(t.getSta());
        if (statusEnum != null) {
          t.setSta(statusEnum.getRoomStatu());
        }
      });

      //设置楼层楼宇中文名
      setFloorInfo(modHotelInfo.getHotelId(), list);
    }

    return list;

  }

  @Override
  public List<Map<String, Object>> searchRooms(RoomStatusDto dto) {
    Assert.notNull(dto.getCompanyId(), "酒店id不能为空");
    Assert.hasText(dto.getSearchStr(), "搜索条件为空");

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById((int) dto.getCompanyId());

    if (modHotelInfo == null) {
      throw new RuntimeException("酒店信息查询为空");
    }
    dto.setHotelId(modHotelInfo.getPmsHotelId());

    List<RoomInfoVo> allRooms = getAllRooms(dto);
    Assert.isTrue(allRooms.size() > 0, "未查询到相关信息");
    allRooms = allRooms.stream().filter(
        t -> {
          //模糊查询
          GuestInfoVo guests = t.getGuests();
          if (guests != null) {
            return guests.getName().contains(dto.getSearchStr()) || t.getRmno()
                .contains(dto.getSearchStr());
          } else {
            return t.getRmno().contains(dto.getSearchStr());
          }
        }).collect(
        Collectors.toList());

    //做房任务查询
    List<HsCleanRoomTask> cleanRoomTasks = taskMapper
        .queryTodayTaskByStatus((int) dto.getCompanyId(), null);
    processData(allRooms, cleanRoomTasks);

    //替换为本地房态
    allRooms.forEach(t -> {
      RoomStatusEnum statusEnum = RoomStatusEnum.converLocalStatu(t.getSta());
      if (statusEnum != null) {
        t.setSta(statusEnum.getRoomStatu());
      }
    });
    return groupList(allRooms);
  }

  /**
   * 获取redis中的酒店信息
   *
   * @return {@link CompanyPmsVo}
   */
  private CompanyPmsVo getCompanyFormRedis(HotelInfo company) {
    Assert.notNull(company, "请至少传入一个查询条件");
    //通过酒店id直接获取
    if (company.getCompanyId() != null && this.redisTemplate.opsForHash()
        .hasKey(BaseConstants.CACHE_CRS_COMPANYINFO, String.valueOf(company.getCompanyId()))) {
      return (CompanyPmsVo) this.redisTemplate.opsForHash()
          .get(BaseConstants.CACHE_CRS_COMPANYINFO, String.valueOf(company.getCompanyId()));
    }

    //通过酒店名称或code查询
    BoundHashOperations<String, String, CompanyPmsVo> boundHashOperations = this.redisTemplate
        .boundHashOps(BaseConstants.CACHE_CRS_COMPANYINFO);
    if (Objects.requireNonNull(boundHashOperations).entries() != null) {
      Map<String, CompanyPmsVo> entries = boundHashOperations.entries();
      for (String key : entries.keySet()) {
        //通过酒店名称查找
        if (company.getName() != null && company.getName().equals(entries.get(key).getName())) {
          return entries.get(key);
        }
        //通过酒店code查找
        if (company.getCode() != null && company.getCode().equals(entries.get(key).getCode())) {
          return entries.get(key);
        }
      }
    }
    return null;
  }

  /**
   * 从数据库查询数据存入redis
   *
   * @param company 公司
   */
  private void getCompanyDbToRides(HotelInfo company) {
    List<CompanyPmsVo> companyInfo = modHotelInfoDao.querySelective(company);
    Assert.isTrue(!companyInfo.isEmpty(), "未查询到相关酒店信息");
    Map<String, CompanyPmsVo> map = new HashMap<>();
    for (CompanyPmsVo info : companyInfo) {
      map.put(info.getCompanyId().toString(), info);
    }

    boolean b = false;
    if (!this.redisTemplate.hasKey(BaseConstants.CACHE_CRS_COMPANYINFO)){
      b = true;
    }

    Iterator<Entry<String, CompanyPmsVo>> iterator = map.entrySet().iterator();
    while (iterator.hasNext()) {
      Entry<String, CompanyPmsVo> obj = iterator.next();
      this.redisTemplate.opsForHash()
          .put(BaseConstants.CACHE_CRS_COMPANYINFO, obj.getKey(), map.get(obj.getKey()));
    }

    if (b){

      this.redisTemplate.expire(BaseConstants.CACHE_CRS_COMPANYINFO, 24L, TimeUnit.HOURS);
    }
  }

  /**
   * 计算签名
   *
   * @param signParams 参数
   * @param appSecret  appSecret
   * @return {@link String}
   */
  public String getSign(Map<String, Object> signParams, String appSecret) {
    //获取signParams中的key值
    StringBuilder sb = new StringBuilder();
    List<String> keyList = new ArrayList(signParams.size());
    for (Map.Entry entry : signParams.entrySet()) {
      keyList.add((String) entry.getKey());
    }
    //将key值进行排序
    Collections.sort(keyList);
    //按照该方式拼接成字符串 appSecret+key+map.get(key)+appSecret
    sb.append(appSecret);
    for (String key : keyList) {
      sb.append(key).append(signParams.get(key));
    }
    sb.append(appSecret);
    //SHA1 加密后 将二进制输出为字符串
    try {
      byte[] sha1Digest = getSHA1Digest(sb.toString());
      return byte2hex(sha1Digest);
    } catch (IOException e) {
      throw new RuntimeException("签名失败", e);
    }
  }

  @Override
  public Map<String, Object> getParamsMap() {
    Map<String, Object> paramsMap = new HashMap<>();
    Object o = this.redisTemplate.opsForValue().get(BaseConstants.CACHE_CRS_SESSIONID);
    if (o != null && !"".equals(o.toString())) {
      this.sessionId = o.toString();
    } else {
      this.sessionId = getSeesionId();
    }
    paramsMap.put("appKey", this.appKey);
    paramsMap.put("sessionId", this.sessionId);
    return paramsMap;
  }

  @Override
  public List<RoomInfoVo> getAllRoomsSta(RoomStatusDto dto) {
    Assert.notNull(dto.getCompanyId(), "酒店id不能为空");
    HotelInfo info = new HotelInfo();
    info.setCompanyId(Math.toIntExact(dto.getCompanyId()));
    CompanyPmsVo companyInfo = getCompanyInfo(info);

    Assert.notNull(companyInfo, "未查询到酒店信息");

    dto.setHotelId(companyInfo.getHotelId());
    List<RoomInfoVo> allRooms = getAllRooms(dto);

    if (allRooms.isEmpty()) {
      return allRooms;
    }
    //替换为本地房态
    allRooms.forEach(t -> {
      RoomStatusEnum statusEnum = RoomStatusEnum.converLocalStatu(t.getSta());
      if (statusEnum != null) {
        t.setSta(statusEnum.getRoomStatu());
      }
    });
    return allRooms;
  }

  @Override
  public List<HsRoomStatusRole> getRoomStaRoles() {
    HsRoomStatusRole roomStatusRole = new HsRoomStatusRole();
    roomStatusRole.setDeleted(BaseConstants.DATA_UNDELETED);

    return roomStatusRoleMapper.queryAll(roomStatusRole);
  }

  @Override
  public ResultVO getOperateRole(RoomStaRoleReq roomStaRole) {
    //用户拥有权限列表
    List<String> roles = roomStaRole.getRoles();
    Assert.isTrue(roles != null && !roles.isEmpty(), "用户角色权限为空");

    List<HsRoomStatusRole> roomStaRoles = roomStatusRoleMapper
        .queryByRoleIds(roles);

    //未配置房态权限则不进行限制
    if (roomStaRoles.isEmpty()) {
      return ResultVO.success();
    }

    List<String> stintList = roomStaRoles.stream().map(HsRoomStatusRole::getRoleCode)
        .collect(Collectors.toList());

    //拥有房态管理菜单的角色
    ModMenu menu = new ModMenu();
    menu.setPlantformCode("APP");
    menu.setName("房态管理");
    menu.setType("C");
    List<String> codeList = modRoleUcDao.selectCodeByMenu(menu);

    boolean b = false;
    List<String> valid = new ArrayList<>();
    for (String role : roles) {
      if (codeList.contains(role)){
        valid.add(role);

        if (!stintList.contains(role)) {
          b = true;
          break;
        }
      }
    }

    if (b) {
      return ResultVO.success();
    }

    //判断用户是否有房态管理菜单角色
    if (valid.isEmpty()){
      return ResultVO.failed("用户权限不足");
    }

    try {
      for (HsRoomStatusRole role : roomStaRoles) {

        if (DateUtils.isBelongPeriodTime(role.getStartTime(), role.getEndTime())) {
          b = true;
        }

      }

    } catch (Exception e) {
      throw new RuntimeException("后台权限时间设置异常，请检查");
    }

    if (b) {
      return ResultVO.success();
    }

    return ResultVO.failed("未在规定时间内");
  }

  @Override
  public ResultVO saveOperateRole(RoomStatusRoleReq req) {
    Assert.notNull(req.getRoleName(), "角色名称不能为空");
    Assert.notNull(req.getStartTime(), "起始时间不能为空");
    Assert.notNull(req.getEndTime(), "结束时间不能为空");
    Assert.notNull(req.getStatus(), "状态不能为空");

    HsRoomStatusRole roomStatusRole = new HsRoomStatusRole();
    BeanUtils.copyProperties(req, roomStatusRole);

    roomStatusRole.setDeleted(BaseConstants.DATA_UNDELETED);

    int i;
    if (roomStatusRole.getId() != null) {

      roomStatusRole.setUpdateTime(new Date());
      roomStatusRole.setUpdateUser(ThreadLocalHelper.getUser() == null ? null : ThreadLocalHelper.getUser().getId());
      i = roomStatusRoleMapper.update(roomStatusRole);

    } else {

      roomStatusRole.setCreateTime(new Date());
      roomStatusRole.setCreateUser(ThreadLocalHelper.getUser() == null ? null : ThreadLocalHelper.getUser().getId());
      i = roomStatusRoleMapper.insert(roomStatusRole);
    }

    if (i > 0) {
      return ResultVO.success();
    }

    return ResultVO.failed("保存失败");
  }

  @Override
  public ResultVO deleteOperateRole(RoomStatusRoleReq req) {

    Assert.notNull(req.getId(), "id不能为空");

    HsRoomStatusRole roomStatusRole = new HsRoomStatusRole();
    roomStatusRole.setId(req.getId());
    roomStatusRole.setDeleted(BaseConstants.DATA_DELETED);
    int i = roomStatusRoleMapper.update(roomStatusRole);

    if (i > 0){
      return ResultVO.success();
    }

    return ResultVO.failed("删除失败");
  }

  @Override
  public RoomInfoDetailVo getRoomStaByRmno(RoomStatusDto dto) {

    Assert.notNull(dto.getCompanyId(), "酒店id不能为空");
    Assert.hasText(dto.getRmno(), "房间号不能为空");

    HotelInfo info = new HotelInfo();
    info.setCompanyId(Math.toIntExact(dto.getCompanyId()));
    CompanyPmsVo companyInfo = getCompanyInfo(info);

    dto.setHotelId(companyInfo.getHotelId());

    Map<String, Object> params = new HashMap<>();
    params.put("hotelGroupId", dto.getHotelGroupId());
    params.put("hotelId", dto.getHotelId());
    params.put("hotelCode", companyInfo.getCode());
    params.put("appkey", igroupAppKey);
    params.put("rmno", dto.getRmno());

    RoomInfoDetailVo vo = greenRoomStatusService.getRoomStaByRmno(igroupUrl, params);

    if (vo != null) {
      RoomStatusEnum statusEnum = RoomStatusEnum.converLocalStatu(vo.getSta());

      if (statusEnum != null) {
        vo.setSta(statusEnum.getRoomStatu());
      }

      //宾客信息脱敏
      processGuestInfo(vo.getMasterBaseRes());
      processGuestInfo(vo.getMasterBaseArrRes());
      processGuestInfo(vo.getMasterBasePreviousRes());
    }
    return vo;
  }

  /**
   * 处理客人信息
   *
   * @param guestList 客人名单
   */
  private void processGuestInfo(List<GuestInfoVo> guestList){

    if (guestList != null && !guestList.isEmpty()){
      for (GuestInfoVo guest : guestList) {
        doRemoveSensitive(guest);
      }
    }

  }

  @Override
  public RoomInfoDetailOldVo getRoomStaByRmnoOld(RoomStatusDto dto) {

    RoomInfoDetailVo roomStaByRmno = getRoomStaByRmno(dto);
    List<GuestInfoVo> masterBaseRes = roomStaByRmno.getMasterBaseRes();

    roomStaByRmno.setMasterBaseRes(null);

    RoomInfoDetailOldVo vo = new RoomInfoDetailOldVo();
    BeanUtils.copyProperties(roomStaByRmno, vo);

    if (masterBaseRes != null && !masterBaseRes.isEmpty()){

      vo.setMasterBaseRes(masterBaseRes.get(0));

    }

    return vo;
  }

  /**
   * 查询房态列表公用
   *
   * @param dto dto
   * @return {@link List<RoomInfoVo>}
   */
  private List<RoomInfoVo> getAllRooms(RoomStatusDto dto) {

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById((int) dto.getCompanyId());

    if(modHotelInfo == null){
      throw new RuntimeException("未查询到酒店信息");
    }

    hotelInfoCommonService.checkPmsHotelId(modHotelInfo);

    dto.setHotelId(modHotelInfo.getPmsHotelId());
    dto.setHotelCode(modHotelInfo.getHotelCode());
    dto.setCompanyId((int) dto.getCompanyId());

    return getAllRoomStaFromCrsNew(dto);
  }

  /**
   * 从绿云重新获取房态信息 (调整后调用)
   *
   * @return {@link List}<{@link RoomInfoVo}>
   */
  private List<RoomInfoVo> getAllRoomStaFromCrsNew(RoomStatusDto dto){

    List<RoomInfoVo> list = greenRoomStatusService.getAllRoomsNew2(igroupUrl, dto.getHotelGroupId(), dto.getHotelId(), dto.getHotelCode(), igroupAppKey);

    if (list.size() > 0) {

      //按状态查询
      if (StringUtils.hasText(dto.getSta())) {
        RoomStatusEnum sta = RoomStatusEnum.conversionStatu(dto.getSta());
        if (sta != null) {
          list = list.stream().filter(x -> sta.getCrsRoomStatu().equals(x.getSta()))
              .collect(Collectors.toList());
        }
      }
      //按房号查询
      if (StringUtils.hasText(dto.getRmno())) {
        list = list.stream().filter(x -> dto.getRmno().equals(x.getRmno()))
            .collect(Collectors.toList());
      }
    }

    return list;
  }

  /**
   * 从reids中获取绿云房态信息
   *
   * @param dto dto
   * @return {@link List<RoomInfoVo>}
   */
  private List<RoomInfoVo> getAllRoomStaFromRedis(RoomStatusDto dto) {

    String key = Tools.buildKey(BaseConstants.CACHE_CRS_ROOMSTA, String.valueOf(dto.getHotelId()));

    List<RoomInfoVo> list = new ArrayList<>();
    if (this.redisTemplate.hasKey(key) && this.redisTemplate.opsForValue()
        .get(key) instanceof List) {
      list = (List<RoomInfoVo>) this.redisTemplate.opsForValue().get(key);

      if (list != null && !list.isEmpty()) {
        //根据房态筛选
        if (StringUtils.hasText(dto.getSta())) {
          RoomStatusEnum statusEnum = RoomStatusEnum.conversionStatu(dto.getSta());
          if (statusEnum != null) {
            list = list.stream().filter(t -> statusEnum.getCrsRoomStatu().equals(t.getSta()))
                .collect(Collectors.toList());
          }
        }

        //根据房间号筛选
        if (StringUtils.hasText(dto.getRmno())) {
          list = list.stream().filter(t -> dto.getRmno().equals(t.getRmno()))
              .collect(Collectors.toList());
        }
      }
    }

    return list;
  }

  /**
   * 删除敏感信息
   *
   * @param list 列表
   */
  private void removeSensitiveInfo(List<RoomInfoVo> list) {
    list.forEach(t -> {
      GuestInfoVo guests = t.getGuests();
      if (guests != null) {
        doRemoveSensitive(guests);
      }
    });
  }

  private void doRemoveSensitive(GuestInfoVo guests){

    String idNo = guests.getIdNo();
    String mobile = guests.getMobile();
    guests.setName(SensitiveHelper.maskName(guests.getName()));
    if (!Strings.isNullOrEmpty(idNo)) {
      idNo = idNo.trim();
      guests.setIdNo(SensitiveHelper.maskCredentialsCard(idNo));
    }

    if (!Strings.isNullOrEmpty(mobile)) {
      guests.setMobile(SensitiveHelper.maskMobile(mobile));
    }

  }

  private String getSeesionId() {
    if ("".equals(sessionId) || Math.abs(System.currentTimeMillis() - aliveTime) >= flashtimes) {
      refreshSession();
    }
    return sessionId;
  }

  private static byte[] getSHA1Digest(String data) throws IOException {
    byte[] bytes = null;
    try {
      MessageDigest md = MessageDigest.getInstance("SHA-1");
      bytes = md.digest(data.getBytes("UTF-8"));
    } catch (GeneralSecurityException gse) {
      throw new IOException(gse.getMessage());
    }
    return bytes;
  }

  private static String byte2hex(byte[] bytes) {
    StringBuilder sign = new StringBuilder();
    for (int i = 0; i < bytes.length; i++) {
      String hex = Integer.toHexString(bytes[i] & 0xFF);
      if (hex.length() == 1) {
        sign.append("0");
      }
      sign.append(hex.toUpperCase());
    }
    return sign.toString();
  }

  private void processData(List<RoomInfoVo> list, List<HsCleanRoomTask> taskList) {

    //按房间
    Map<String, HsCleanRoomTask> roomNoMap = new HashMap<>();
    if (!taskList.isEmpty()) {
      for (HsCleanRoomTask task : taskList) {
        roomNoMap.put(task.getRoomNo(), task);
      }
    }

    list.forEach(t -> {
      //替换为本地房态
      RoomStatusEnum statusEnum = RoomStatusEnum.converLocalStatu(t.getSta());
      if (statusEnum != null) {
        t.setSta(statusEnum.getRoomStatu());
      }

      //放入当日房间清扫状态（以最新的为准）
      if (roomNoMap.containsKey(t.getRmno())) {
        HsCleanRoomTask task = roomNoMap.get(t.getRmno());
        t.setCleanStatus(task.getCleanStatus());
        t.setNoPass(task.getPass());
        t.setTaskId(task.getId());
        t.setCheckUser(task.getCheckUser());
        t.setCheckUserName(task.getCheckUserName());

      }

    });
  }

  /**
   * 过滤列表
   *
   * @param list 列表
   * @param dto  dto
   * @return {@link List<RoomInfoVo>}
   */
  private List<RoomInfoVo> filterList(List<RoomInfoVo> list, RoomStatusDto dto) {
    //宾客预离\预抵筛选
    if (dto.getGuestSta() != null && !"".equals(dto.getGuestSta())) {
      //预抵
      if ("L".equals(dto.getGuestSta())) {
        list = list.stream().filter(t -> t.getIsArr() != null && "T".equals(t.getIsArr())).collect(Collectors.toList());
      } else {
        //预离
        list = list.stream().filter(t -> t.getIsDep().equals(dto.getGuestSta()))
            .collect(Collectors.toList());
      }
    }

    //房型筛选
    if (dto.getRmtype() != null && !"".equals(dto.getRmtype())) {
      if (dto.getRmtype().contains(BaseConstants.ARRAY_SEPARATOR)) {
        String[] rmtype = dto.getRmtype().split(BaseConstants.ARRAY_SEPARATOR);
        list = list.stream().filter(t -> Arrays.asList(rmtype).contains(t.getRmtype()))
            .collect(Collectors.toList());
      } else {
        list = list.stream().filter(t -> t.getRmtype().equals(dto.getRmtype()))
            .collect(Collectors.toList());
      }
    }

    //楼宇筛选
    if (dto.getBuilding() != null && !"".equals(dto.getBuilding())) {
      if (dto.getBuilding().contains(BaseConstants.ARRAY_SEPARATOR)) {
        String[] build = dto.getBuilding().split(BaseConstants.ARRAY_SEPARATOR);
        list = list.stream().filter(t -> Arrays.asList(build).contains(t.getBuilding()))
            .collect(Collectors.toList());
      } else {
        list = list.stream().filter(t -> t.getBuilding().equals(dto.getBuilding()))
            .collect(Collectors.toList());
      }
    }

    //楼层筛选
    if (dto.getFloor() != null && !"".equals(dto.getFloor())) {
      if (dto.getFloor().contains(BaseConstants.ARRAY_SEPARATOR)) {
        String[] floor = dto.getFloor().split(BaseConstants.ARRAY_SEPARATOR);
        list = list.stream()
            .filter(t -> Arrays.asList(floor).contains(t.getBuilding() + "-" + t.getFloor()))
            .collect(Collectors.toList());
      } else {
        list = list.stream()
            .filter(t -> dto.getFloor().equals(t.getBuilding() + "-" + t.getFloor()))
            .collect(Collectors.toList());
      }
    }

    return list;
  }

  private List<Map<String, Object>> groupList(List<RoomInfoVo> list) {
    List<Map<String, Object>> newData = new ArrayList<>();
    Map<String, List<RoomInfoVo>> buildGroup = list.stream()
        .collect(Collectors.groupingBy(RoomInfoVo::getBuilding));
    for (String build : buildGroup.keySet()) {
      List<RoomInfoVo> list1 = buildGroup.get(build);
      Map<String, List<RoomInfoVo>> floor = list1.stream()
          .collect(Collectors.groupingBy(RoomInfoVo::getFloor));
      for (String key : floor.keySet()) {
        Map<String, Object> map = new HashMap<>();
        map.put("building", build);
        map.put("floor", key);
        map.put("rooms", floor.get(key));
        newData.add(map);
      }
    }
    return newData;
  }

  /**
   * 房态列表排序
   *
   * @param list 列表
   * @return {@link List<Map<String, Object>>}
   */
  private List<Map<String, Object>> orderList(List<Map<String, Object>> list) {
    list.sort(((o1, o2) -> {
      int i = String.valueOf(o1.get("building")).compareTo(String.valueOf(o2.get("building")));
      if (i == 0) {
        return String.valueOf(o1.get("floor")).compareTo(String.valueOf(o2.get("floor")));
      } else {
        return i;
      }
    }));
    return list;
  }

  /**
   * 放入楼层楼宇信息
   *
   * @param list      列表
   * @param companyId 酒店id
   */
  private void setFloorInfo(Integer companyId, List<RoomInfoVo> list){
    List<Map<String, Object>> hotelFloors = hotelFloor(companyId);

    if (hotelFloors != null && !hotelFloors.isEmpty() && !list.isEmpty()){

      //楼层信息
      Map<String, String> floorInfo = new HashMap<>();

      hotelFloors.forEach(t -> {

        List<HotelFloor> floors = JSON
            .parseArray(JSONObject.toJSONString(t.get("floor")), HotelFloor.class);
        if (CollectionUtil.isNotEmpty(floors))  {
          floorInfo.putAll(floors.stream().filter(Objects::nonNull)
              .collect(Collectors.toMap(k -> k.getBuildingCode() + "-" + k.getCode(),
                  HotelFloor::getName, (v1, v2) -> v2)));
        } else {
          log.info("[酒店{}楼层信息为空]:{}", companyId, t);
        }
      });

      //楼宇信息
      Map<Object, Map<String, Object>> buildingMap = hotelFloors.stream()
          .collect(Collectors.toMap(t -> t.get("building"), c -> c, (v1, v2) -> v2));

      for (RoomInfoVo vo : list) {

        //设置楼宇名
        if (buildingMap.containsKey(vo.getBuilding())){
          Map<String, Object> building = buildingMap.get(vo.getBuilding());
          vo.setBuildingName(building.get("name").toString());

        }

        String key = vo.getBuilding() + "-" + vo.getFloor();
        if (floorInfo.containsKey(key)){

          vo.setFloorName(floorInfo.get(key));
        }

      }
      
    }
  }
}
