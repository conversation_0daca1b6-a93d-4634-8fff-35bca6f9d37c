package com.shands.mod.main.service.acceptuser;

import com.shands.mod.dao.model.res.hs.staff.UserServerAreaRes;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 * @desc 工单受理人获取service
*/
public interface AcceptUserService {

  /**
   * 获取工单受理人
   * @param companyId 酒店编码
   * @param serviceType 服务类型
   * @param serviceExtend 服务子项
   * @param roomNumber 房间号
   * @return 受理人信息
   */
  public UserServerAreaRes getAcceptUser(Integer companyId,String serviceType,Integer serviceExtend,String roomNumber);

  /**
   * 工单创建查询默认受理人
   * @param companyId 酒店编码
   * @param serviceType 服务类型
   * @return
   */
  public UserServerAreaRes getAcceptUser(Integer companyId, String serviceType);

  /**
   * 查询默认受理人接单模式过滤
   * @param userIds
   * @return
   */
  public List<Integer> receiveFilter(List<Integer> userIds);

  public UserServerAreaRes getAcceptUserNew(Integer companyId, String serviceType, Integer serviceExtend,
      String roomNumber);
}
