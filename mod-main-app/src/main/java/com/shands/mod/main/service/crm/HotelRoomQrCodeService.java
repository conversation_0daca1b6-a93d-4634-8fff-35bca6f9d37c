package com.shands.mod.main.service.crm;

import com.shands.mod.dao.model.v0701.pojo.ZipFileDownloadnPojo;
import io.swagger.models.auth.In;
import java.io.InputStream;
import java.util.List;

public interface HotelRoomQrCodeService {

  public void getRoomFiles(String accessToken, Integer companyId);

  /**
   * 上传二维码 领券中心二维码上传
   *
   * @param in 输入流
   * @return {@link String}
   */
  String uploadQrCode(InputStream in, Integer companyId, String QrCode);

  /**
   * 菜本或者菜品 二维码上传
   * @param in
   * @param id 二维码表的id 菜本/菜项
   * @return
   */
  String uploadFoodCode(InputStream in, Integer id);

  /**
   * 上传早餐券二维码
   *
   * @param in 在
   * @return {@link String}
   */
  String uploadBreakfastQr(InputStream in);

   /* 号码牌二维码
   * @param in
   * @param id
   * @return
   */
  String numberPlateQr(InputStream in, Integer id);

  void plateZip(String zipName, List<ZipFileDownloadnPojo> filePaths,Integer companyId);

}
