package com.shands.mod.main.job;

import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.service.hotelinfo.IHotelRoomService;
import com.shands.mod.message.remote.main.MainCenterRemoteService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @author: lvyouyin
 * @date: 2023/7/20 18:01
 */
@Component
@Slf4j
public class HotelInfoJob {

  @Autowired
  private IHotelRoomService iHotelRoomService;

  @Resource
  ModHotelInfoDao modHotelInfoDao;
  @XxlJob("syncHotelInfo")
  public ReturnT<String> syncHotelInfo(String param) {

    List<ModHotelInfo> hotelInfoList;
    if (StringUtils.isNotEmpty(param)) {
      String[] hotelCodes = param.split(",");
      hotelInfoList = modHotelInfoDao.queryUserHotelInfosByCodes(Arrays.asList(hotelCodes));
    } else  {
      hotelInfoList = modHotelInfoDao.findAllHotelList();
    }

    long begin = System.currentTimeMillis();
    log.info("[德胧生态酒店信息同步][开始]");

    for (ModHotelInfo hotelInfo : hotelInfoList) {
      try {
        iHotelRoomService.getHotelInfo(hotelInfo);
        log.info("同步完成，hotelInfo={}",hotelInfo);
      } catch (Exception e) {
        log.error("同步异常，hotelInfo={}",hotelInfo, e);
      }
    }

    log.info("[德胧生态酒店信息同步][结束，耗时:{} ms]", System.currentTimeMillis() - begin);
    return ReturnT.SUCCESS;
  }
}
