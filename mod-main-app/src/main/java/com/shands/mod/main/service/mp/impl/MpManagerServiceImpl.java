package com.shands.mod.main.service.mp.impl;

import com.shands.mod.dao.mapper.mp.MpManagerDao;
import com.shands.mod.dao.model.mp.po.MpManager;
import com.shands.mod.main.service.mp.MpManagerService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 工单配置字段详情表(MpManager)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-02 19:02:56
 */
@Service("MpManagerService")
public class MpManagerServiceImpl implements MpManagerService {
    @Resource
    private MpManagerDao mpManagerDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public MpManager queryById(Integer id) {
        return this.mpManagerDao.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param mpManager 筛选条件
     * @return 查询结果
     */
    @Override
    public Page<MpManager> queryByPage(MpManager mpManager) {
        long total = this.mpManagerDao.count(mpManager);
        return new PageImpl<>(null);
    }

    /**
     * 新增数据
     *
     * @param MpManager 实例对象
     * @return 实例对象
     */
    @Override
    public MpManager insert(MpManager MpManager) {
        this.mpManagerDao.insert(MpManager);
        return MpManager;
    }

    /**
     * 修改数据
     *
     * @param MpManager 实例对象
     * @return 实例对象
     */
    @Override
    public MpManager update(MpManager MpManager) {
        this.mpManagerDao.update(MpManager);
        return this.queryById(MpManager.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.mpManagerDao.deleteById(id) > 0;
    }
}
