package com.shands.mod.main.controller.statistics;


import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.req.statChart.DeptDataReq;
import com.shands.mod.main.service.statistics.BdwCardStatisticService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/betterWoodCard")
@Api(value = "百达卡销售统计", tags = "百达卡")
@Slf4j
public class BdwCardStatisticDataController {

    @Autowired
    private BdwCardStatisticService bdwCardStatisticService;


    @ApiOperation(value = "部门相关数据")
    @PostMapping(value = "/deptData")
    @ApiResponse(code = 0, message = "成功",response = DeptDataVo.class)
    private ResultVO<List<DeptDataVo>> queryVoucherDeptData(@Valid @RequestBody DeptDataReq deptDataReq) {
        return ResultVO.success(bdwCardStatisticService.queryBdwCardDeptData(deptDataReq));
    }
}
