package com.shands.mod.main.service.betterwood;

import com.shands.mod.dao.model.catecard.req.MiniAppQrCodeReqDto;
import com.shands.mod.dao.model.catecard.resp.CateCardDetailInfoResp;
import com.shands.mod.dao.model.catecard.resp.CateCardInfoResp;
import com.shands.mod.main.service.betterwood.req.MiniAppQrCodeReq;
import com.shands.mod.main.service.betterwood.req.VoucherWriteOffReq;
import com.shands.mod.main.service.betterwood.resp.MiniAppQrCodeResp;
import java.util.List;

public interface VoucherService {

  /**
   * 查询法宝早餐
   *
   * @param orderNo 订单号
   * @param channel 渠道
   * @return 法宝凭证号列表
   */
  List<String> listAvailableCoupon(String orderNo, Integer channel);

  /**
   * 核销法宝早餐
   *
   * @param req 请求参数
   * @return 是否核销成功
   */
  boolean writeOffBreakfast(VoucherWriteOffReq req);

  /**
   * 生成权益卡的二维码信息
   * @param miniAppQrCodeReqDto MiniAppQrCodeReqDto
   * @return
   */
  MiniAppQrCodeResp createQrCode(MiniAppQrCodeReq miniAppQrCodeReq);


  /**
   * 百达卡信息
   * @param salesChannel 销售渠道
   * @return
   */
  List<CateCardInfoResp> queryCardList(String salesChannel,Integer channelStatus);

  /**
   * 获取权益卡信息
   * @param cardId
   * @return
   */
  CateCardDetailInfoResp queryCardDetail(Long cardId);
}
