package com.shands.mod.main.controller.checkinservice.hotelservice;


import com.alibaba.fastjson.JSONObject;
import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.model.req.hs.crm.HsFoodQrCodeReq;
import com.shands.mod.dao.model.req.hs.crm.OutletByUrlReq;
import com.shands.mod.main.service.crm.IHsPosFoodQrCodeService;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Map;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@Api("菜本/菜项 二维码相关")
@RestController
@RequestMapping("/hsFoodQrCode")
@Deprecated
public class HsPosFoodQrCodeController extends BaseController {

  @Override
  public boolean isPublic() {
    return true;
  }

  @Autowired
  IHsPosFoodQrCodeService qrCodeService;

  /**
   * 二维码生成及下载
   *
   * @param req
   * @return
   */
  @PostMapping("/getByCode")
  @ApiOperation("菜本或菜品下载二维码")
    @ResultLog(name = "HsPosFoodQrCodeController.qrCodeUrl", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO qrCodeUrl(@Valid @RequestBody HsFoodQrCodeReq req) {
    req.setCompanyId(ThreadLocalHelper.getCompanyId());
    return ResultVO.success(qrCodeService.qrCodeUrl(req));
  }

  @PostMapping("/detailById")
  @ApiOperation("二维码解析")
    @ResultLog(name = "HsPosFoodQrCodeController.qrCodeUrl", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO qrCodeUrl(@RequestBody Map<String, Integer> map) {
    if (map.isEmpty() || map.get("id") == null) {
      return ResultVO.failed("请求参数为空");
    }
    Integer id = map.get("id");
    return ResultVO.success(qrCodeService.selectByPrimaryKey(id));
  }

  /**
   * 根据二维码，查询营业点
   *
   * @param req
   * @return
   */
  @PostMapping("outletByUrl")
  @ApiOperation("根据二维码，查询营业点")
    @ResultLog(name = "HsPosFoodQrCodeController.outletByUrl", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO outletByUrl(@Valid @RequestBody OutletByUrlReq req) {
    return ResultVO.success(qrCodeService.outletByUrl(req));
  }

  /**
   * 根据二维码,营业点查询菜项分类
   *
   * @return
   */
  @PostMapping("pluSortByUrl")
  @ApiOperation("根据二维码,营业点查询菜项分类")
    @ResultLog(name = "HsPosFoodQrCodeController.pluSortByNoteUrl", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO pluSortByNoteUrl(@Valid @RequestBody OutletByUrlReq req) {
    return ResultVO.success(qrCodeService.pluSortByNoteUrl(req));
  }

  /**
   * 根据二维码,营业点,菜项分类 查询菜项
   *
   * @param req
   * @return
   */
  @PostMapping("pluByUrl")
  @ApiOperation("根据二维码,营业点,菜项分类 查询菜项")
    @ResultLog(name = "HsPosFoodQrCodeController.pluByNUrl", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO pluByNUrl(@Valid @RequestBody OutletByUrlReq req) {
    return ResultVO.success(qrCodeService.urlForPlu(req));
  }

}


