package com.shands.mod.main.service.dataBoard;

import com.shands.mod.dao.model.req.Board.CleanRoomDataReq;
import com.shands.mod.dao.model.req.Board.UpdataDataBoardRoleReq;
import com.shands.mod.dao.model.res.board.DataBoardListRes;
import com.shands.mod.dao.model.res.board.DataBoardRes;
import com.shands.mod.dao.model.res.board.KyBizReportRes;
import com.shands.mod.dao.model.res.board.ModDataBoardListRes;
import java.util.List;

public interface DataBoardService {

  /**
   * 数据看板
   *
   * @return
   */
  List<DataBoardRes> dataBoardRes(CleanRoomDataReq req,List<String> roles);

  //修改顺序及选择的数据
  int updateBoard(List<String> req, Integer userId);

  //app端已选择和未选择
  DataBoardListRes boardEnumList(Integer userId, List<String> roles);

  //角色配置中 数据看板配置列表
  List<ModDataBoardListRes> queryAll();

  List<ModDataBoardListRes> dataBoardByRoleId(Integer roldId);

  boolean updateByRole(UpdataDataBoardRoleReq req);

  /**
   * 获取营业数据
   *
   * @param hotelCode 酒店的代码
   * @param day       时间（yyyy-mm-dd）
   * @return {@link KyBizReportRes}
   */
  KyBizReportRes getReportData(String hotelCode, String day);
}
