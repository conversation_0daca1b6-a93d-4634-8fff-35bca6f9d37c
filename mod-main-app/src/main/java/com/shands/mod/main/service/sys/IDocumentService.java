package com.shands.mod.main.service.sys;

import com.shands.mod.dao.model.Document;
import com.shands.mod.dao.model.res.hs.hotel.QrCodeDLRes;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2020/9/24
 * @desc 文件上传service
*/
public interface IDocumentService {
  /**
   * 保存上传文件
   *
   * @param refTable 关联表
   * @param refId 关联ID
   * @param file 上传文件
   * @return
   */
  String saveFile(String refTable, String refId, MultipartFile file);

  /**
   * 文件列表
   *
   * @param refTable 关联表
   * @param refId 关联ID
   * @return
   */
  List<Document> list(String refTable, String refId);

  /**
   * 查找有没有已经上传的二维码图片
   * @param req
   * @return
   */
  String getUrlByRoom(Integer req);

  List<QrCodeDLRes> getAllUrl(Integer companyId,String roomNum);
}
