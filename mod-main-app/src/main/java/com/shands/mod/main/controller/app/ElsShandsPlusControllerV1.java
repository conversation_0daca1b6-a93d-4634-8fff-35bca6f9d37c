package com.shands.mod.main.controller.app;

import cn.hutool.core.net.url.UrlQuery;
import com.betterwood.log.core.annotation.ResultLog;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.merchant.api.res.HotelInfoDTORes;
import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.sales.tool.dto.WechatParameters;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.external.model.dto.QRCodeDto;
import com.shands.mod.external.model.vo.ActivityShowPosterVo;
import com.shands.mod.external.model.vo.DevelopmentQrCodeVo;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.config.DevelopSwitchConfig;
import com.shands.mod.main.remote.merchant.MerchantServiceClient;
import com.shands.mod.main.service.betterwood.rpc.BdxRpcService;
import com.shands.mod.main.service.common.CommonService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/6/2
 * @desc ELS 德胧生态会员中心controller
 */
@RestController
@RequestMapping(value = "/shandsPlus/public")
@Api(value = "APP", tags = "德胧生态会员中心-公开接口")
@Slf4j
public class ElsShandsPlusControllerV1 extends BaseController {

  @Autowired
  private ElsShandsPlusController elsShandsPlusController;

  @Autowired
  private DevelopSwitchConfig developSwitchConfig;

  @Autowired
  private BdxRpcService bdxRpcService;
  @Autowired
  private CommonService commonService;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;


  @Autowired
  private BetterwoodConfig betterwoodConfig;
  @Autowired
  private MerchantServiceClient merchantServiceClient;

  @Override
  public boolean isPublic() {
    return true;
  }

  @PostMapping("/getMemberCode")
  @ApiOperation(value = "获取扇微临时二维码")
  @ResultLog(name = "ElsShandsPlusControllerV1.getMemberCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<DevelopmentQrCodeVo> getMemberCode(@RequestBody QRCodeDto qrCodeDto) {
    if (developSwitchConfig.isReadFlag()) {
      return ResultVO.success(bdxRpcService.getMiniAppMemberCode(qrCodeDto));
    }
    qrCodeDto.setFromFeiShu(1);
    return elsShandsPlusController.getMemberCode(qrCodeDto);
  }


  @GetMapping("/getActivityShowPoster")
  @ApiOperation(value = "小程序海报列表")
  @ResultLog(name = "ElsShandsPlusControllerV1.getActivityShowPoster", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<ActivityShowPosterVo>> getActivityShowPoster(String ucId) {
    return ResultVO.success(bdxRpcService.getActivityShowPoster(ucId));
  }




  @PostMapping("/shareMemberCode")
  @ApiOperation(value = "分享小程序二维码")
  @ResultLog(name = "ElsShandsPlusControllerV1.shareMemberCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<WechatParameters> shareMemberCode(@RequestBody QRCodeDto qrCodeDto) {

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    HotelInfoDTORes hotelInfoDTORes = new HotelInfoDTORes();
    String path = betterwoodConfig.getBetterwoodMiniCodePage();
    //集团
    if("000001".equals(modHotelInfo.getHotelCode())) {
      hotelInfoDTORes.setHotelName(betterwoodConfig.getBetterwoodShareMemberCodeTitle());
      path = betterwoodConfig.getBetterwoodMiniCodeIndexPage();
    } else {
      hotelInfoDTORes = merchantServiceClient.queryAllHotelInfoByCode(
          modHotelInfo.getHotelCode());
      if(null == hotelInfoDTORes) {
        hotelInfoDTORes = new HotelInfoDTORes();
        path = betterwoodConfig.getBetterwoodMiniCodeIndexPage();
      }
      if(StringUtils.isBlank(hotelInfoDTORes.getHotelName())){
        hotelInfoDTORes.setHotelName(betterwoodConfig.getBetterwoodShareMemberCodeTitle());
      }
    }
    WechatParameters parameters = new WechatParameters();
    try {
      Map<String, String> map = new HashMap<>();
      map.put("eventType", qrCodeDto.getEventType());
      map.put("eventId", qrCodeDto.getEventId());
      map.put("salesId", qrCodeDto.getSalesId());
      if(null != hotelInfoDTORes.getHotelId()) {
        map.put("hotelId", hotelInfoDTORes.getHotelId().toString());
      }
      UrlQuery urlQuery = new UrlQuery().addAll(map);
      path = path.concat("?").concat(urlQuery.toString());
    } catch (Exception e) {
      log.error("WechatStrategy.execute error: ", e);
    }
    parameters.setPath(path);
    parameters.setTitle(hotelInfoDTORes.getHotelName());
    parameters.setDescription(hotelInfoDTORes.getHotelName());
    parameters.setImageUrl(StringUtils.isBlank(hotelInfoDTORes.getCoverImage()) ? betterwoodConfig.getBetterwoodShareMemberCodeImageUrl() : hotelInfoDTORes.getCoverImage());

    return ResultVO.success(parameters);


  }



  @GetMapping("/getDisplayText")
  @ApiOperation(value = "获取配置文本信息")
  @ResultLog(name = "ElsShandsPlusControllerV1.getDisplayText", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<String> getDisplayText(@RequestParam("type") String type) {
    return ResultVO.success(commonService.getDisplayText(type));
  }

  @PostMapping("/getHotelDevelopCode")
  @ApiOperation(value = "获取酒店发展二维码")
  @ResultLog(name = "ElsShandsPlusControllerV1.getHotelDevelopCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<DevelopmentQrCodeVo> getHotelDevelopCode() {
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    QRCodeDto qrCodeDto = new QRCodeDto();
    qrCodeDto.setHotelCode(modHotelInfo.getHotelCode());
    return ResultVO.success(bdxRpcService.getMiniAppMemberCode(qrCodeDto));
  }

}
