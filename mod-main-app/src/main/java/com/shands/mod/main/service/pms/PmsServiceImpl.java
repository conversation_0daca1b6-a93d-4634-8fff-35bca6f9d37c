package com.shands.mod.main.service.pms;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.betterwood.log.core.util.TraceUtil;
import com.google.common.collect.Sets;
import com.shands.hotel.go.pms.domain.model.res.master.QueryCurrentCheckInGuestsRes;
import com.shands.hotel.go.pms.openapi.httpclient.IRoomOrderClient;
import com.shands.hotel.go.pms.openapi.httpclient.impl.RoomOrderClientImpl;
import com.shands.mod.dao.mapper.ModFollowHotelDao;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.ModFollowHotel;
import com.shands.mod.dao.model.pms.CityHotelBo;
import com.shands.mod.dao.model.pms.CityHotelDetailsBo;
import com.shands.mod.dao.model.pms.CityHotelVo;
import com.shands.mod.dao.model.pms.FollowHotelBo;
import com.shands.mod.dao.model.pms.PmsQueryCityRsvRmTypeBo;
import com.shands.mod.dao.model.pms.PmsQueryCityRsvRmTypeDetailsVo;
import com.shands.mod.dao.model.pms.PmsQueryCityRsvRmTypePageVo;
import com.shands.mod.dao.model.pms.PmsQueryCityRsvRmTypeTotalBo;
import com.shands.mod.dao.model.pms.PmsQueryCityRsvRmTypeTotalVo;
import com.shands.mod.dao.model.pms.PmsQueryCityRsvRmTypeVo;
import com.shands.mod.dao.model.pms.PmsRsvRmDetailsVo;
import com.shands.mod.dao.model.sales.tool.req.GetOrSaveEnterpriseReq;
import com.shands.mod.main.service.app.TbBindingPmsService;
import com.shands.mod.main.service.betterwood.MagicWeaponService;
import com.shands.mod.main.service.development.DevelopmentService;
import com.shands.mod.main.util.DateUtil;
import com.shands.mod.main.util.HttpClientUtil;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.dao.model.sales.tool.res.AgreementEnterpriseRes;
import com.shands.mod.main.vo.HotelCurrentOrderQueryVo;
import com.shands.mod.main.vo.HotelJudgeVo;
import com.shands.mod.main.vo.MemberInfo;
import com.shands.mod.main.vo.PmsCurrentCheckInOrder;
import com.shands.mod.main.vo.PmsVersionResp;
import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.shands.mod.main.vo.feign.RpcHttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
@Slf4j
public class PmsServiceImpl implements IPmsService {

  @Autowired
  private ModFollowHotelDao modFollowHotelDao;
  @Autowired
  private ModHotelInfoDao modHotelInfoDao;
  @Autowired
  private TbBindingPmsService tbBindingPmsService;
  @Value("${stockWebUrl}")
  private String stockWebUrl;
  @Value("${merchantService.url}")
  private String merchantServiceUrl;
  @Value("${crs.igroup.url}")
  private String pmsOrderQueryDomain;
  @Value("${crs.igroup.key}")
  private String pmsKey;
  @Value("${crs.hotelGroupCode:NEWCENTURY}")
  private String hotelGroupCode;

  @Value("${crs.igroup.pmsstaurl:http://***********:9090}")
  private String newPMSStaUrl;
  @Resource
  private MagicWeaponService magicWeaponService;
  @Resource
  private DevelopmentService developmentService;

  @Value("${betterwood-config.betterwoodPrivateKey}")
  private String betterwoodPrivateKey;


  /**
   * 百达星会员信息字段"records"
   */
  private static final String DATA = "data";

  @Override
  public ModFollowHotel followHotel(FollowHotelBo followHotelBo) {
    if (followHotelBo.isFlag()) {
      ModFollowHotel modFollowHotel = ModFollowHotel.builder()
          .hotelCode(followHotelBo.getHotelCode())
          .userId(ThreadLocalHelper.getUser().getId()).build();
      modFollowHotelDao.insert(modFollowHotel);
      return modFollowHotel;
    } else {
      modFollowHotelDao.deleteById(ThreadLocalHelper.getUser().getId(),
          followHotelBo.getHotelCode());
      ModFollowHotel modFollowHotel = ModFollowHotel.builder()
          .userId(ThreadLocalHelper.getUser().getId())
          .hotelCode(followHotelBo.getHotelCode()).build();
      return modFollowHotel;
    }
  }

  @Override
  public PmsQueryCityRsvRmTypePageVo localCityHotelList(CityHotelBo cityHotelBo) {
    cityHotelBo.setUserId(ThreadLocalHelper.getUser().getId());
    List<CityHotelVo> cityHotelVoList = null;
    if (cityHotelBo.isIfFollow()) {
      cityHotelVoList = modHotelInfoDao.followHotelList(cityHotelBo.getHotelCode(),
          cityHotelBo.getUserId());
    } else {
      cityHotelVoList = modHotelInfoDao.localCityHotelList(cityHotelBo.getHotelCode(),
          cityHotelBo.getUserId(),
          cityHotelBo.getHotelName());
    }
    List<String> pmsHotelList = new ArrayList<>();
    if (cityHotelVoList != null && cityHotelVoList.size() > 0) {
      cityHotelVoList.forEach(followHotelVo -> {
        pmsHotelList.add(followHotelVo.getHotelCode());
      });
    }

    String api = stockWebUrl+ "/stock_web/v1/queryCityRsvRmTypeTotal";

    LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
    log.info("日期" + DateUtil.localDateTimeToStr(today));
//    ObtainTokenVo obtainTokenVo = tbBindingPmsService.obtainTokenByMobile(obtainTokenBo);
    PmsQueryCityRsvRmTypeTotalBo pmsQueryCityRsvRmTypeTotalBo = PmsQueryCityRsvRmTypeTotalBo.builder()
        .hotelCodeList(pmsHotelList)
        .hotelGroupCode(hotelGroupCode)//hotelGroupCode 测试传HOTELGO  生产传NEWCENTURY
        .occDate(DateUtil.localDateTimeToStr(today))
        .pageNo(cityHotelBo.getPageNo())
        .pageSize(cityHotelBo.getPageSize())
        .build();
    HttpHeaders headers = new HttpHeaders();
//    headers.add("uc-token", cityHotelBo.getToken());
//    headers.add("X-HOTEL-CODE", cityHotelBo.getHotelCode());
    headers.add("trace-id", TraceUtil.getTraceIdFromMdc());
    headers.add("from", "mod-main-app");
//    headers.add("uc-application", "pms-service");

    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    HttpEntity<PmsQueryCityRsvRmTypeTotalBo> entity = new HttpEntity<>(pmsQueryCityRsvRmTypeTotalBo,
        headers);
    ParameterizedTypeReference<PmsQueryCityRsvRmTypeTotalVo> ptr =
        new ParameterizedTypeReference<PmsQueryCityRsvRmTypeTotalVo>() {
        };
    log.info("pms同城房态列表查询接口地址----->" + api.toString());
    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<PmsQueryCityRsvRmTypeTotalVo> exchange = restTemplate
        .exchange(api, HttpMethod.POST, entity, ptr);
    PmsQueryCityRsvRmTypeTotalVo body = exchange.getBody();
    log.info("pms同城房态列表返回----->" + body.toString());
    log.info(" localCityHotelList http.return={}", JSONObject.toJSONString(body));
    if (body.getCode().equals(1)) {
      PmsQueryCityRsvRmTypePageVo data = body.getData();
      List<PmsQueryCityRsvRmTypeVo> list = data.getList();
      log.info(list.toString());
      PmsQueryCityRsvRmTypeVo thisHotel = new PmsQueryCityRsvRmTypeVo();
      List<PmsQueryCityRsvRmTypeVo> reList = new ArrayList<>();

      PmsQueryCityRsvRmTypeVo thisHotel1 = getThisHotel(cityHotelBo);
      list.add(thisHotel1);
      if (list != null && list.size() > 0) {
        for (PmsQueryCityRsvRmTypeVo pmsQueryCityRsvRmTypeVo : list) {
          if (pmsQueryCityRsvRmTypeVo.getHotelCode().equals(cityHotelBo.getHotelCode())) {
            thisHotel = pmsQueryCityRsvRmTypeVo;
          }
          for (CityHotelVo cityHotelVo : cityHotelVoList) {
            if (pmsQueryCityRsvRmTypeVo.getHotelCode().equals(cityHotelVo.getHotelCode())) {
              pmsQueryCityRsvRmTypeVo.setHotelName(cityHotelVo.getHotelName());
              pmsQueryCityRsvRmTypeVo.setSaleNum(
                  pmsQueryCityRsvRmTypeVo.getTotalNum() - pmsQueryCityRsvRmTypeVo.getSaleBookNum()
              - pmsQueryCityRsvRmTypeVo.getOooNum());
              if(0 == pmsQueryCityRsvRmTypeVo.getTotalNum()) {
                pmsQueryCityRsvRmTypeVo.setOcc(new BigDecimal(0));
              } else {
                pmsQueryCityRsvRmTypeVo.setOcc(
                    new BigDecimal(pmsQueryCityRsvRmTypeVo.getSaleBookNum()).divide(
                        new BigDecimal(pmsQueryCityRsvRmTypeVo.getTotalNum()), 4,
                        BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
              }
              pmsQueryCityRsvRmTypeVo.setFollowId(cityHotelVo.getFollowId());
              pmsQueryCityRsvRmTypeVo.setAddress(cityHotelVo.getAddress());
            }
          }
          if (!pmsQueryCityRsvRmTypeVo.getHotelCode().equals(cityHotelBo.getHotelCode())) {
            reList.add(pmsQueryCityRsvRmTypeVo);
          }
        }
      }
      data.setList(reList);
      data.setThisHotel(thisHotel);
      data.setTotal(data.getTotal() > 0 ? data.getTotal() - 1 : data.getTotal());
      return data;
    }
    return null;
  }

  @Override
  public List<PmsRsvRmDetailsVo> localCityHotelDetails(CityHotelDetailsBo cityHotelDetailsBo) {
    //hotelGroupCode 测试传HOTELGO  生产传NEWCENTURY
    String api = stockWebUrl+ "/stock_web/v1/findRsvRmtypeTotalList?hotelCode={hotelCode}&hotelGroupCode={hotelGroupCode}&occDate={occDate}";
    LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
    log.info("日期" + DateUtil.localDateTimeToStr(today));
    URI uri = UriComponentsBuilder
        .fromUriString(api)
        .build(cityHotelDetailsBo.getHotelCode(), hotelGroupCode, DateUtil.localDateTimeToStr(today));

    HttpHeaders headers = new HttpHeaders();
//    headers.add("uc-token",
//        cityHotelDetailsBo.getToken());
//    headers.add("X-HOTEL-CODE", cityHotelDetailsBo.getHotelCode());
//    headers.add("uc-application", "pms-service");
    headers.add("from", "mod-main-app");
    headers.add("trace-id", TraceUtil.getTraceIdFromMdc());
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    HttpEntity<PmsQueryCityRsvRmTypeBo> entity = new HttpEntity<>(null,
        headers);
    ParameterizedTypeReference<PmsQueryCityRsvRmTypeDetailsVo> ptr =
        new ParameterizedTypeReference<PmsQueryCityRsvRmTypeDetailsVo>() {
        };

    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<PmsQueryCityRsvRmTypeDetailsVo> exchange = restTemplate
        .exchange(uri, HttpMethod.GET, entity, ptr);
    PmsQueryCityRsvRmTypeDetailsVo body = exchange.getBody();
    log.info("pms同城房态列表详情uri={},entity={},返回={}",uri, entity,body.toString());
    if (body.getCode().equals(1)) {
      List<PmsRsvRmDetailsVo> data = body.getData();
      PmsRsvRmDetailsVo total = PmsRsvRmDetailsVo.builder().build();
      Integer totalRmt = 0;
      Integer totalSaleBookNum = 0;
      Integer totalSaleNum = 0;
      for (PmsRsvRmDetailsVo datum : data) {
//        datum.setSaleBookNum(datum.getOooNum() + datum.getSureBookNum() + datum.getUnsureBookNum()
//            + datum.getVillaNum() + datum.getHaltNum());
        datum.setSaleBookNum(datum.getSureBookNum() + datum.getUnsureBookNum()
            + datum.getVillaNum() + datum.getHaltNum());

        datum.setSaleNum(datum.getRmtypeNum() - datum.getSaleBookNum() - datum.getOooNum());
        if (datum.getRmtypeNum().equals(0)) {
          datum.setOcc(BigDecimal.ZERO);
        } else {
          datum.setOcc(
              new BigDecimal(datum.getSaleBookNum()).divide(new BigDecimal(datum.getRmtypeNum()), 4,
                  BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
        }
        totalRmt += datum.getRmtypeNum();
        totalSaleBookNum += datum.getSaleBookNum();
        totalSaleNum += datum.getSaleNum();
      }
      total.setRmtype("总计");
      total.setRmtypeNum(totalRmt);
      total.setSaleBookNum(totalSaleBookNum);
      total.setSaleNum(totalSaleNum);
      log.info(" localCityHotelDetails http.return={}", JSONObject.toJSONString(body));
      if (totalRmt.equals(0)) {
        total.setOcc(BigDecimal.ZERO);
      } else {
        total.setOcc(new BigDecimal(totalSaleBookNum).divide(new BigDecimal(totalRmt), 4,
            BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
      }
      data.add(0, total);
      return data;
    }
    return null;
  }

  private PmsQueryCityRsvRmTypeVo getThisHotel(CityHotelBo cityHotelBo) {
    //获取本酒店
    String api = stockWebUrl + "/stock_web/v1/queryCityRsvRmTypeTotal";
    LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
    List<String> nowHotelList = new ArrayList<>();
    nowHotelList.add(cityHotelBo.getHotelCode());
    PmsQueryCityRsvRmTypeTotalBo pmsQueryCityRsvRmTypeTotalBo = PmsQueryCityRsvRmTypeTotalBo.builder()
        .hotelCodeList(nowHotelList)
        .hotelGroupCode(hotelGroupCode)//hotelGroupCode 测试传HOTELGO  生产传NEWCENTURY
        .occDate(DateUtil.localDateTimeToStr(today))
        .pageNo(1)
        .pageSize(10)
        .build();
    HttpHeaders headers = new HttpHeaders();
//    headers.add("uc-token", cityHotelBo.getToken());
//    headers.add("X-HOTEL-CODE", cityHotelBo.getHotelCode());
//    headers.add("uc-application", "pms-service");
    headers.add("trace-id", TraceUtil.getTraceIdFromMdc());
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    HttpEntity<PmsQueryCityRsvRmTypeTotalBo> entity = new HttpEntity<>(pmsQueryCityRsvRmTypeTotalBo,
        headers);
    ParameterizedTypeReference<PmsQueryCityRsvRmTypeTotalVo> nowPtr =
        new ParameterizedTypeReference<PmsQueryCityRsvRmTypeTotalVo>() {
        };
    log.info("pms同城房态列表查询接口地址----->" + api.toString());
    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<PmsQueryCityRsvRmTypeTotalVo> nowHotel = restTemplate
        .exchange(api, HttpMethod.POST, entity, nowPtr);
    PmsQueryCityRsvRmTypeTotalVo body = nowHotel.getBody();
    log.info(" getThisHotel http.return={}", JSONObject.toJSONString(body));
    if (body.getCode().equals(1)) {
      PmsQueryCityRsvRmTypePageVo data = body.getData();
      List<PmsQueryCityRsvRmTypeVo> list1 = data.getList();
      if (list1 != null && list1.size() > 0) {
        for (PmsQueryCityRsvRmTypeVo pmsQueryCityRsvRmTypeVo : list1) {
          log.info("同城房态本酒店----->" + pmsQueryCityRsvRmTypeVo);
          return pmsQueryCityRsvRmTypeVo;
        }
      }
    }
    return new PmsQueryCityRsvRmTypeVo();
  }

  @Override
  public boolean judgeOldPmsHotel(String hotelCode, String token) {
    String api = merchantServiceUrl + "/merchant_web/v1/hotel_config/getPmsVersionByHotelCode";
    HotelJudgeVo hotelJudgeVo = HotelJudgeVo.builder()
        .hotelCodes(Sets.newHashSet(hotelCode))
        .build();
    HttpHeaders headers = new HttpHeaders();
    headers.add("from", "mod-main-app");
    headers.add("trace-id", TraceUtil.getTraceIdFromMdc());
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    HttpEntity<HotelJudgeVo> entity = new HttpEntity<>(hotelJudgeVo, headers);
    ParameterizedTypeReference<PmsVersionResp> nowPtr = new ParameterizedTypeReference<PmsVersionResp>() {};
    log.info("pms酒店实时入住订单接口查询地址----->" + api);
    RestTemplate restTemplate = new RestTemplate();
    ResponseEntity<PmsVersionResp> nowHotel = restTemplate
        .exchange(api, HttpMethod.POST, entity, nowPtr);
    PmsVersionResp body = nowHotel.getBody();
    if (Objects.isNull(body) || Objects.isNull(body.getData())) {
      return false;
    }
    log.info(" judgeOldPmsHotel http.return={}", JSONObject.toJSONString(body));
    return body.getData().stream().anyMatch(version -> "1".equals(version.getVershion()));
  }

  @Override
  public List<PmsCurrentCheckInOrder> getCurrentCheckInOrderList(String bizDate, String hotelCode,
      boolean filterNonMember) {
    // 1、查询订单列表
    IRoomOrderClient iRoomOrderClient = new RoomOrderClientImpl();
    String temp = JSONObject.toJSONString(iRoomOrderClient.queryCurrentCheckInGuests(
        pmsOrderQueryDomain,
        pmsKey,
        hotelCode));
    List<QueryCurrentCheckInGuestsRes> tempResult = JSONObject.parseArray(temp, QueryCurrentCheckInGuestsRes.class);
    List<PmsCurrentCheckInOrder> result = PmsCurrentCheckInOrder.transferToOrder(tempResult);
    // 2、查询是否会员信息
    List<String> mobileList = result.stream().map(PmsCurrentCheckInOrder::getCustomerPhone)
        .filter(StringUtils::hasLength)
        .collect(Collectors.toList());
    JSONObject infoListJsonResult = magicWeaponService.batchQueryMemberInfo(mobileList);
    Map<String, String> mobileToMemberLevelMap = new HashMap<>();
    if (Objects.nonNull(infoListJsonResult) && infoListJsonResult.containsKey(DATA)) {
      JSONArray records = infoListJsonResult.getJSONArray(DATA);
      List<MemberInfo> memberInfos = JSONObject.parseArray(JSONObject.toJSONString(records),
          MemberInfo.class);
      if (!CollectionUtils.isEmpty(memberInfos)) {
        mobileToMemberLevelMap = memberInfos.stream().collect(
            Collectors.toMap(MemberInfo::getMobile, MemberInfo::getTimeLevelName, (v1, v2) -> v1));
      }
    }
    // 同步会员信息
    setMemberLevelInfo(result, mobileToMemberLevelMap);
    // 非会员筛选，只返回非会员客人信息
    if (filterNonMember) {
      result = result.stream().filter(order -> !order.isMember()).collect(Collectors.toList());
    }
    return result;
  }

  private void setMemberLevelInfo(List<PmsCurrentCheckInOrder> orders, Map<String, String> mobileToMemberLevelMap) {
    orders.forEach(order -> {
      if (mobileToMemberLevelMap.containsKey(order.getCustomerPhone())) {
        order.setMember(Boolean.TRUE);
        order.setCustomerMemberLevel(mobileToMemberLevelMap.get(order.getCustomerPhone()));
      } else {
        order.setMember(Boolean.FALSE);
      }
    });
  }

  /**
   * 查询百达星会员信息
   *
   * @param token 百达星token
   * @param hotelCode 酒店code
   * @param mobile 手机号
   * @param idNo 身份证号
   * @return 会员信息
   */
  private JSONObject getBdxMemberInfo(String token, String hotelCode, String mobile, String idNo) {
    JSONObject jsonObject = magicWeaponService.qurMemberInfoWithToken(token, hotelCode, mobile, idNo, 0, 1);
    return jsonObject.getJSONObject("data");
  }

  @Override
  public JSONObject exportMemberInfo(HotelCurrentOrderQueryVo queryVo) {
    List<PmsCurrentCheckInOrder> result = getCurrentCheckInOrderList(queryVo.getBizDate(), queryVo.getHotelCode(),
        queryVo.isFilterNonMember());
    return JSONObject.parseObject(JSONObject.toJSONString(result));
  }

  @Override
  public List<String> getProtocolNoBySaleManId(Integer saleManId) {
    String url = newPMSStaUrl + "/state_web/agreementEnterprise/agreementBySaleManId";
    Map<String, String> params = new HashMap<>();
    params.put("saleManId", String.valueOf(saleManId));
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      if (jsonObject != null && jsonObject.getBoolean("successWithData") &&
          jsonObject.getJSONArray("data") != null) {
        List<AgreementEnterpriseRes> AgreementBySaleManIdVoList = jsonObject.getJSONArray("data").toJavaList(AgreementEnterpriseRes.class);

        return AgreementBySaleManIdVoList.stream().map(AgreementEnterpriseRes::getAgreementNo).collect(Collectors.toList());
      }
    } catch (Exception e) {
      log.error("pms查询错误 getProtocolNoBySaleManId", e);
    }
    return new ArrayList<>();
  }

  @Override
  public AgreementEnterpriseRes getAssociationProtocol(GetOrSaveEnterpriseReq getOrSaveEnterpriseReq) {
    String url = newPMSStaUrl + "/state_web/agreementEnterprise/getOrSaveEnterprise";
    String requestBody = JSONUtil.toJsonStr(getOrSaveEnterpriseReq);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      log.info("getAssociationProtocol http.return={}", JSONObject.toJSONString(httpResponse));
      if (httpResponse != null && httpResponse.isOk()) {
        JSONObject jsonObject = JSONObject.parseObject(httpResponse.getBody());
        if (jsonObject != null && jsonObject.getJSONObject("data") != null) {
          return jsonObject.getJSONObject("data").toJavaObject(AgreementEnterpriseRes.class);
        }
      }
    } catch (Exception e) {
      log.error("pms查询错误 getAssociationProtocol", e);
    }
    return null;
  }

  @Override
  public List<AgreementEnterpriseRes> getAllLegaEnterprise(String hotelCode) {
    String url = newPMSStaUrl + "/state_web/agreementEnterprise/getHotelCodeList";
    Map<String, String> params = new HashMap<>();
    params.put("hotelCode", hotelCode);
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      log.info("getAllLegaEnterprise http.return={}", JSONObject.toJSONString(jsonObject));
      if (jsonObject != null && jsonObject.getBoolean("successWithData") &&
          jsonObject.getJSONArray("data") != null) {
          return jsonObject.getJSONArray("data").toJavaList(AgreementEnterpriseRes.class);
      }
    } catch (Exception e) {
      log.error("pms查询错误 getProtocolNoBySaleManId", e);
    }
    return new ArrayList<>();
  }
}
