package com.shands.mod.main.service.message;

/**
 * <AUTHOR>
 * @date 2020/7/7
 * @desc 消息发送类型定义枚举类
*/
public enum MessageStateEnum {

  /** 消息发送状态 */
  SEND_SUCCESS(1,"发送成功"),

  SEND_FAIL(0,"发送失败"),

  /** 消息读取状态 */
  READ_IS(1,"已读"),

  READ_UN(0,"未读"),

  ;

  /**
   * 状态码
   */
  private int stateCode;

  /**
   * 状态名称
   */
  private String stateName;

  MessageStateEnum(int stateCode, String stateName) {
    this.stateCode = stateCode;
    this.stateName = stateName;
  }

  public int getStateCode() {
    return stateCode;
  }

  public void setStateCode(int stateCode) {
    this.stateCode = stateCode;
  }

  public String getStateName() {
    return stateName;
  }

  public void setStateName(String stateName) {
    this.stateName = stateName;
  }

  public static MessageStateEnum getMessageStateEnum(int stateCode) {
    for (MessageStateEnum item : MessageStateEnum.values()) {
      if (item.stateCode == stateCode) {
        return item;
      }
    }
    return null;
  }

  @Override
  public String toString() {
    return "MessageStateEnum{" +
        "stateCode=" + stateCode +
        ", stateName='" + stateName + '\'' +
        '}';
  }
}
