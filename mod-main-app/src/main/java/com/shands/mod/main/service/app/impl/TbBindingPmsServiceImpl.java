package com.shands.mod.main.service.app.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.app.ObtainTokenBo;
import com.shands.mod.dao.model.app.ObtainTokenVo;
import com.shands.mod.dao.model.app.CompanyRoles;
import com.shands.mod.dao.model.app.UserDetailsInfoVo;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.service.app.TbBindingPmsService;
import com.shands.mod.main.util.HttpClientUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.CommonConstants;
import com.shands.mod.util.JwtUtils;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.UserInfoVO;
import com.shands.uc.base.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/9/26
 **/
@Slf4j
@Service
public class TbBindingPmsServiceImpl implements TbBindingPmsService {
  private static final String PMS_CODE = "code";
  private static final String PMS_DATA = "data";
  private static final String PMS_MSG = "msg";
  private static final String ROLE_NAME = "name";
  private static final String ROLE_CHILDREN = "children";

  @Resource
  private ModUserDao modUserDao;

  @Resource
  private ModHotelInfoDao hotelInfoDao;

  @Value("${pms-config.pmsApiUrl}")
  private String pmsApiUrl;
  @Resource
  private RedisTemplate redisTemplate;

  /**
   * GET请求数据
   * @param url 相对地址
   * @param params 地址参数
   * @param token
   * @return
   */
  private JSONObject getData(String url, Map<String, String> params, String token, String pmsSource) {
    JSONObject result = null;
    Map<String, String> headers = new HashMap<>();
    headers.put("uc-application","pms-service");
    if (StringUtils.isNotBlank(token)) {
      headers.put("uc-token",token);
    }
    if (StringUtils.isNotBlank(pmsSource)) {
      headers.put("x-pms-source", pmsSource);
    }

    log.info("[GET] url={}, headers={}, params={}", url, JSON.toJSONString(headers), JSON.toJSONString(params));
    try {
      result = HttpClientUtil.doGet(this.pmsApiUrl + url, headers, params);
    }
    catch (Exception e){
      log.error(e.getMessage(), e);
    }

    log.info("[GET] result={}", JSON.toJSONString(result));
    return result;
  }

  /**
   * POST请求数据
   * @param url 相对地址
   * @param params 地址参数
   * @param token
   * @return
   */
  private JSONObject postData(String url, Map<String, Object> params, String token, String pmsSource) {
    JSONObject result = null;
    Map<String, String> headers = new HashMap<>();
    headers.put("uc-application","pms-service");
    if (StringUtils.isNotBlank(token)) {
      headers.put("uc-token",token);
    }
    if (StringUtils.isNotBlank(pmsSource)) {
      headers.put("x-pms-source", pmsSource);
    }
    try {
      result = HttpClientUtil.doJsonPost(this.pmsApiUrl + url, headers, params);
    }
    catch (Exception e){
      log.error(e.getMessage(), e);
    }

    log.info("[POST] url={}, headers={}, params={}", url, JSON.toJSONString(headers), JSON.toJSONString(params));
    return result;
  }

  @Override
  public ObtainTokenVo obtainTokenByMobile(ObtainTokenBo obtainTokenBo) {
    ModUser user = modUserDao.queryByMobile(obtainTokenBo.getMobile());
    if (user==null || user.getUcId()==null || user.getUcId().intValue()<=0) {
      throw new ServiceException(obtainTokenBo.getMobile() + "不是有效通宝用户");
    }
    Map<String, Object> params = new HashMap<>(2);
    params.put("username", obtainTokenBo.getMobile());
    params.put("password", "JWT:" + JwtUtils.generateToken(user.getUcId().toString(), JwtUtil.JWT_SECRET, 43200));

    JSONObject obj = this.postData("/login", params, null, null);

    if (obj!=null && obj.getIntValue(PMS_CODE)==1){
      ObtainTokenVo obtainTokenVo = obj.getJSONObject(PMS_DATA).toJavaObject(ObtainTokenVo.class);
      String roleId = this.getUserInfoByPms(obtainTokenVo.getToken());
      this.getPermission(obtainTokenVo.getToken(),roleId,obtainTokenBo.getProfitCode());
      List<String> buttons = this.getButton(obtainTokenVo.getToken(),roleId);
      obtainTokenVo.setButtons(buttons);
      return obtainTokenVo;
    }
    else {
      throw new ServiceException(obj!=null ? obj.getString(PMS_MSG) : StringUtils.EMPTY);
    }
  }

  @Override
  public String getPmsToken(UserInfoVO userInfoVO, Long expiration, String hotelCode) {
    if (userInfoVO==null || userInfoVO.getUcId()==null || userInfoVO.getUcId().intValue()<=0) {
      throw new ServiceException(userInfoVO.getMobile() + "不是有效通宝用户");
    }
    Map<String, Object> params = new HashMap<>(2);
    params.put("username", userInfoVO.getMobile());
    params.put("password", "JWT:" + JwtUtils.generateToken(userInfoVO.getUcId().toString(), JwtUtil.JWT_SECRET, expiration));

    JSONObject obj = this.postData("/login", params, null, "shands");
    if (obj!=null && obj.getIntValue(PMS_CODE)==1){
      ObtainTokenVo obtainTokenVo = obj.getJSONObject(PMS_DATA).toJavaObject(ObtainTokenVo.class);
      bindPmsTokenHotel(obtainTokenVo.getToken(),hotelCode);
      return obtainTokenVo.getToken();
    }
    return null;
  }

  @Override
  public boolean bindPmsTokenHotel(String token, String hotelCode) {
    JSONObject obj = this.getData("/getUserInfoByPms", null, token, "shands");
    if (null == obj || obj.getIntValue(PMS_CODE) != 1) {
      return false;
    }
    UserDetailsInfoVo userDetailsInfoVo = obj.getJSONObject(PMS_DATA).toJavaObject(UserDetailsInfoVo.class);
    List<CompanyRoles> companyRolesList = userDetailsInfoVo.getCompanyRoles();
    if (CollectionUtils.isEmpty(companyRolesList)) {
      return false;
    }
    String roleId = userDetailsInfoVo.getCompanyRoles().get(0).getRoleViewList().get(0).getRoleId();
    String companyCode = userDetailsInfoVo.getCompanyRoles().get(0).getCompanyCode();
    if (StringUtils.isNotBlank(hotelCode)) {
      CompanyRoles companyRoles = companyRolesList.stream().filter(cr -> hotelCode.equals(cr.getCompanyCode())).findFirst().orElse(null);
      if (null != companyRoles) {
        roleId = companyRoles.getRoleViewList().get(0).getRoleId();
        companyCode = companyRoles.getCompanyCode();
      }
    }

    Map<String, String> permissionParams = new HashMap<>();
    permissionParams.put("companyCode",companyCode);
    permissionParams.put("roleId", roleId);
    this.getData("/getPermission", permissionParams, token,"shands");
    String redKey = Tools.buildKey(BaseConstants.CACHE_USER_PMS_HOTEL, token);
    this.redisTemplate.opsForValue().set(redKey, companyCode, 3600, TimeUnit.SECONDS);
    return true;
  }

  private String getUserInfoByPms(String token) {
    JSONObject obj = this.getData("/getUserInfoByPms", null, token, null);
    if (obj==null || obj.getIntValue(PMS_CODE)==0){
      throw new ServiceException(obj!=null ? obj.getString(PMS_MSG) : StringUtils.EMPTY);
    }
    else {
      int hotelId = ThreadLocalHelper.getCompanyId();
      ModHotelInfo modHotelInfo = hotelInfoDao.queryById(hotelId);
      String hotelCode = modHotelInfo.getHotelCode();
      UserDetailsInfoVo userDetailsInfoVo = obj.getJSONObject(PMS_DATA).toJavaObject(UserDetailsInfoVo.class);
      if (null != userDetailsInfoVo){
        Map<String, CompanyRoles> map = new HashMap<>();
        List<CompanyRoles> companyRoles = userDetailsInfoVo.getCompanyRoles();
        companyRoles.forEach(a->{
          map.put(a.getCompanyCode(),a);
        });
        if (null == map.get(hotelCode)){
          throw new ServiceException("当前用户没有pms端" + modHotelInfo.getHotelName() + "的权限");
        }
      }
      return userDetailsInfoVo.getCompanyRoles().get(0).getRoleViewList().get(0).getRoleId();
    }
  }

  private List<String> getButton(String token, String roleId) {
    Map<String, String> params = new HashMap<>();
    params.put("roleId", roleId);

    JSONObject obj = this.getData("/getButton", params, token, null);
    if (obj==null || obj.getIntValue(PMS_CODE)==0){
      throw new ServiceException(obj!=null ? obj.getString(PMS_MSG) : StringUtils.EMPTY);
    }
    List<String> list = obj.getJSONArray(PMS_DATA).toJavaList(String.class);
    return list;
  }

  private void getPermission(String token, String roleId, String profitCode) {
    int hotelId = ThreadLocalHelper.getCompanyId();
    String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

    Map<String, String> params = new HashMap<>();
    params.put("companyCode",hotelCode);
    params.put("roleId", roleId);
    JSONObject obj = this.getData("/getPermission", params, token, null);
    if (obj==null || obj.getIntValue(PMS_CODE)==0){
      throw new ServiceException(obj!=null ? obj.getString(PMS_MSG) : StringUtils.EMPTY);
    }

    //通宝返回用户PMS系统权限报文
    List<Map<String, String>> listOne = JSONArray.parseObject(obj.getString(PMS_DATA), List.class);

    Map<String, Map<String, String>> oneLayerMap = new HashMap<>();
    Map<String, Map<String, String>> twoLayerMap = new HashMap<>();
    Map<String, Map<String, String>> threeLayerMap = new HashMap<>();

    for (Map<String, String> row : listOne){
      oneLayerMap.put(row.get(ROLE_NAME), row);
      oneLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
    }

    //收益管理
    if("base_price".equals(profitCode) || "room_quantity".equals(profitCode)){

      //收益管理
      Map<String, String> mapOne = oneLayerMap.get("earnings");
      if(mapOne == null){
        throw new ServiceException("没有收益管理权限，请联系管理员");
      }
      List<Map<String, String>> listTwo = JSONArray
          .parseObject(JSONObject.toJSONString(mapOne.get(ROLE_CHILDREN)
          ), List.class);
      for (Map<String, String> row : listTwo){
        twoLayerMap.put(row.get(ROLE_NAME), row);
        twoLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
      }
      //房价管理
      Map<String, String> mapTwo = twoLayerMap.get("priceManage");
      if (null != mapTwo && !mapTwo.isEmpty()){
        List<Map<String, String>> listThree = JSONArray
            .parseObject(JSONObject.toJSONString(mapTwo.get(ROLE_CHILDREN)
            ), List.class);
        for (Map<String, String> row : listThree){
          threeLayerMap.put(row.get(ROLE_NAME), row);
          threeLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
        }
        if ("base_price".equals(profitCode)){
          profitCode = "basePriceConfig";
        }
        if ("room_quantity".equals(profitCode)){
          profitCode = "channelRoomsConfig";
        }
        if (null == threeLayerMap.get(profitCode)){
          throw new ServiceException("没有收益管理权限，请联系管理员");
        }
      }else {
        throw new ServiceException("没有收益管理权限，请联系管理员");
      }
    }else if("operating_income_forecast".equals(profitCode)){
      //  今日营收预测: 统计->报表中心->今日营业预测 改为 统计->营收统计->今日营业预测
      //统计
      Map<String, String> mapOne = oneLayerMap.get("count");
      if(mapOne == null){
        throw new ServiceException("没有统计管理权限，请联系管理员");
      }
      List<Map<String, String>> listTwo = JSONArray
          .parseObject(JSONObject.toJSONString(mapOne.get(ROLE_CHILDREN)
          ), List.class);
      for (Map<String, String> row : listTwo){
        twoLayerMap.put(row.get(ROLE_NAME), row);
        twoLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
      }
      //营收统计
      Map<String, String> mapTwo = twoLayerMap.get("revenueStatistics");
      if (null != mapTwo && !mapTwo.isEmpty()){
        List<Map<String, String>> listThree = JSONArray
            .parseObject(JSONObject.toJSONString(mapTwo.get(ROLE_CHILDREN)
            ), List.class);
        for (Map<String, String> row : listThree){
          threeLayerMap.put(row.get(ROLE_NAME), row);
          threeLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
        }
        if ("operating_income_forecast".equals(profitCode)){
          profitCode = "openTodayReport";
        }
        if (null == threeLayerMap.get(profitCode)){
          throw new ServiceException("没有统计管理权限，请联系管理员");
        }
      }else {
        throw new ServiceException("没有统计管理权限，请联系管理员");
      }
    }else if("room_order".equals(profitCode)){
      //统计
      Map<String, String> mapOne = oneLayerMap.get("order");
      if(mapOne == null){
        throw new ServiceException("没有订单管理权限，请联系管理员");
      }
      List<Map<String, String>> listTwo = JSONArray
          .parseObject(JSONObject.toJSONString(mapOne.get(ROLE_CHILDREN)
          ), List.class);
      for (Map<String, String> row : listTwo){
        twoLayerMap.put(row.get(ROLE_NAME), row);
        twoLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
      }
      //报表中心
      Map<String, String> mapTwo = twoLayerMap.get("orderManage");
      if (null != mapTwo && !mapTwo.isEmpty()){
        List<Map<String, String>> listThree = JSONArray
            .parseObject(JSONObject.toJSONString(mapTwo.get(ROLE_CHILDREN)
            ), List.class);
        for (Map<String, String> row : listThree){
          threeLayerMap.put(row.get(ROLE_NAME), row);
          threeLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
        }
        if ("room_order".equals(profitCode)){
          profitCode = "orderList";
        }
        if (null == threeLayerMap.get(profitCode)){
          throw new ServiceException("没有订单管理权限，请联系管理员");
        }
      }else {
        throw new ServiceException("没有订单管理权限，请联系管理员");
      }
    }else if("same_city_room_type".equals(profitCode)){
      //房态
      Map<String, String> mapOne = oneLayerMap.get("roomStatus");
      if(mapOne == null){
        throw new ServiceException("没有房态管理权限，请联系管理员");
      }
      List<Map<String, String>> listTwo = JSONArray
          .parseObject(JSONObject.toJSONString(mapOne.get(ROLE_CHILDREN)
          ), List.class);
      for (Map<String, String> row : listTwo){
        twoLayerMap.put(row.get(ROLE_NAME), row);
        twoLayerMap.put(JSONObject.toJSONString(row.get(ROLE_CHILDREN)), row);
      }
      if ("same_city_room_type".equals(profitCode)){
        profitCode = "roomStatus-list";
      }
      if (null == twoLayerMap.get(profitCode)){
        throw new ServiceException("没有房态管理权限，请联系管理员");
      }
    }
  }
}
