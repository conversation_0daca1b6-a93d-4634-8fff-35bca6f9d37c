package com.shands.mod.main.controller.datarevision.v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.model.datarevision.bo.HotelProjectMapBo;
import com.shands.mod.dao.model.datarevision.bo.ProjectDataBo;
import com.shands.mod.dao.model.datarevision.bo.ProjectTrendsBo;
import com.shands.mod.dao.model.datarevision.vo.GeneralDrawingDetailsVo;
import com.shands.mod.dao.model.datarevision.vo.HomeDataVo;
import com.shands.mod.dao.model.datarevision.vo.HotelProjectMapVo;
import com.shands.mod.dao.model.datarevision.vo.ProjectDataVo;
import com.shands.mod.dao.model.homerevision.UcViewListVo;
import com.shands.mod.main.service.datarevision.v3.HotelProjectService;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import com.shands.uc.model.req.v3.auth.RoleInfo;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/hotelproject")
@Api(value = "数据报表3.0",tags = "数据报表3.0")
@Slf4j
public class HotelProjectController {

  private final HotelProjectService hotelProjectService;
  private final ModUserCommonService modUserCommonService;

  @Autowired
  public HotelProjectController(HotelProjectService hotelProjectService,ModUserCommonService modUserCommonService){
    this.hotelProjectService=hotelProjectService;
    this.modUserCommonService = modUserCommonService;
  }


  @ApiOperation(value = "数据维度信息")
  @PostMapping(value = "/getDataDimension")
  public ResultVO getDataDimension(@Valid @RequestBody Map<String,String> map) {
    return ResultVO.success(hotelProjectService.getDataDimension(map.get("dimensionType")));
  }

  @ApiOperation(value = "获取默认事业部")
  @PostMapping(value = "/getOwnership")
  public ResultVO getOwnership() {
    Map res = new HashMap();
    List<String> ownershipList = new ArrayList<>();
    UserAuthInfo userAuthInfo = modUserCommonService.getUserUcInfo(BaseThreadLocalHelper.getUser().getId());
    List<RoleInfo> roleInfos = userAuthInfo.getRoles();
    if (roleInfos != null && roleInfos.size() > 0){
      roleInfos.forEach(roleInfo -> {
        if(org.springframework.util.StringUtils.hasText(roleInfo.getViewCode())) {
          if (roleInfo.getViewCode().contains("MAP")){
            JSONArray jsonArray = JSON.parseArray(roleInfo.getViewValue());
            List<UcViewListVo> viewListVos = jsonArray.toJavaList(UcViewListVo.class);
            viewListVos.forEach(viewListVo -> {
              if ("ownership".equals(viewListVo.getType())) {
                ownershipList.add(viewListVo.getValue());
              }
            });
          }
        }
      });
    }
    if (ownershipList.size() == 1){
      res.put("ownership",ownershipList.get(0));
      return ResultVO.success(res);
    }
    return ResultVO.success();
  }



  @ApiOperation(value = "酒店项目地图分布")
  @PostMapping(value = "/hotelProjectMap")
  @ApiResponse(code = 0, message = "成功", response = HotelProjectMapVo.class)
  public ResultVO<Map<String, Object>> hotelProjectMap(@Valid @RequestBody HotelProjectMapBo hotelProjectMapBo) {
    return ResultVO.success(hotelProjectService.getHotelProjectMap(hotelProjectMapBo));
  }

  @ApiOperation(value = "项目数据分析")
  @PostMapping(value = "/projectData")
  @ApiResponse(code = 0, message = "成功", response = ProjectDataVo.class)
  public ResultVO projectData (@Valid @RequestBody ProjectDataBo ProjectDataBo) {
    return ResultVO.success(hotelProjectService.projectData(ProjectDataBo));
  }


  @ApiOperation(value = "酒店状态项目趋势")
  @PostMapping(value = "/projectTrends")
  @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  public ResultVO projectTrends (@Valid @RequestBody ProjectTrendsBo projectTrendsBo) {
    return ResultVO.success(hotelProjectService.projectTrends(projectTrendsBo));
  }

  @ApiOperation(value = "预计开业项目趋势")
  @PostMapping(value = "/expectProjectTrends")
  @ApiResponse(code = 0, message = "成功", response = GeneralDrawingDetailsVo.class)
  public ResultVO expectProjectTrends (@Valid @RequestBody ProjectTrendsBo projectTrendsBo) {
    return ResultVO.success(hotelProjectService.expectProjectTrends(projectTrendsBo));
  }

  @ApiOperation(value = "完成率展示")
  @PostMapping(value = "/completionShow")
  @ApiResponse(code = 0, message = "成功", response = HomeDataVo.class)
  public ResultVO completionShow (@Valid @RequestBody Map<String, String> map) {
    return ResultVO.success(hotelProjectService.completionAndOverview("COMPLETION",map.get("pId")));
  }


  @ApiOperation(value = "概览展示")
  @PostMapping(value = "/overviewshow")
  @ApiResponse(code = 0, message = "成功", response = HomeDataVo.class)
  public ResultVO overviewshow (@Valid @RequestBody Map<String, String> map) {
    return ResultVO.success(hotelProjectService.completionAndOverview("OVERVIEW",map.get("pId")));
  }

}
