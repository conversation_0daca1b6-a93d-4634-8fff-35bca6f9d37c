package com.shands.mod.main.service.betterwood;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.model.res.elsreport.BdxDataReportBo;
import com.shands.mod.dao.model.res.elsreport.BdxQurCouponBo;
import com.shands.mod.dao.model.res.elsreport.BdxQurMemberInfoBo;
import com.shands.mod.dao.model.res.elsreport.BindingFailStatusVo;
import com.shands.mod.dao.model.res.elsreport.MemberDevelopForBetterwoodBo;
import com.shands.mod.dao.model.res.elsreport.MemberDevelopForBetterwoodVo;
import com.shands.mod.dao.model.res.elsreport.PlatFormBo;
import com.shands.mod.dao.model.res.elsreport.PlatFormVo;
import com.shands.mod.dao.model.res.elsreport.dept.MarkRestaurantDeptReq;
import com.shands.mod.dao.model.res.elsreport.dept.UcDeptInfo;
import com.shands.mod.main.service.betterwood.resp.AreaCodeResp;
import java.util.List;

public interface ToBetterwoodService {
  List<MemberDevelopForBetterwoodVo> selectMemberDevelopForBetterwood(
    MemberDevelopForBetterwoodBo memberDevelopForBetterwoodBo);

  PlatFormVo selectBindingRecordsBetterWood(PlatFormBo platFormBo);

  JSONObject qurMemberInfo(BdxQurMemberInfoBo bdxQurMemberInfoBo);

  /**
   * 获取国家码
   * @return
   */
  List<AreaCodeResp> qurAreaCode();


  JSONObject qurCouponList(BdxQurCouponBo bdxQurCouponBo);

  JSONObject bookingDataReport(BdxDataReportBo bdxDataReportBo);

  JSONObject roomOrderScan();

  /**
   * 获取失败状态和描述
   * @return
   */
  List<BindingFailStatusVo> queryFailStatus();

  /**
   * 获取部门树
   * @return
   */
  List<UcDeptInfo> queryDeptTree(Integer companyId);

  /**
   * 获取餐饮部门
   * @param companyId
   * @return
   */
  List<UcDeptInfo> queryRestaurantDeptList(Integer companyId);

  /**
   * 保存餐饮部门
   * @param deptRespList
   */
  void addRestaurantDeptList(MarkRestaurantDeptReq markRestaurantDeptReq);
}
