package com.shands.mod.main.util;

import cn.hutool.poi.excel.style.StyleUtil;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import org.apache.poi.ss.usermodel.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1
 **/
public class SalaryCellWriteHandler implements CellWriteHandler {

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cell<PERSON><PERSON><PERSON><PERSON>, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        int rowIndex = cell.getRowIndex();
        int cellIndex = cell.getColumnIndex();
        // 自定义样式处理
        if (rowIndex >= 3) {
          if (cellIndex == 0){
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            //设置字体颜色
            headWriteFont.setColor((short) 10);
            //在样式用应用设置的字体;
            writeCellStyle.setWriteFont(headWriteFont);
            WriteCellData<?> writeCellData = cellDataList.get(cellIndex);
            writeCellData.setWriteCellStyle(writeCellStyle);
          }
        }
    }
}
