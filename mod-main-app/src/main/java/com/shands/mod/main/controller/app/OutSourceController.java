package com.shands.mod.main.controller.app;

import static com.shands.mod.main.service.workorder.newwork.OutSourceOperationEnum.CREATE;
import static com.shands.mod.main.service.workorder.newwork.OutSourceOperationEnum.MODIFY;

import com.shands.mod.dao.model.v0701.dto.OutSourceAddDto;
import com.shands.mod.dao.model.v0701.dto.OutSourceListDto;
import com.shands.mod.dao.model.v0701.dto.OutSourceLoginDto;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import com.shands.mod.dao.model.v0701.dto.OutSourceModifyDto;
import com.shands.mod.dao.model.v0701.dto.OutSourceSmsDto;
import com.shands.mod.dao.model.v0701.vo.ModUserLoginVo;
import com.shands.mod.dao.model.v0701.vo.OutSourceRoleVo;
import com.shands.mod.dao.model.v0701.vo.OutUserCompanyVo;
import com.shands.mod.dao.model.v0701.vo.OutUserDeptVo;
import com.shands.mod.dao.model.v0701.vo.OutUserInfoVo;
import com.shands.mod.dao.model.v0701.vo.OutUserLogVo;
import com.shands.mod.main.annotation.OutSourceOpterDetail;
import com.shands.mod.main.service.outsource.OutUserInfoService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/12/9
 * @desc 外包人员controller
 */

@RestController
@RequestMapping(value = "/outSource")
@Api(value = "外包人员管理",tags = "外包人员管理")
@Slf4j
public class OutSourceController {

  private final OutUserInfoService outUserInfoService;

  private final HttpServletRequest httpServletRequest;

  public OutSourceController(
      OutUserInfoService outUserInfoService,
      HttpServletRequest httpServletRequest) {
    this.outUserInfoService = outUserInfoService;
    this.httpServletRequest = httpServletRequest;
  }

  @PostMapping("/addUser")
  @ApiOperation(value = "添加外协人员")
  @OutSourceOpterDetail(OUT_SOURCE_OPERATION_ENUM = CREATE)
    @ResultLog(name = "OutSourceController.addUser", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Boolean> addUser(@RequestBody OutSourceAddDto dto){
    return ResultVO.success(outUserInfoService.addUser(dto));
  }

  @PostMapping("/getRoles")
  @ApiOperation(value = "查询外包适用角色列表")
    @ResultLog(name = "OutSourceController.getRoles", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<OutSourceRoleVo>> getRoles(){
    return ResultVO.success(outUserInfoService.getRoles());
  }

  @PostMapping("/getUserInfos")
  @ApiOperation(value = "查询外包人员列表")
    @ResultLog(name = "OutSourceController.getUserInfos", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<OutUserInfoVo>> getUserInfos(@RequestBody OutSourceListDto dto){
    return ResultVO.success(outUserInfoService.getUserInfos(dto));
  }

  @PostMapping("/getUserInfo")
  @ApiOperation(value = "查询外包人员详情")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户编码", required = true, dataType = "String", paramType = "query"),
  })
    @ResultLog(name = "OutSourceController.getUserInfo", methodType = MethodTypeEnum.HTTP_UP)
      public ResultVO<OutUserInfoVo> getUserInfo(@RequestBody Map<String,Integer> params){
    return ResultVO.success(outUserInfoService.getUserInfo(params.get("userId")));
  }

  @PostMapping("/modifyUser")
  @ApiOperation(value = "修改外包人员信息")
  @OutSourceOpterDetail(OUT_SOURCE_OPERATION_ENUM = MODIFY)
    @ResultLog(name = "OutSourceController.modifyUser", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Boolean> modifyUser(@RequestBody OutSourceModifyDto dto){
    return ResultVO.success(outUserInfoService.modifyUser(dto));
  }

  @PostMapping("/getOpterInfo")
  @ApiOperation(value = "操作日志查询")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userMobile", value = "用户手机号", required = true, dataType = "String", paramType = "query"),
  })
    @ResultLog(name = "OutSourceController.getOpterInfo", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<OutUserLogVo>> getOpterInfo(@RequestBody Map<String,String> params){
    return ResultVO.success(outUserInfoService.getOpterInfo(params.get("userMobile")));
  }

  @PostMapping("/getUserCompanys")
  @ApiOperation(value = "查询外包人员机构列表")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userMobile", value = "用户手机号", required = true, dataType = "String", paramType = "query"),
  })
    @ResultLog(name = "OutSourceController.getUserCompanys", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<OutUserCompanyVo>> getUserCompanys(@RequestBody Map<String,String> params){
    return ResultVO.success(outUserInfoService.getUserCompanys(params.get("userMobile")));
  }

  @PostMapping("/getDepts")
  @ApiOperation(value = "查询外包人员已登记部门列表")
    @ResultLog(name = "OutSourceController.getDepts", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<OutUserDeptVo>> getDepts(){
    return ResultVO.success(outUserInfoService.getDepts());
  }

  @PostMapping("/getSmsCode")
  @ApiOperation(value = "获取短信验证码")
    @ResultLog(name = "OutSourceController.getSmsCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<String> getSmsCode(@RequestBody OutSourceSmsDto dto) {
    String result = outUserInfoService.getSmsCode(dto);
    return ResultVO.success(result);
  }

  @PostMapping("/loginByCode")
  @ApiOperation(value = "短信验证码登录")
    @ResultLog(name = "OutSourceController.loginByCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<ModUserLoginVo> loginByCode(@RequestBody OutSourceLoginDto dto){
    return ResultVO.success(outUserInfoService.loginByCode(dto,this.httpServletRequest));
  }
}
