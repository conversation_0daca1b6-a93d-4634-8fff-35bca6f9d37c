package com.shands.mod.main.controller.shift;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.model.hs.HotelLocationReq;
import com.shands.mod.dao.model.hs.HotelLocationRes;
import com.shands.mod.dao.model.res.hs.staff.UserServerAreaRes;
import com.shands.mod.dao.model.shift.BatchShiftInfoBo;
import com.shands.mod.dao.model.shift.CopyShiftBo;
import com.shands.mod.dao.model.shift.FloorBuildRoomResVo;
import com.shands.mod.dao.model.shift.MbFloorBuildRoomResVo;
import com.shands.mod.dao.model.shift.MbRoomNumberBo;
import com.shands.mod.dao.model.shift.MbWorkOrderCreateBo;
import com.shands.mod.dao.model.shift.ModBaseUser;
import com.shands.mod.dao.model.shift.ModServiceExtendVo;
import com.shands.mod.dao.model.shift.ModShiftAllServiceRoomReq;
import com.shands.mod.dao.model.shift.ModShiftAllServiceRoomRes;
import com.shands.mod.dao.model.shift.ModShiftAllUserBo;
import com.shands.mod.dao.model.shift.ModShiftGroup;
import com.shands.mod.dao.model.shift.ModShiftInfo;
import com.shands.mod.dao.model.shift.ModShiftInfoDelUserBo;
import com.shands.mod.dao.model.shift.ModShiftInfoInrBo;
import com.shands.mod.dao.model.shift.ModShiftInfoListBo;
import com.shands.mod.dao.model.shift.ModShiftInfoStatusBo;
import com.shands.mod.dao.model.shift.ModShiftInfoUpdBo;
import com.shands.mod.dao.model.shift.ModShiftInfoUserBo;
import com.shands.mod.dao.model.shift.ModShiftUserBo;
import com.shands.mod.dao.model.shift.ModShiftUserParentVo;
import com.shands.mod.dao.model.shift.ModShiftUserRepairBo;
import com.shands.mod.dao.model.shift.ModShiftUserServiceBo;
import com.shands.mod.dao.model.shift.QurMbServicesVo;
import com.shands.mod.dao.model.shift.QurShiftUserParentVo;
import com.shands.mod.dao.model.shift.RemoveShiftBo;
import com.shands.mod.dao.model.shift.RepairServiceResVo;
import com.shands.mod.dao.model.shift.RepairShiftSetBo;
import com.shands.mod.dao.model.shift.ServiceShiftSetBo;
import com.shands.mod.dao.model.shift.ShiftAcceptUserReq;
import com.shands.mod.dao.model.shift.ShiftBaseUserInfoReq;
import com.shands.mod.dao.model.shift.ShiftGroupAddReq;
import com.shands.mod.dao.model.shift.ShiftGroupDeleteReq;
import com.shands.mod.dao.model.shift.ShiftGroupUpdateReq;
import com.shands.mod.dao.model.shift.ShiftInfoMultiUserBo;
import com.shands.mod.dao.model.shift.ShiftInfoReq;
import com.shands.mod.dao.model.shift.ShiftWithUsers;
import com.shands.mod.dao.model.shift.SyncDeliverShiftReq;
import com.shands.mod.dao.model.shift.UpdateShiftBo;
import com.shands.mod.main.service.shift.ModShiftAllServiceRoomService;
import com.shands.mod.main.service.shift.ModShiftInfoService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/12/26
 * @desc 排班管理
 */

@RestController
@RequestMapping(value = "/shift")
@Api(value = "APP",tags = "排班管理")
@Slf4j
public class HotelShiftController extends BaseController {

  private final ModShiftInfoService modShiftInfoService;

  private final ModShiftAllServiceRoomService  modShiftAllServiceRoomService;

  @Override
  public boolean isPublic() {
    return true;
  }

  public HotelShiftController(
      ModShiftInfoService modShiftInfoService,
  ModShiftAllServiceRoomService  modShiftAllServiceRoomService) {
    this.modShiftInfoService = modShiftInfoService;
    this.modShiftAllServiceRoomService = modShiftAllServiceRoomService;
  }

  @ApiOperation(value = "班次列表信息查询")
  @PostMapping(value = "/qurShiftInfos")
  public ResultVO<List<ModShiftInfo>> qurShiftInfos(@RequestBody ModShiftInfoListBo modShiftInfoListBo){
    log.info("班次列表信息查询qurShiftInfos,酒店id：{},入参:{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(modShiftInfoListBo));
    return ResultVO.success(modShiftInfoService.queryByHotelIdIgnoreStatus(ThreadLocalHelper.getCompanyId(),modShiftInfoListBo.getShiftType()));
  }

  @ApiOperation(value = "班次状态设置")
  @PostMapping(value = "/setShiftInfoStatus")
  public ResultVO<Boolean> setShiftInfoStatus(@RequestBody ModShiftInfoStatusBo modShiftInfoStatusBo){
    log.info("班次状态设置setShiftInfoStatus,酒店id：{},入参:{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(modShiftInfoStatusBo));
    return ResultVO.success(modShiftInfoService.setStatus(modShiftInfoStatusBo));
  }

  @ApiOperation(value = "新增班次信息")
  @PostMapping(value = "/inrShiftInfo")
  public ResultVO<Boolean> inrShiftInfo(@RequestBody ModShiftInfoInrBo modShiftInfoInrBo){
    log.info("新增班次信息inrShiftInfo,酒店id：{},入参:{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(modShiftInfoInrBo));
    return ResultVO.success(modShiftInfoService.inrInfo(modShiftInfoInrBo));
  }

  @ApiOperation(value = "修改班次信息")
  @PostMapping(value = "/updShiftInfo")
  public ResultVO<Boolean> updShiftInfo(@RequestBody ModShiftInfoUpdBo modShiftInfoUpdBo){
    log.info("修改班次信息updShiftInfo,酒店id:{}，入参:{}",ThreadLocalHelper.getCompanyId(),JSON.toJSONString(modShiftInfoUpdBo));
    return ResultVO.success(modShiftInfoService.updInfo(modShiftInfoUpdBo));
  }

  @ApiOperation(value = "初始化员工排班")
  @PostMapping(value = "/initShiftUser")
  public ResultVO<Boolean> initShiftUser(@RequestBody @Valid ModShiftInfoUserBo modShiftInfoUserBo){
    log.info("初始化员工排班initShiftUser,入参:{}",JSON.toJSONString(modShiftInfoUserBo));
    return ResultVO.success(modShiftInfoService.initShiftUser(modShiftInfoUserBo));
  }

  @ApiOperation(value = "删除员工排班")
  @PostMapping(value = "/delShiftUser")
  public ResultVO<Boolean> delShiftUser(@RequestBody @Valid ModShiftInfoDelUserBo modShiftInfoDelUserBo){
    log.info("删除员工排班delShiftUser,酒店id:{}，入参:{}",ThreadLocalHelper.getCompanyId(),JSON.toJSONString(modShiftInfoDelUserBo));
    return ResultVO.success(modShiftInfoService.delShiftUser(modShiftInfoDelUserBo));
  }

  @ApiOperation(value = "根据班次类型查询员工排班")
  @PostMapping(value = "/qurShiftUserInfos")
  public ResultVO<ModShiftUserParentVo> qurShiftUserInfos(@RequestBody ModShiftUserBo modShiftUserBo){
    return ResultVO.success(modShiftInfoService.qurShiftUserInfos(modShiftUserBo));
  }

  @ApiOperation(value = "服务细分排班")
  @PostMapping(value = "/qurHotelRoomServiceShift")
  public ResultVO<List<FloorBuildRoomResVo>> qurHotelServiceShift(@RequestBody ModShiftUserServiceBo modShiftUserServiceBo){
    return ResultVO.success(modShiftInfoService.qurHotelServiceShift(modShiftUserServiceBo));
  }

  @ApiOperation(value = "维修细分排班")
  @PostMapping(value = "/qurHotelRepairShift")
  public ResultVO<List<RepairServiceResVo>> qurHotelRepairShift(@RequestBody ModShiftUserRepairBo modShiftUserRepairBo){
    return ResultVO.success(modShiftInfoService.qurHotelRepairShift(modShiftUserRepairBo));
  }

  @ApiOperation(value = "维修明细查询")
  @PostMapping(value = "/qurServiceExtend")
  public ResultVO<List<ModServiceExtendVo>> qurServiceExtend(){
    return ResultVO.success(modShiftInfoService.qurServiceExtend());
  }

  @ApiOperation(value = "查询所有班次员工排班情况")
  @PostMapping(value = "/qurShiftUsers")
  public ResultVO<List<QurShiftUserParentVo>> qurShiftUsers(@RequestBody ModShiftAllUserBo modShiftAllUserBo){
    return ResultVO.success(modShiftInfoService.qurShiftUsers(modShiftAllUserBo));
  }

  @ApiOperation(value = "服务细分排班设置")
  @PostMapping(value = "/setServiceShift")
  public ResultVO<Boolean> setServiceShift(@RequestBody ServiceShiftSetBo serviceShiftSetBo){
    return ResultVO.success(modShiftInfoService.setServiceShift(serviceShiftSetBo));
  }

  @ApiOperation(value = "维修细分排班设置")
  @PostMapping(value = "/setRepairShift")
  public ResultVO<Boolean> setRepairShift(@RequestBody RepairShiftSetBo repairShiftSetBo){
    return ResultVO.success(modShiftInfoService.setRepairShift(repairShiftSetBo));
  }

  @ApiOperation(value = "客户端-酒店服务类型查询")
  @PostMapping(value = "/qurMbServices")
  public ResultVO<List<QurMbServicesVo>> qurMbServices(){
    List<QurMbServicesVo> qurMbServicesVos = modShiftInfoService.qurMbServices();

    if (CollectionUtil.isNotEmpty(qurMbServicesVos)) {
      for (QurMbServicesVo qurMbServicesVo : qurMbServicesVos) {
        if (!StringUtils.isEmpty(qurMbServicesVo.getIconUrl())) {
          qurMbServicesVo.setIconUrl(qurMbServicesVo.getIconUrl().replace("oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
        }
      }
    }

    return ResultVO.success(qurMbServicesVos);
  }

  @ApiOperation(value = "客户端-酒店楼层楼宇获取")
  @PostMapping(value = "/qurMbFloorBuild")
  public ResultVO<List<MbFloorBuildRoomResVo>> qurMbFloorBuild(@RequestBody MbRoomNumberBo mbRoomNumberBo){
    return ResultVO.success(modShiftInfoService.qurMbFloorBuild(mbRoomNumberBo.getRoomNumber()));
  }

  @ApiOperation(value = "客户端-服务工单受理人查询")
  @PostMapping(value = "/qurMbAcceptUser")
  public ResultVO<UserServerAreaRes> qurMbAcceptUser(@RequestBody MbWorkOrderCreateBo mbWorkOrderCreateBo){
    return ResultVO.success(modShiftInfoService.qurMbAcceptUser(mbWorkOrderCreateBo));
  }

  @ApiOperation(value = "多日排班")
  @PostMapping(value = "/initManyDayShiftUser")
  public ResultVO<Boolean> initManyDayShiftUser(@RequestBody @Valid ShiftInfoMultiUserBo shiftInfoMultiUserBo){
    log.info("多日排班,入参：{}",JSON.toJSONString(shiftInfoMultiUserBo));
    return ResultVO.success(modShiftInfoService.initManyDayShiftUser(shiftInfoMultiUserBo));
  }

  @ApiOperation(value = "批量排班")
  @PostMapping(value = "/batchSetServiceShift")
  public ResultVO<Boolean> batchSetServiceShift(@RequestBody @Valid BatchShiftInfoBo batchShiftInfoBo){
    log.info("批量排班,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(batchShiftInfoBo));
    return ResultVO.success(modShiftAllServiceRoomService.batchSetServiceShift(batchShiftInfoBo));
  }

  @ApiOperation(value = "复制排班")
  @PostMapping(value = "/copyShift")
  public ResultVO<Boolean> copyShift(@RequestBody @Valid CopyShiftBo copyShiftBo){
    log.info("复制排班,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(copyShiftBo));
    return ResultVO.success(modShiftAllServiceRoomService.copyShift(copyShiftBo));
  }

  @ApiOperation(value = "编辑排班")
  @PostMapping(value = "/updateShift")
  public ResultVO<Boolean> updateShift(@RequestBody @Valid UpdateShiftBo updateShiftBo){
    log.info("编辑排班,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(updateShiftBo));
    return ResultVO.success(modShiftAllServiceRoomService.updateShift(updateShiftBo));
  }

  @ApiOperation(value = "清空排班")
  @PostMapping(value = "/removeShift")
  public ResultVO<Boolean> removeShift(@RequestBody @Valid RemoveShiftBo removeShiftBo){
    log.info("清空排班,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(removeShiftBo));
    return ResultVO.success(modShiftAllServiceRoomService.removeShift(removeShiftBo));
  }

  @ApiOperation(value = "同步保洁服务排班到送物服务")
  @PostMapping(value = "/syncToDeliverService")
  public ResultVO<Boolean> syncToDeliverService(@RequestBody @Valid SyncDeliverShiftReq syncDeliverShiftReq){
    log.info("syncToDeliverService,酒店id:{},入参：{}",ThreadLocalHelper.getCompanyId(), JSON.toJSONString(syncDeliverShiftReq));
    return ResultVO.success(modShiftAllServiceRoomService.syncToDeliverService(syncDeliverShiftReq));
  }

  @ApiOperation(value = "服务细分排班列表")
  @PostMapping(value = "/queryHotelRoomServiceShift")
  public ResultVO<PageInfo<ModShiftAllServiceRoomRes>> queryHotelRoomServiceShift(@RequestBody @Valid ModShiftAllServiceRoomReq allServiceRoomReq){
    log.info("searchServiceShiftsByItems,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(allServiceRoomReq));

    return ResultVO.success(modShiftAllServiceRoomService.queryHotelRoomServiceShift(allServiceRoomReq));
  }

  @ApiOperation(value = "获取部门下的班次已排班人员")
  @PostMapping(value="/getShiftInfoByShiftType")
  public ResultVO<List<ShiftWithUsers>> getShiftInfoByShiftType(@RequestBody @Valid ShiftInfoReq shiftInfoReq) {
    log.info("getShiftInfoByShiftType,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(shiftInfoReq));
    return ResultVO.success(modShiftInfoService.getShiftInfoByShiftType(shiftInfoReq));
  }

  @ApiOperation(value = "获取当前部门当前日期下的员工姓名")
  @PostMapping(value = "/getShiftBaseUserInfo")
  public ResultVO<List<ModBaseUser>> getShiftBaseUserInfo(@RequestBody @Valid ShiftBaseUserInfoReq baseUserInfoReq) {
    log.info("getShiftBaseUserInfo,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(baseUserInfoReq));
    return ResultVO.success(modShiftInfoService.getShiftBaseUserInfo(baseUserInfoReq));
  }

  @ApiOperation(value = "获取楼层楼栋房间信息")
  @PostMapping(value = "/getHotelLocationInfo")
  public ResultVO<List<HotelLocationRes>> getHotelLocationInfo(@RequestBody @Valid HotelLocationReq hotelLocationReq) {
    log.info("getHotelLocationInfo,酒店id:{},入参：{}", ThreadLocalHelper.getCompanyId(), JSON.toJSONString(hotelLocationReq));
    return ResultVO.success(modShiftInfoService.getHotelLocationInfo(hotelLocationReq));
  }

  @ApiOperation(value = "获取工单受理人")
  @PostMapping(value = "/getShiftAcceptUserInfoList")
  public ResultVO<List<Integer>> getShiftAcceptUserInfoList(@RequestBody @Valid ShiftAcceptUserReq shiftAcceptUserReq) {
    log.info("getShiftAcceptUserInfoList,入参：{}", JSON.toJSONString(shiftAcceptUserReq));
    return ResultVO.success(modShiftAllServiceRoomService.getShiftAcceptUserInfoList(shiftAcceptUserReq));
  }


  @ApiOperation(value = "添加班组")
  @PostMapping(value = "/addShiftGroup")
  public ResultVO<Boolean> addShiftGroup(@RequestBody @Valid ShiftGroupAddReq shiftAcceptUserReq) {
    log.info("addShiftGroup,入参：{}", JSON.toJSONString(shiftAcceptUserReq));
    return ResultVO.success(modShiftInfoService.addShiftGroup(shiftAcceptUserReq));
  }


  @ApiOperation(value = "获取班组列表")
  @GetMapping(value = "/queryShiftGroupList")
  public ResultVO<List<ModShiftGroup>> queryShiftGroupList(Integer status) {
    log.info("queryShiftGroupList,酒店id:{}", ThreadLocalHelper.getCompanyId());
    return ResultVO.success(modShiftInfoService.queryShiftGroupList(status));
  }

  @ApiOperation(value = "编辑班组")
  @PostMapping(value = "/updateShiftGroup")
  public ResultVO<Boolean> updateShiftGroup(@RequestBody @Valid ShiftGroupUpdateReq shiftGroupUpdateReq) {
    log.info("updateShiftGroup,入参：{}", JSON.toJSONString(shiftGroupUpdateReq));
    return ResultVO.success(modShiftInfoService.updateShiftGroup(shiftGroupUpdateReq));
  }


  @ApiOperation(value = "删除班组")
  @PostMapping(value = "/deleteShiftGroup")
  public ResultVO<Boolean> deleteShiftGroup(@RequestBody ShiftGroupDeleteReq shiftGroupDeleteReq) {
    log.info("deleteShiftGroup,入参：{}", JSON.toJSONString(shiftGroupDeleteReq));
    return ResultVO.success(modShiftInfoService.deleteShiftGroup(shiftGroupDeleteReq.getShiftType()));
  }

}
