package com.shands.mod.main.service.app;

import com.shands.mod.vo.ResultVO;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/10
 * @desc 用户通宝信息处理serivce
*/
public interface UcUserService {

  /**
   * 根据用户名校验用户在通宝中的信息
   * @param userName
   */
  public UserAuthInfo ucLoginByUsername(String userName);

  /**
   * app扫码登录通宝
   *
   * @param uid    uid
   * @param userId 用户id
   * @return {@link ResultVO}
   */
  ResultVO scanQrLoginUc(Integer userId, String uid);

  /**
   * pms扫码登录
   *
   * @param params    uid
   * @param userId 用户id
   * @return {@link ResultVO}
   */
  ResultVO scanQrLoginPms(Integer userId, Map<String,String> params);

  /**
   * 通过手机号和用户ID
   *
   * @param mobile 手机号
   * @param ucId   通宝用户ID
   * @return {@link String}
   */
  String loginByPhoneAndId(String mobile, Integer ucId);

  /**
   * 通过手机号和密码登录
   *
   * @param mobile 手机号
   * @param password   通宝用户密码
   * @return {@link String}
   */
  String loginByMobileAndPassword(String mobile, String password);

  /**
   * 获取通宝角色信息权限
   *
   * @param ucToken 通宝uctoken
   * @param ucAppId 通宝应用名称
   * @return {@link List}
   */
  List getUcUserRoles(String ucToken, String ucAppId);
}
