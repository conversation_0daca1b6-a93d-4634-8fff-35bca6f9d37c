package com.shands.mod.main.service.voucher.strategy;

import com.betterwood.merchant.api.res.HotelInfoDTORes;
import com.shands.mod.dao.model.enums.ShareChannelEnum;
import com.shands.mod.dao.model.sales.tool.dto.ShareParameters;
import com.shands.mod.dao.model.sales.tool.dto.WechatParameters;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.config.VoucherBenefitConfig;
import com.shands.mod.main.config.VoucherBenefitConfig.BenefitTemplate;
import com.shands.mod.main.remote.merchant.MerchantServiceClient;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 微信小程序分享
 * @Author: wj
 * @Date: 2024/8/12 18:07
 */
@Service("voucherWechatStrategy")
@Slf4j
public class VoucherWechatStrategy implements VoucherShareStrategy {

  public static final String AMOUNT_CARD_TITLE = "%d元立减券，点击立即领取>>";
  public static final String BENEFIT_CARD_TITLE = "酒店专属法宝！%s，点击立即领取！>>";

  public static final String CARD_DESC = "酒店专属法宝请点击领取";

  @Autowired
  private BetterwoodConfig betterwoodConfig;

  @Resource
  private VoucherBenefitConfig voucherBenefitConfig;

  @Autowired
  private MerchantServiceClient merchantServiceClient;

  @Override
  public String getChannelId() {
    return ShareChannelEnum.WECHAT.getChannelId();
  }

  @Override
  public ShareParameters execute(ModHotelInfo modHotelInfo, List<String> templateIdList,
      String recordCode, Integer worthPrice) {
    WechatParameters parameters = new WechatParameters();
    parameters.setPath(generateH5Link(recordCode));
    parameters.setDescription(CARD_DESC);
    parameters.setImageUrl(getCoverImageUrl(modHotelInfo.getHotelCode()));
    parameters.setTitle(generateTitle(worthPrice, templateIdList));
    return parameters;
  }

  private String getCoverImageUrl(String hotelCode) {
    return betterwoodConfig.getBetterwoodShareMemberCodeImageUrl();
//    HotelInfoDTORes infoDTORes = merchantServiceClient.queryAllHotelInfoByCode(hotelCode);
//    if (infoDTORes != null) {
//      return StringUtils.isBlank(infoDTORes.getCoverImage())
//          ? betterwoodConfig.getBetterwoodShareMemberCodeImageUrl()
//          : infoDTORes.getCoverImage();
//    } else {
//      return betterwoodConfig.getBetterwoodShareMemberCodeImageUrl();
//    }
  }

  private String generateTitle(Integer worthPrice, List<String> templateIdList) {
    if (Objects.nonNull(worthPrice) && worthPrice > 0) {
      return String.format(AMOUNT_CARD_TITLE, worthPrice / 100);
    } else {
      String benefitTitle = getBenefitTitle(templateIdList);
      return String.format(BENEFIT_CARD_TITLE, benefitTitle);
    }
  }

  /**
   * 生成 H5 跳转链接
   * @param code 用户的 code
   * @return 完整的跳转 URL
   */
  private String generateH5Link(String code){
    // 使用传入的 code 格式化 h5VoucherPage 模板
    return String.format(betterwoodConfig.getH5VoucherPage(), code);
  }


  /**
   * 根据早餐>升房>延迟优先级查找第一个符合条件的权益券，并返回对应的标题
   * @param templateIds 模板ID列表
   * @return 权益券标题
   */
  private String getBenefitTitle(List<String> templateIds) {
    List<BenefitTemplate> templates = voucherBenefitConfig.getTemplates();

    return templates.stream()
        .filter(benefitTemplate -> templateIds.stream().anyMatch(templateId -> templateId.contains(benefitTemplate.getCode())))
        .findFirst()
        .map(BenefitTemplate::getName)
        .orElse("免费权益");
  }

}
