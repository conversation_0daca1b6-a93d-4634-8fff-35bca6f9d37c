package com.shands.mod.main.service.hotelservice;

import com.shands.mod.dao.model.hs.HsHotelServiceFoodConfig;
import com.shands.mod.dao.model.req.hs.hotel.FoodConfigAddOrUpdateReq;
import com.shands.mod.dao.model.res.hs.good.FoodConfigByFoodRes;
import com.shands.mod.dao.model.res.hs.hotel.FoodConfigByServiceIdRes;
import com.shands.mod.dao.model.res.hs.hotel.FoodConfigMiddleRes;
import com.shands.mod.vo.ResultVO;
import java.util.List;

public interface IHsHotelServiceFoodConfigService {

  int deleteByPrimaryKey(Integer id);

  int insert(HsHotelServiceFoodConfig record);

  int insertSelective(HsHotelServiceFoodConfig record);

  HsHotelServiceFoodConfig selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(HsHotelServiceFoodConfig record);

  int updateByPrimaryKey(HsHotelServiceFoodConfig record);

  List<FoodConfigByServiceIdRes> getByHotelServiceId(Integer companyId,
      Integer hotelServiceId);

  List<Integer> getAllId(Integer companyId,
      Integer hotelServiceId);

  Integer replaceFoodConfig(List<FoodConfigAddOrUpdateReq> list, Integer hotelServiceId);

  List<FoodConfigByServiceIdRes> foodConfigList(Integer companyId);

  List<FoodConfigByServiceIdRes> foodConfigForMiniNew(Integer companyId);

  List<FoodConfigByFoodRes> getByFood(String foodConfigId);

  ResultVO deliveryTimeNew(Integer foodConfigId);

  ResultVO detection(FoodConfigAddOrUpdateReq req);

  ResultVO detectionAll(List<FoodConfigAddOrUpdateReq> list);


  /**
   * 判断是否可以下订单    当前时间是否在服务时间之内
   * @return
   */
  Boolean placeOrder(Integer foodConfigId);


  HsHotelServiceFoodConfig selectByKey(Integer id);

  /**
   * 客房送餐 中间页面 送餐内容
   * @param companyId
   * @return
   */
  List<FoodConfigMiddleRes> middlePage(Integer companyId);

  /**
   *  餐饮内容 服务子项
   * @param id 餐饮内容id
   * @return
   */
  List<Integer> getUser(Integer id);
}
