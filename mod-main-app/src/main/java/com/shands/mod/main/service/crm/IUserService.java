package com.shands.mod.main.service.crm;

import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.User;
import com.shands.mod.dao.model.res.ReceiveOrderModeRes;
import com.shands.mod.dao.model.res.UserDeptRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.v0701.vo.DeptUsers;
import com.shands.mod.dao.model.v0701.vo.TreeVo;
import com.shands.mod.main.vo.UserStatusVO;
import com.shands.mod.vo.KeyValueVO;
import com.shands.mod.vo.ResultVO;
import com.shands.mod.vo.UserDetailVO;
import com.shands.mod.vo.UserExtendVO;
import com.shands.mod.vo.UserInfoVO;
import com.shands.mod.vo.UserReq;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/24
 * @desc 用户系统登录service
 */
public interface IUserService {

  /**
   * 用户登出
   *
   * @param token JWT
   * @return boolean
   */
  boolean logout(String token);

  /**
   * 使用token获取用户信息
   *
   * @param token token
   * @return UserInfoVO
   */
  UserInfoVO getUserByToken(String token);

  /**
   * 获取企业用户列表
   *
   * @param companyId 企业ID
   * @param search    查询条件(deptId:部门ID; name:用户姓名[*]; mobile:手机号[*]; username:登录帐号[*])
   * @return List
   */
  List<UserDetailVO> listByCompany(int companyId, UserDetailVO search);

  /**
   * 获取企业用户树形列表（带部门）
   *
   * @param companyId 企业ID
   * @return List
   */
  List<TreeVo> listUserTree(int companyId);

  /**
   * 获取企业用户树形列表（带部门)
   *
   * @param companyId 企业ID
   * @param onlyQueryFeishuDeparmentId 是否只查询有飞书部门id的，true-是，false-否
   * @return List
   */
  List<TreeVo> listUserTree(int companyId, boolean onlyQueryFeishuDeparmentId);

  /**
   * 获取部门用户列表
   *
   * @param deptId 部门ID
   * @return List
   */
  List<User> listByDepartment(int deptId);

  /**
   * 发送登录验证码
   *
   * @param mobile 手机号码
   * @return String
   */
  String sendLoginCode(String mobile);

  /**
   * 修改密码时，获取验证码
   * @param mobile
   * @return
   */
  String updatePasswordCode(String mobile);

  /**
   * 修改密码 根据验证码
   * @param mobile 手机号码
   * @param code 验证码
   * @param password 密码
   * @param platform 是否平台（true平台，false酒店）
   * @return true 验证码正确，false 不正确
   */
  ResultVO updatePasswordByCode(String mobile, String code,String password, boolean platform )
      throws Exception;

  ResultVO updatePasswordByParam(String param) throws Exception;


  ResultVO updateUcPassword(Integer ucId, String password);

  /**
   * 获取用户
   *
   * @param id 用户ID
   * @return User
   */
  User getById(int id);

  /**
   * 清空deviceId（用户注销时）
   *
   * @param token    JWT
   * @param userId   用户ID
   * @param deviceId 设备ID
   * @return boolean
   */
  boolean clearDeviceIdByToken(String token, int userId, String deviceId);

  /**
   * 获取部门用户（ID，NAME）列表
   *
   * @param deptId    部门ID
   * @param recursion 是否递归（true递归获取）
   * @return List
   */
  List<KeyValueVO> listByDept(int deptId, boolean recursion);

  /**
   * 转为UserDetailVO对象
   *
   * @param user      用户对象
   * @param companyId 企业ID
   * @return UserDetailVO
   */
  UserDetailVO toUserDetailVO(User user, int companyId);

  /**
   * 获取接单模式开关以及其他一些信息
   *
   * @param id
   * @return
   */
  ReceiveOrderModeRes getReceiveOrderModeAndMore(Integer id);

  /**
   * 修改接单模式开关
   *
   * @param
   * @return
   */
  Boolean updateReceiveOrderMode(UserInfoVO userInfoVO);

  /**
   * 获取音频播放设置
   *
   * @return
   */
  Integer getSoundSelect();

  /**
   * 设置音频播放
   *
   * @param userInfoVO
   */
  void setSoundSelect(UserInfoVO userInfoVO);

  /**
   * 用户酒店列表切换查询用户最新用户权限
   * @return
   */
  public List<UserExtendVO> getUserCompanyInfos();

  /**
   * 根据部门id查询所有用户
   * @param deptId
   * @return
   */
  public List<DeptUsers> getUserByDeptId(Integer deptId);

  List<UserDeptRes> selectByUser(Integer hotelId,String userName,String name,Integer deptId);

  List<UserDeptRes> deptForUser(Integer hotelId,String userName);

  /**
   * 后台管理系统获取机构切换列表信息
   * @return
   */
  List<ModHotelInfo> currentNew();

  /**
   * 调整用户状态
   * @param statusVO 请求信息
   * @return 响应
   */
  ResultVO<Boolean> adjustUserStatus(UserStatusVO statusVO, HttpServletRequest request);

  /**
   * 用户调整自身状态
   * @param statusVO 请求信息
   * @return 响应
   */
  ResultVO<Boolean> selfAdjustStatus(UserStatusVO statusVO, HttpServletRequest request);

  /**
   * 管理员调整
   * @param statusVO 请求信息
   * @return 响应
   */
  ResultVO<Boolean> managerAdjustStatus(UserStatusVO statusVO, HttpServletRequest request);

  /**
   * 返回用户状态列表
   * @return 用户状态列表
   */
  List<UserStatusVO> findUserStatusList();

  /**
   * 请求信息
   * @param userResp 请求信息
   * @return 员工列表
   */
  PageInfo<User> listUserV2(UserReq userResp);

}
