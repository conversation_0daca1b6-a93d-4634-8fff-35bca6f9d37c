package com.shands.mod.main.service.app.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.betterwood.base.common.model.Result;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.ModUserIndexMapper;
import com.shands.mod.dao.mapper.hs.HotelServiceMapper;
import com.shands.mod.dao.mapper.syncuc.ModDeptDao;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModRoleUcDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.ModUserIndex;
import com.shands.mod.dao.model.enums.HotelStatusEnum;
import com.shands.mod.dao.model.enums.UserRightsTypeEnum;
import com.shands.mod.dao.model.homerevision.ModHotelInfosDto;
import com.shands.mod.dao.model.hs.HotelService;
import com.shands.mod.dao.model.req.UserIndexMenuEditReq;
import com.shands.mod.dao.model.req.UserMenuReq;
import com.shands.mod.dao.model.res.PermissionTree;
import com.shands.mod.dao.model.res.UserDeptRes;
import com.shands.mod.dao.model.res.UserIndexCustomMenuRes;
import com.shands.mod.dao.model.res.UserIndexMenuRes;
import com.shands.mod.dao.model.rolepermissionnew.PermissionNew;
import com.shands.mod.dao.model.syncuc.ModDept;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.syncuc.ModUserFindDto;
import com.shands.mod.dao.model.v0701.vo.HotelInfosV2Vo;
import com.shands.mod.dao.model.v0701.vo.HotelInfosVo;
import com.shands.mod.dao.model.v0701.vo.ModUserLoginVo;
import com.shands.mod.dao.model.v0701.vo.UserInfoForElsVo;
import com.shands.mod.dao.model.v0701.vo.UserInfoVo;
import com.shands.mod.dao.model.voucher.dto.VoucherTemplateInfoVo;
import com.shands.mod.external.model.dto.ElsShandsPlusDto;
import com.shands.mod.external.model.dto.ElsShandsPlusGetGiftListDto;
import com.shands.mod.external.model.vo.ElsGiftListInfoVo;
import com.shands.mod.external.model.vo.ElsGiftListVo;
import com.shands.mod.external.service.ElsShandsPlusService;
import com.shands.mod.main.config.CateCardConfig;
import com.shands.mod.main.config.DisplayTextConfig;
import com.shands.mod.main.config.VoucherConfig;
import com.shands.mod.main.service.app.UcUserService;
import com.shands.mod.main.service.app.UserCenterService;
import com.shands.mod.main.service.common.UserInfoCommonService;
import com.shands.mod.main.service.crm.UcLoginService;
import com.shands.mod.main.util.CodeBoxSignUtils;
import com.shands.mod.main.util.HttpClientUtil;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.StringReplaceUtils;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.ResultVO;
import com.shands.uc.model.req.v3.auth.RoleInfo;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
@Slf4j
public class UserCenterServiceImpl implements UserCenterService {

  private static final String USER_LOGIN_SESSION = "mod3:USER_LOsGIN_SESSION:";

  private static final long USER_LOGIN_SESSION_TIME = 86400;

  @Value("${app.token.expiration:259200}")
  private long expiration;

  @Value("${app.gw.host}")
  private String host;

  @Value("${officialOrder.appKey}")
  private  String appKey;

  @Value("${officialOrder.appSecret}")
  private  String appSecret;

  @Value("${els.memberUrl:http://test2.shands.cn/elsapp}")
  private String elsMemberUrl;

  //  @Value("${haogu.hgSecret}")
  private String hgSecret;

  //  @Value("${haogu.hgUrl}")
  private String hgUrl;

  @Resource
  private ModRoleUcDao modRoleUcDao;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Resource
  private ModUserDao modUserDao;

  @Resource
  HotelServiceMapper hotelServiceMapper;

  @Resource
  private ModDeptDao deptDao;

  @Resource
  private ModUserIndexMapper userIndexMapper;


  private final RedisTemplate redisTemplate;

  private final ElsShandsPlusService elsShandsPlusService;

  private final UcUserService ucUserService;

  private final UcLoginService ucLoginService;

  @Resource
  private CateCardConfig cateCardConfig;

  @Resource
  private VoucherConfig voucherConfig;

  @Resource
  private DisplayTextConfig displayTextConfig;

  private final UserInfoCommonService userInfoCommonService;

  public UserCenterServiceImpl(RedisTemplate redisTemplate,
      ElsShandsPlusService elsShandsPlusService,
      UcUserService ucUserService, UcLoginService ucLoginService,
      UserInfoCommonService userInfoCommonService) {
    this.redisTemplate = redisTemplate;
    this.elsShandsPlusService = elsShandsPlusService;
    this.ucUserService = ucUserService;
    this.ucLoginService = ucLoginService;
    this.userInfoCommonService = userInfoCommonService;
  }

  @Override
  public ModUserLoginVo getUserByToken(String token) {

    if(!StringUtils.isNotBlank(token)){
      return null;
    }

    ModUserLoginVo resultVo;
    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    Object obj = this.redisTemplate.opsForValue().get(key);
    if (obj != null) {
      resultVo = (ModUserLoginVo) obj;
    }else{
      return null;
    }

    //外包人员，直接跳过
    if("OUT".equals(resultVo.getPlatform())){
      return resultVo;
    }

    //定期校验当前在职状态（24小时执行一次）
    //定期校验用户当前状态 or uc离职
    String sessionKey = USER_LOGIN_SESSION + token;
    if(redisTemplate.hasKey(sessionKey)){
      return resultVo;
    }else {

      String mobile = resultVo.getMobile();
      //查询用户通宝用户信息
      UserAuthInfo userAuthInfo = ucUserService.ucLoginByUsername(mobile);

      if (userAuthInfo == null) {
        return null;
      }

      //解析通宝用户信息
      if (userAuthInfo.getRoles() == null || userAuthInfo.getRoles().isEmpty()) {
        return null;
      }

      //更新用户信息
      ucLoginService.updUcUserInfo(userAuthInfo);

      //如果用户为离职状态，则直接返回空对象
      if (!userAuthInfo.isStatus()) {
        return null;
      }

      //更新用户权限信息
      List<RoleInfo> roleInfos = userAuthInfo.getRoles();
      Map<String,RoleInfo> roleInfoMap = roleInfos.stream().
          collect(Collectors.toMap(RoleInfo :: getCode, RoleInfo -> RoleInfo));

      List<String> keys = new ArrayList<>();
      for(String kk : roleInfoMap.keySet()){
        keys.add(kk);
      }

      resultVo.setRoles(keys);

      //重新缓存数据
      String setKey = Tools.buildKey(BaseConstants.CACHE_USER, token);
      this.redisTemplate.opsForValue().set(setKey, resultVo, this.expiration, TimeUnit.SECONDS);
      //设置24小时定期更新用户信息
      this.redisTemplate.opsForValue().set(sessionKey, resultVo, USER_LOGIN_SESSION_TIME, TimeUnit.SECONDS);
    }

    return resultVo;
  }

  @Override
  public List<HotelInfosVo> getUserHotelInfos(ModHotelInfo hotelInfo) {

    //查询酒店列表
    Integer ucCompanyId = ThreadLocalHelper.getCompanyId();
    List<String> roles = ThreadLocalHelper.getROLES();

    if(roles == null || roles.isEmpty()){
      return null;
    }

    //如果是admin或者IT维护人员，查询所有微管家已上线酒店列表信息
    if(!roles.contains("admin") && !roles.contains("It_maintainer")){
      hotelInfo.setUcCompanyId(ucCompanyId);
    }else{
      hotelInfo.setMiniprogramStatus(1);
    }

    return modHotelInfoDao.queryUserHotelInfos(hotelInfo);
  }

  @Override
  public List<HotelInfosV2Vo> getUserHotelInfosV2(ModHotelInfo hotelInfo) {

    //查询酒店列表
    ModHotelInfosDto modHotelInfosDto = new ModHotelInfosDto();

    List<String> codes = userInfoCommonService.getUserSwitchs(ThreadLocalHelper.getUser().getId());

    modHotelInfosDto.setHotelCodes(codes);
    modHotelInfosDto.setHotelName(hotelInfo.getHotelName());

    List<HotelInfosVo> infosVos = modHotelInfoDao.queryUserHotelInfosUc(modHotelInfosDto);

    List<HotelInfosV2Vo> vos = infosVos.
        stream().collect(Collectors.groupingBy(HotelInfosVo::getHotelInfoType))
        .entrySet().stream().sorted(Map.Entry.comparingByKey())
        .map(e -> new HotelInfosV2Vo(e.getKey() == 0 ? "集团" : "酒店",e.getValue()))
        .collect(Collectors.toList());

    vos.forEach(x -> {

      if("集团".equals(x.getHotelInfoType())){
        HotelInfosVo newList = modHotelInfoDao.queryHotelInfosByUserId(ThreadLocalHelper.getUser().getId());
        if(newList != null && ("000001".equals(newList.getHotelCode()) || "HOTELGO".equals(newList.getHotelCode()))){
          newList.setHotelName("德胧集团");
        }
        List<HotelInfosVo> newResVos = new ArrayList<>();
        newResVos.add(newList);
        x.setHotelInfos(newResVos);
      }
    });

    return vos;
  }

  @Override
  public List<PermissionTree> getUserPermissions() {

    List<String> roles = ThreadLocalHelper.getROLES();

    if(roles == null || roles.isEmpty()){
      return null;
    }

    //返回对象
    List<PermissionTree> result = new ArrayList<>();

    //查询用户所有权限代码
    List<String> param = modRoleUcDao.selectPcPerms(roles,"APP");

    if(param == null || param.isEmpty()){
      throw new RuntimeException("用户角色信息查询为空请联系管理员！");
    }

    //根据权限代码查询用户app菜单列表
    List<PermissionNew> permissionNews = modRoleUcDao.selectAllByCode(param);

    //组装数据返回
    return this.getTreeNew(null,permissionNews,result);
  }

  @Override
  public UserIndexMenuRes getUserIndexMenu(Integer userId) {

    Assert.notNull(userId, "用户id不能为空");

    ModUserIndex userIndex = new ModUserIndex();
    userIndex.setUserId(userId);
    List<ModUserIndex> indexList = userIndexMapper.queryAll(userIndex);

    //用户所有权限菜单
    List<PermissionTree> userPermissions = getUserPermissions();

    UserIndexMenuRes res = new UserIndexMenuRes();
    if (userPermissions.isEmpty()){
      return res;
    }

    //提取所有菜单列表
    List<PermissionTree> menus = new ArrayList<>();
    for (PermissionTree userPermission : userPermissions) {
      menus.addAll(userPermission.getChild());
    }

    //所有菜单排序
    menus.sort(Comparator.comparing(PermissionTree::getSort, Comparator.nullsLast(Integer::compareTo)));

    List<UserIndexCustomMenuRes> customMenuResList = new ArrayList<>();
    //用户无自定义首页菜单 展示前7个图标
    if (indexList.isEmpty()){

      for (PermissionTree menu : menus) {
        UserIndexCustomMenuRes customMenuRes = new UserIndexCustomMenuRes();
        BeanUtils.copyProperties(menu, customMenuRes);

        customMenuResList.add(customMenuRes);
      }

      if (customMenuResList.size() > 7){

        res.setUserMenu(customMenuResList.subList(0, 7));
      }else {

        res.setUserMenu(customMenuResList);
      }

    }else {

      Map<Integer, PermissionTree> permissionTreeMap = menus.stream()
          .collect(Collectors.toMap(PermissionTree::getPermissionId, t -> t, (v1, v2) -> v2));

      for (ModUserIndex modUserIndex : indexList) {

        Integer permissionId = modUserIndex.getPermissionId();

        if (permissionTreeMap.containsKey(permissionId)){
          UserIndexCustomMenuRes customMenuRes = new UserIndexCustomMenuRes();
          PermissionTree permissionTree = permissionTreeMap.get(permissionId);
          BeanUtils.copyProperties(permissionTree, customMenuRes);

          customMenuResList.add(customMenuRes);
        }

      }
      res.setUserMenu(customMenuResList);

    }

    res.setAllMenu(userPermissions);

    return res;
  }

  /**
   * 会员发展设置URL
   * @param permissionTree
   */
  private void setMemberUrl(PermissionTree permissionTree) {
    Optional<String> url = getMemberUrl();
    url.ifPresent(permissionTree::setUrl);
  }

  private void setMemberUrl(PermissionNew perm) {
    Optional<String> url = getMemberUrl();
    url.ifPresent(perm::setUrl);
  }

  private Optional<String> getMemberUrl() {
    int userId = ThreadLocalHelper.getUser().getId();
    List<String> roles = ThreadLocalHelper.getROLES();
    if (CollectionUtils.isEmpty(roles) || !roles.contains("Fan_Xiaoer")) {
      return Optional.empty();
    }
    return Optional.of(generateMemberUrl(userId, roles));
  }

  private String generateMemberUrl(int userId, List<String> roles) {
    int companyId = ThreadLocalHelper.getCompanyId();
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(companyId);
    ModUser modUser = modUserDao.queryById(userId);

    if (org.springframework.util.StringUtils.hasText(modHotelInfo.getRegisterType())) {
      switch (modHotelInfo.getRegisterType()) {
        case "BDW_CUSTOMER_GZH":
          if (roles.contains("EWMZY")) {
            return elsMemberUrl + "/appDevVip?mobile=" + modUser.getMobile() + "&memberType=customer&qrCodeType=fixed";
          } else {
            return elsMemberUrl + "/appDevVip?mobile=" + modUser.getMobile() + "&memberType=customer&qrCodeType=temporary";
          }
        case "BDW_CUSTOMER_MINI":
          return elsMemberUrl + "/appDevVip?mobile=" + modUser.getMobile() + "&memberType=customer&qrCodeType=mini";
        default:
          return elsMemberUrl + "/appDevVip?mobile=" + modUser.getMobile();
      }
    }
    return elsMemberUrl + "/appDevVip?mobile=" + modUser.getMobile();
  }

  @Override
  public UserIndexMenuRes getUserIndexMenuV2(Integer userId) {

    int iconCount = 7;
    if(redisTemplate.hasKey(BaseConstants.HOME_PAGE_ICON_COUNT)){
      iconCount = 9;
    }

    Assert.notNull(userId, "用户id不能为空");

    ModUserIndex userIndex = new ModUserIndex();
    userIndex.setUserId(userId);
    List<ModUserIndex> indexList = userIndexMapper.queryAll(userIndex);

    //用户所有权限菜单
    List<PermissionTree> userPermissions = getUserPermissionsV2();

    UserIndexMenuRes res = new UserIndexMenuRes();
    if (userPermissions.isEmpty()){
      return res;
    }

    //提取所有菜单列表
    List<PermissionTree> menus = new ArrayList<>();
    for (PermissionTree userPermission : userPermissions) {
      List<PermissionTree> child = userPermission.getChild();
      Iterator<PermissionTree> iterator = child.iterator();
      while (iterator.hasNext()){
        PermissionTree next = iterator.next();
        if(StrUtil.containsAny(next.getPermissionCode(),"shans_plus_")){
          iterator.remove();
        }
        if(StrUtil.containsAny(next.getPermissionCode(),"member_develop")){
          setMemberUrl(next);
        }
        if("hotel_coupon".equals(next.getPermissionCode()) && !isShowHotelCoupon()) {
          iterator.remove();
        }
      }
      menus.addAll(userPermission.getChild());
    }

    //所有菜单排序
    menus.sort(Comparator.comparing(PermissionTree::getSort, Comparator.nullsLast(Integer::compareTo)));

    List<UserIndexCustomMenuRes> customMenuResList = new ArrayList<>();
    //用户无自定义首页菜单 展示前9个图标
    if (indexList.isEmpty()){

      for (PermissionTree menu : menus) {
        UserIndexCustomMenuRes customMenuRes = new UserIndexCustomMenuRes();
        BeanUtils.copyProperties(menu, customMenuRes);

        //常用功能菜单首页icon不展示
        if(menu.getParentId() != null && menu.getParentId() != 52){
          customMenuResList.add(customMenuRes);
        }
      }

      if (customMenuResList.size() > iconCount){
        res.setUserMenu(customMenuResList.subList(0, iconCount));
      }else {
        res.setUserMenu(customMenuResList);
      }

    }else {

      Map<Integer, PermissionTree> permissionTreeMap = menus.stream()
          .collect(Collectors.toMap(PermissionTree::getPermissionId, t -> t, (v1, v2) -> v2));

      for (ModUserIndex modUserIndex : indexList) {

        Integer permissionId = modUserIndex.getPermissionId();

        if (permissionTreeMap.containsKey(permissionId)){
          UserIndexCustomMenuRes customMenuRes = new UserIndexCustomMenuRes();
          PermissionTree permissionTree = permissionTreeMap.get(permissionId);
          BeanUtils.copyProperties(permissionTree, customMenuRes);

          customMenuResList.add(customMenuRes);
        }

      }

      //兼容历史菜单ICON配置 跟现有权限匹配为空情况
      if(customMenuResList == null || customMenuResList.isEmpty()){
        for (PermissionTree menu : menus) {
          UserIndexCustomMenuRes customMenuRes = new UserIndexCustomMenuRes();
          BeanUtils.copyProperties(menu, customMenuRes);

          //常用功能菜单首页icon不展示
          if(menu.getParentId() != null && menu.getParentId() != 52){
            customMenuResList.add(customMenuRes);
          }
        }

        if (customMenuResList.size() > iconCount){
          res.setUserMenu(customMenuResList.subList(0, iconCount));
        }else {
          res.setUserMenu(customMenuResList);
        }
      }else{
        res.setUserMenu(customMenuResList);
      }

    }

    // 要求将售卖百达卡移菜单到用户自定义菜单栏展示
    this.cateCardMenuForceTopCustom(res,menus,iconCount);

    res.setAllMenu(userPermissions);

    return res;
  }


  @Override
  public ResultVO saveUserIndexMenu(UserIndexMenuEditReq req) {
    Assert.notNull(req.getUserId(), "用户id不能为空");

    List<UserMenuReq> menuList = req.getMenuList();
    Assert.isTrue(menuList != null && !menuList.isEmpty(), "首页自定义菜单不能为空");

    ModUserIndex userIndex = new ModUserIndex();
    userIndex.setUserId(req.getUserId());
    userIndexMapper.deleteByUser(userIndex);

    Date date = new Date();

    List<ModUserIndex> insertList = new ArrayList<>();
    for (UserMenuReq menuReq : menuList) {
      ModUserIndex index = new ModUserIndex();
      index.setCompanyId(req.getCompanyId());
      index.setUserId(req.getUserId());
      index.setPermissionId(menuReq.getPermissionId());
      index.setSort(menuReq.getSort());
      index.setCreateTime(date);

      insertList.add(index);

    }

    if (userIndexMapper.insertBatch(insertList) > 0){
      return ResultVO.success();
    }

    return ResultVO.failed("保存失败");
  }

  @Override
  public UserInfoVo getUserInfo() {
    Integer userId = ThreadLocalHelper.getUser().getId();
    UserInfoVo vo = modUserDao.queryUserInfoById(userId);
    if("000001".equals(vo.getHotelCode()) || "HOTELGO".equals(vo.getHotelCode())){
      vo.setHotelName("德胧集团");
    }
    return vo;
  }

  @Override
  public UserInfoForElsVo getUserInfoByToken(String token) {

    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    if (!this.redisTemplate.hasKey(key)){
      return null;
    }

    ModUserLoginVo userLoginVo = (ModUserLoginVo) this.redisTemplate.opsForValue().get(key);

    if (Objects.isNull(userLoginVo)){
      return null;
    }

    Integer userid = userLoginVo.getId();

    ModUser modUser = modUserDao.queryById(userid);

    if (Objects.isNull(modUser)){
      return null;
    }

    Integer deptId = modUser.getDeptId();
    Integer ucCompanyId = modUser.getUcCompanyId();

    //获取部门及酒店信息
    ModDept modDept = deptDao.queryByUcDeptId(deptId);
    ModHotelInfo hotelInfo = modHotelInfoDao.queryByUcCompanyId(ucCompanyId);

    UserInfoForElsVo vo = new UserInfoForElsVo();
    vo.setUcId(modUser.getUcId());
    vo.setMobile(modUser.getMobile());
    vo.setName(modUser.getName());
    vo.setHotelCode(hotelInfo != null ? hotelInfo.getHotelCode() : null);
    vo.setHotelName(hotelInfo != null ? hotelInfo.getHotelName() : null);
    vo.setDeptId(modDept != null ? modDept.getUcId() : null);
    vo.setDeptName(modDept != null ? modDept.getName() : null);

    return vo;
  }

  @Override
  public UserInfoForElsVo getUserInfoByUcid(String ucid) {

    ModUser modUser = modUserDao.queryByUcid(Integer.valueOf(ucid));

    if (Objects.isNull(modUser)){
      return null;
    }

    Integer deptId = modUser.getDeptId();
    Integer ucCompanyId = modUser.getUcCompanyId();

    //获取部门及酒店信息
    ModDept modDept = deptDao.queryByUcDeptId(deptId);
    ModHotelInfo hotelInfo = modHotelInfoDao.queryByUcCompanyId(ucCompanyId);

    UserInfoForElsVo vo = new UserInfoForElsVo();
    vo.setUcId(modUser.getUcId());
    vo.setMobile(modUser.getMobile());
    vo.setName(modUser.getName());
    vo.setHotelCode(hotelInfo != null ? hotelInfo.getHotelCode() : null);
    vo.setHotelName(hotelInfo != null ? hotelInfo.getHotelName() : null);
    vo.setDeptId(modDept != null ? modDept.getUcId() : null);
    vo.setDeptName(modDept != null ? modDept.getName() : null);

    return vo;
  }

  @Override
  public List<UserInfoForElsVo> batchGetUserInfoByUcIds(List<String> ucIds) {
    return modUserDao.batchGetUserInfoByUcIds(ucIds);
  }

  @Override
  public UserInfoForElsVo getUserInfoByMobile(String mobile) {

    ModUser modUser = modUserDao.queryByMobile(mobile);

    if (Objects.isNull(modUser)){
      return null;
    }

    Integer deptId = modUser.getDeptId();
    Integer ucCompanyId = modUser.getUcCompanyId();

    //获取部门及酒店信息
    ModDept modDept = deptDao.queryByUcDeptId(deptId);
    ModHotelInfo hotelInfo = modHotelInfoDao.queryByUcCompanyId(ucCompanyId);

    UserInfoForElsVo vo = new UserInfoForElsVo();
    vo.setUcId(modUser.getUcId());
    vo.setMobile(modUser.getMobile());
    vo.setName(modUser.getName());
    vo.setHotelCode(hotelInfo != null ? hotelInfo.getHotelCode() : null);
    vo.setHotelName(hotelInfo != null ? hotelInfo.getHotelName() : null);
    vo.setDeptId(modDept != null ? modDept.getUcId() : null);
    vo.setDeptName(modDept != null ? modDept.getName() : null);

    return vo;
  }

  @Override
  public List<UserInfoForElsVo> batchGetUserInfoByMobileList(List<String> mobileList) {
    return modUserDao.batchGetUserInfoByMobileList(mobileList);
  }


  @Override
  public List<ElsGiftListInfoVo> getGiftListInfo(Integer userId, String mobile, Integer companyId) {

    Assert.notNull(companyId, "酒店id不能为空");
    Assert.notNull(userId, "用户id不能为空");
    Assert.hasText(mobile, "手机号不能为空");

    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(companyId);
    Assert.notNull(modHotelInfo, "未查询到酒店信息");

    ElsShandsPlusDto elsShandsPlusDto = new ElsShandsPlusDto();
    elsShandsPlusDto.setUrl(host);
    elsShandsPlusDto.setAppKey(appKey);
    elsShandsPlusDto.setSecret(appSecret);

    ElsShandsPlusGetGiftListDto getGiftListDto = new ElsShandsPlusGetGiftListDto();
    getGiftListDto.setMobile(mobile);
    getGiftListDto.setHotelCode(modHotelInfo.getHotelCode());

    ElsGiftListVo elsGiftListVo = elsShandsPlusService.getGiftList(elsShandsPlusDto,getGiftListDto);

    List<ElsGiftListInfoVo> giftList = new ArrayList<>();
    if (elsGiftListVo != null && elsGiftListVo.getGiftList() != null && !elsGiftListVo.getGiftList().isEmpty()){

      giftList = elsGiftListVo.getGiftList();

    }

    return giftList;
  }

  @Override
  public String getHGTOken(String mobile, String hotelCode) {

    long timeStamp = System.currentTimeMillis();

    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(hgSecret);
    stringBuilder.append("&");
    stringBuilder.append(mobile);
    stringBuilder.append("&");
    stringBuilder.append(hotelCode);
    stringBuilder.append("&");
    stringBuilder.append(timeStamp);
    String localSign = CodeBoxSignUtils.md5(stringBuilder.toString());

    Map<String,Object> map = new HashMap<>();
    map.put("mobile",mobile);
    map.put("hotelCode",hotelCode);
    map.put("sign",localSign);
    map.put("timestamp",timeStamp);

    JSONObject jsonObject = null;
    try {

      log.info("[皓谷云jwToken获取请求参数：{}]",JSONObject.toJSONString(map));
      jsonObject = HttpClientUtil
          .doJsonPost(hgUrl,null,map);

      log.info("[皓谷云jwToken获取返回参数：{}]",jsonObject.toString());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RuntimeException(e.getMessage());
    }

    if(jsonObject == null || jsonObject.get("result") == null){
      throw new RuntimeException(jsonObject.get("msg").toString());
    }

    Integer resultCode = (Integer) jsonObject.get("result");

    if(resultCode != 0){
      throw new RuntimeException(jsonObject.get("msg").toString());
    }

    String hgToken = jsonObject.getJSONObject("retVal").getString("jwtToken");

    return hgToken;
  }

  @Override
  public List<PermissionTree> getUserPermissionsV2() {

    //返回对象
    List<PermissionTree> result = new ArrayList<>();

    //查询用户所有权限代码
    List<String> param = userInfoCommonService.getUserRights(
        ThreadLocalHelper.getUser().getId(),
        ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);

    if(param == null || param.isEmpty()){
      throw new RuntimeException("用户角色信息查询为空请联系管理员！");
    }

    //根据权限代码查询用户app菜单列表
    List<PermissionNew> permissionNews = modRoleUcDao.selectAllByCode(param);
    //首页改版，所有人都添加常用功能菜单 兼容老版本 避免旧版本出现常用功能icon
    PermissionNew permissionNew = modRoleUcDao.selectOneByCode("common_fun");
    permissionNews.add(permissionNew);

    permissionNews.parallelStream().forEach(perm -> {
      String icon = perm.getIcon();
      if (StringUtils.contains(icon, "oss-cn-shenzhen.aliyuncs.com")) {
        perm.setIcon(icon.replace("oss-cn-shenzhen.aliyuncs.com", "betterwood.com"));
      }
    });

    //组装数据返回
    return this.getTreeNew(null,permissionNews,result);
  }

  @Override
  public List<UserDeptRes> getDeptUsers(ModUserFindDto modUserFindDto) {

    List<UserDeptRes> vos = new ArrayList<>();

    modUserFindDto.setHotelId(null == modUserFindDto.getHotelId() ? ThreadLocalHelper.getCompanyId() : modUserFindDto.getHotelId());

    List<UserDeptRes> userDeptRes = modUserDao.getDeptUsers(modUserFindDto);

    Map<Integer, List<UserDeptRes>> listMap = userDeptRes.stream()
        .collect(Collectors.groupingBy(UserDeptRes::getDeptId));

    Set<Integer> deptId = listMap.keySet();
    deptId.forEach(key->{
      UserDeptRes dept = new UserDeptRes();
      dept.setId(key);
      dept.setName(listMap.get(key).get(0).getDeptName());
      dept.setUsers(listMap.get(key));
      vos.add(dept);
    });
    return vos;
  }

  @Override
  public List<UserDeptRes> getDeptUsersWorkOrder(ModUserFindDto modUserFindDto) {

    List<UserDeptRes> vos = new ArrayList<>();

    List<UserDeptRes> userDeptRes = modUserDao.getDeptUsers(modUserFindDto);

    Map<Integer, List<UserDeptRes>> listMap = userDeptRes.stream()
        .collect(Collectors.groupingBy(UserDeptRes::getDeptId));

    Set<Integer> deptId = listMap.keySet();
    deptId.forEach(key->{
      UserDeptRes dept = new UserDeptRes();
      dept.setId(key);
      dept.setName(listMap.get(key).get(0).getDeptName());
      dept.setUsers(listMap.get(key));
      vos.add(dept);
    });
    return vos;
  }

  @Override
  public List<UserDeptRes> getHotelUsers(ModUserFindDto modUserFindDto) {
    modUserFindDto.setHotelId(ThreadLocalHelper.getCompanyId());
    List<UserDeptRes> vos = modUserDao.getDeptUsers(modUserFindDto);
    return vos;
  }

  private List<PermissionTree> getTreeNew(PermissionTree parentNode,List<PermissionNew> permissions,List<PermissionTree> result){

    //当前节点当做父节点
    Integer pid = parentNode == null ? null:parentNode.getPermissionId();

    //当前节点的子节点集合
    List<PermissionTree> childList = Lists.newArrayList();

    List<HotelService> hotelServices = hotelServiceMapper
        .allServiceByGroupId(ThreadLocalHelper.getCompanyId());

    Map<String, HotelService> serviceMap = new HashMap<>();
    for (HotelService service : hotelServices) {
      serviceMap.put(service.getServiceType(), service);
    }

    //循环查找当前节点的所有子节点
    for (PermissionNew permission: permissions) {
      if (Objects.equals(pid, permission.getParentId())){
        PermissionTree child = new PermissionTree();
        child.setPermissionId(permission.getId());
        child.setPermissionName(permission.getPermissionName());
        child.setPermissionCode(permission.getPermissionCode());
        child.setIcon(permission.getIcon());
        child.setSort(permission.getSort());
        child.setParentId(permission.getParentId());

        if (permission.getUrl() != null && !"".equals(permission.getUrl())){
          child.setUrl(permission.getUrl());
        }

        HotelService findService = new HotelService();

        String key = "";
        if(("deliver_service").equals(permission.getTarget())){
          key = "DELIVER";
        }else if(("clean_service").equals(permission.getTarget())){
          key = "CLEAN";
        }else if(("repair_service").equals(permission.getTarget())){
          key = "REPAIR";
        }else if(("access_service").equals(permission.getTarget())){
          key = "ACCESS";
        }

        if (!"".equals(key) && serviceMap.containsKey(key)){
          findService = serviceMap.get(key);
        }

        if(findService != null){

          if(org.springframework.util.StringUtils.hasText(findService.getServiceType())){
            child.setServiceType(findService.getServiceType());
          }

          if(findService.getId() != null){
            child.setHotelServiceId(findService.getId());
          }
        }

          childList.add(child);
        //递归下面所有层级的节点
        getTreeNew(child,permissions,result);
      }

    }

    if (parentNode != null){
      parentNode.setChild(childList);
    }else {
      try{
        setShandsPlusUrl(childList);
      }catch (Exception e){
        log.error(e.getMessage(), e);
      }
      result = childList;
    }

    return result;
  }

  /**
   * 获取用户德胧生态权限
   * @param childList
   */
  private void setShandsPlusUrl(List<PermissionTree> childList){

    Iterator<PermissionTree> iterator = childList.iterator();
    while (iterator.hasNext()) {

      PermissionTree next = iterator.next();

      if("gift_sales".equals(next.getPermissionCode())){

        List<PermissionTree> childs = new ArrayList<>();

        ElsShandsPlusDto elsShandsPlusDto = new ElsShandsPlusDto();
        elsShandsPlusDto.setUrl(host);
        elsShandsPlusDto.setAppKey(appKey);
        elsShandsPlusDto.setSecret(appSecret);

        int companyId = ThreadLocalHelper.getCompanyId();
        String mobile = ThreadLocalHelper.getUser().getMobile();
        ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(companyId);
        if(modHotelInfo == null){
          throw new RuntimeException("酒店信息查询为空");
        }

        ElsShandsPlusGetGiftListDto getGiftListDto = new ElsShandsPlusGetGiftListDto();
        getGiftListDto.setMobile(mobile);

        getGiftListDto.setHotelCode(modHotelInfo.getHotelCode());

        ElsGiftListVo elsGiftListVo = new ElsGiftListVo();

        elsGiftListVo = elsShandsPlusService.getGiftList(elsShandsPlusDto,getGiftListDto);

        if(elsGiftListVo != null && elsGiftListVo.getGiftList() != null && !elsGiftListVo.getGiftList().isEmpty()){

          List<ElsGiftListInfoVo> giftList = elsGiftListVo.getGiftList();
          giftList.forEach( y -> {

            PermissionTree cc = new PermissionTree();
            cc.setPermissionId(y.getGiftId());
            cc.setPermissionName(y.getGiftName());
            cc.setPermissionCode("shans_plus_" + y.getGiftId());
            cc.setIcon("http://oss.kaiyuanhotels.com/dfs/group1/prod/mod3/company/libao.png");
            cc.setUrl(y.getGiftUrl());

            childs.add(cc);
          });
          next.setChild(childs);
        }else{
          //如果没有礼包列表，则移除该结构
          iterator.remove();
        }
      }
    }
  }


  /**
   * 如果用户拥有百达卡售卖菜单按钮，将菜单提到常住栏
   * @param res
   * @param menus
   * @param menuTotal
   */
  private void cateCardMenuForceTopCustom(UserIndexMenuRes res, List<PermissionTree> menus,
      int menuTotal) {
    if (!cateCardConfig.isCateCardMenuCodeTop()) {
      return;
    }
    //用户已自定义
    List<UserIndexCustomMenuRes> userMenuList = res.getUserMenu();
    List<String> customMenuCodeList = userMenuList.stream()
        .map(UserIndexCustomMenuRes::getPermissionCode).collect(Collectors.toList());
    if (customMenuCodeList.contains(cateCardConfig.getCateCardMenuCode())) {
      return;
    }

    Optional<PermissionTree> cateCardMenuOptional = menus.stream().filter(
        permissionTree -> Objects.equals(permissionTree.getPermissionCode(),
            cateCardConfig.getCateCardMenuCode())).findFirst();
    // 用户无百达卡菜单权限
    if (cateCardMenuOptional.isEmpty()) {
      return;
    }
    PermissionTree permissionTree = cateCardMenuOptional.get();
    UserIndexCustomMenuRes userIndexCustomMenuRes = new UserIndexCustomMenuRes();
    BeanUtils.copyProperties(permissionTree, userIndexCustomMenuRes);

    // 大于规定个数，则移除最后一个
    if (userMenuList.size() >= menuTotal) {
      userMenuList.remove(userMenuList.size() - 1);
      userMenuList.add(userIndexCustomMenuRes);
    } else {
      userMenuList.add(userIndexCustomMenuRes);
    }
  }


  private boolean isShowHotelCoupon(){
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId());
    // 如果 pmsFlag 为空，返回 true
    if (StringUtils.isEmpty(modHotelInfo.getPmsFlag())) {
        return true;
    }
    // 皓古不展示
    if (!"IGROUP".equals(modHotelInfo.getPmsFlag())) {
      return false;
    }
    // 退出或中止或空 不展示
    boolean invalid = HotelStatusEnum.findEnumByCode(modHotelInfo.getContract()).isInvalid();
    if (invalid) {
      return false;
    }
    //判断门店是否有权限
    if(voucherConfig.getVoucherHotelSwitch()) {
      if(voucherConfig.getVoucherHotelBlackList().contains(modHotelInfo.getHotelCode())) {
        return false;
      }
    } else {
      if(!voucherConfig.getVoucherHotelWhiteList().contains(modHotelInfo.getHotelCode())) {
        return false;
      }
    }
    return true;
  }

  /**
   * 获取用户首页工具列表
   *
   * @return
   */
  @Override
  public List<UserIndexCustomMenuRes> getIndexMebTools() {
    //查询用户所有权限代码
    List<String> param = userInfoCommonService.getUserRights(
        ThreadLocalHelper.getUser().getId(),
        ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);

    if(CollectionUtils.isEmpty(param)){
      throw new RuntimeException("用户角色信息查询为空请联系管理员！");
    }

    //根据权限代码查询用户app导航工具栏列表
    List<PermissionNew> permissionNews = modRoleUcDao.selectAllByCode(param);
    PermissionNew navToolsMenu = permissionNews.stream()
        .filter(permissionNew -> "index_nav_tools".equals(permissionNew.getPermissionCode()))
        .findFirst().orElse(null);
    if (Objects.isNull(navToolsMenu)) {

      return new ArrayList<>();
    }
    boolean showHotelCoupon = isShowHotelCoupon();
    List<PermissionNew> toolsList = permissionNews.stream()
        .filter(permissionNew -> navToolsMenu.getId().equals(permissionNew.getParentId()) &&
            // 如果权限码是"hotel_coupon"，则需要showHotelCoupon为true才能通过过滤
            (!"hotel_coupon".equals(permissionNew.getPermissionCode()) || showHotelCoupon))
        .collect(Collectors.toList());

    Map<String, String> modMenuIconMap = JSON.parseObject(displayTextConfig.getModMenuIcon(),
        new TypeReference<>() {
        });

    List<UserIndexCustomMenuRes> customMenuResList = new ArrayList<>();
    for (PermissionNew perm : toolsList) {
      // 首页图标替换
      if (modMenuIconMap.containsKey(perm.getPermissionCode())) {
        perm.setIcon(modMenuIconMap.get(perm.getPermissionCode()));
      }

      if(StrUtil.containsAny(perm.getPermissionCode(),"member_develop")){
        setMemberUrl(perm);
      }

      if(StrUtil.contains(perm.getIcon(),"oss-cn-shenzhen.aliyuncs.com")){
        String updatedIcon = StringReplaceUtils.replaceNotNull(perm.getIcon(), "oss-cn-shenzhen.aliyuncs.com", "betterwood.com");
        perm.setIcon(updatedIcon);
      }

      UserIndexCustomMenuRes customMenuRes = new UserIndexCustomMenuRes();
      BeanUtils.copyProperties(perm, customMenuRes);
      customMenuResList.add(customMenuRes);
    }

    return customMenuResList;
  }
}
