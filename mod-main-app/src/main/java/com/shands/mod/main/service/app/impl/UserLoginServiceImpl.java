package com.shands.mod.main.service.app.impl;

import com.alibaba.fastjson.JSON;
import com.lark.oapi.service.contact.v3.enums.EmployeeTypeEnum;
import com.lark.oapi.service.ext.model.UserAccessTokenInfo;
import com.lark.oapi.service.ext.model.UserInfo;
import com.shands.mod.dao.mapper.UserMapper;
import com.shands.mod.dao.mapper.outsource.OutUserRoleDao;
import com.shands.mod.dao.mapper.rolepermissionnew.ModRoleCheckMapper;
import com.shands.mod.dao.mapper.syncuc.ModDeptDao;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.User;
import com.shands.mod.dao.model.enums.UserStatusEnum;
import com.shands.mod.dao.model.res.syncuc.FeiShuDeptRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.v0701.dto.ModUserLoginDto;
import com.shands.mod.dao.model.v0701.dto.ModUserSecurityVerificationDto;
import com.shands.mod.dao.model.v0701.dto.OutSourceFeiShuLoginDto;
import com.shands.mod.dao.model.v0701.vo.ModUserLoginVo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.model.resp.MiniAppToken;
import com.shands.mod.external.service.FeiShuApiService;
import com.shands.mod.external.service.FeishuContactService;
import com.shands.mod.main.config.AccountSecurityConfig;
import com.shands.mod.main.enums.ValidateCodeEnum;
import com.shands.mod.main.service.app.UcUserService;
import com.shands.mod.main.service.app.UserLoginService;
import com.shands.mod.main.service.common.HotelInfoCommonService;
import com.shands.mod.main.service.crm.IUserService;
import com.shands.mod.main.service.crm.UcLoginService;
import com.shands.mod.main.service.plan.HsPlanSanitateTaskService;
import com.shands.mod.main.service.syncuc.IModUserTokenService;
import com.shands.mod.main.util.BaiDaWuRSAUtils;
import com.shands.mod.main.util.DateUtil;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.main.util.MainConstants;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.vo.SendCodeRedisVO;
import com.shands.mod.main.vo.SendCodeVO;
import com.shands.mod.main.vo.feign.MessageVO;
import com.shands.mod.message.service.IMessageService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.CommonConstants;
import com.shands.mod.util.JwtUtils;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.ResultCode;
import com.shands.mod.vo.ResultVO;
import com.shands.uc.model.auth.UserLoginRes;
import com.shands.uc.model.req.user.SetPassword;
import com.shands.uc.model.req.v3.PublicLoginReq;
import com.shands.uc.model.req.v3.auth.RoleInfo;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/8/10
 * @desc 用户登录service
*/

@Service
@Slf4j
public class UserLoginServiceImpl implements UserLoginService {

  @Value("${app.token.secret:shands-mod3-secret}")
  private String secret;

  @Value("${app.token.expiration:259200}")
  private long expiration;

  @Resource
  private UserMapper userMapper;

  @Resource
  private ModUserDao modUserDao;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Resource
  private OutUserRoleDao outUserRoleDao;

  @Resource
  private ModDeptDao modDeptDao;
  @Resource
  private ModRoleCheckMapper modRoleCheckMapper;
  private final UcUserService ucUserService;

  private final UcLoginService ucLoginService;

  @Autowired
  private IMessageService messageService;
  @Resource
  private IUserService userService;

  private final HotelInfoCommonService hotelInfoCommonService;

  private final IModUserTokenService modUserTokenService;

  private final RedisTemplate redisTemplate;

  private final FeiShuApiService feiShuApiService;

  private final FeishuContactService feishuContactService;

  private final HsPlanSanitateTaskService hsPlanSanitateTaskService;

  private static final String MOBILE_PREFIX = "+86";

  private static final String DEVICE_NUMBER = "DeviceNumber";

  private static final String REGEX_PASSWORD = "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[!@#$%^&*()_+{}\":;'<>,.?/~`]).{8,18}$";

  @Resource
  private AccountSecurityConfig accountSecurityConfig;

  @Autowired
  private Environment environment;

  public UserLoginServiceImpl(UcUserService ucUserService,
      UcLoginService ucLoginService,
      HotelInfoCommonService hotelInfoCommonService,
      IModUserTokenService modUserTokenService,
      RedisTemplate redisTemplate,
      FeiShuApiService feiShuApiService,
      FeishuContactService feishuContactService,
      HsPlanSanitateTaskService hsPlanSanitateTaskService) {
    this.ucUserService = ucUserService;
    this.ucLoginService = ucLoginService;
    this.hotelInfoCommonService = hotelInfoCommonService;
    this.modUserTokenService = modUserTokenService;
    this.redisTemplate = redisTemplate;
    this.feiShuApiService = feiShuApiService;
    this.feishuContactService = feishuContactService;
    this.hsPlanSanitateTaskService = hsPlanSanitateTaskService;
  }

  @Override
  public ModUserLoginVo loginByMobile(String mobile, String password, HttpServletRequest httpServletRequest) {
    String pw = DigestUtils.md5Hex(password);
    String token = ucUserService.loginByMobileAndPassword(mobile, pw);
    if (StringUtils.isBlank(token)) {
      throw new ServiceException("密码错误");
    }
    return getResultInfo(mobile,httpServletRequest);
  }

  /**
   * APP员工端手机号登录(加密)
   *
   * @param httpServletRequest
   * @return
   */
  @Override
  public ModUserLoginVo loginByMobileEncrypt(String param, HttpServletRequest httpServletRequest)
      throws Exception {
    String decrypt = BaiDaWuRSAUtils.decrypt(param,
        BaiDaWuRSAUtils.getPrivateKey(BaiDaWuRSAUtils.DL_RSA_PRIVATE_KEY));
    ModUserLoginDto dto = JSON.parseObject(decrypt, ModUserLoginDto.class);
    return this.loginByMobile(dto.getMobile(), dto.getPassword(), httpServletRequest);
  }

  @Override
  public ModUserLoginVo loginByCode(String mobile, String code, HttpServletRequest httpServletRequest) {

    String key = Tools.buildKey(BaseConstants.CACHE_LOGIN, code);

    if (Boolean.TRUE.equals(this.redisTemplate.hasKey(key))){
      String smsCode = this.redisTemplate.opsForValue().get(key).toString();
      if (smsCode == null || !smsCode.equals(mobile)) {
        throw new RuntimeException("请确认手机号或验证码是否正确");
      }

    }else {
      throw new RuntimeException("验证码错误");
    }

    return getResultInfo(mobile,httpServletRequest);
  }

  @Override
  public ModUserLoginVo loginByFeiShuMiniAppCode(String appId, String code, HttpServletRequest request) {
    String appAccessToken = feiShuApiService.getAppAccessToken(appId);
    if (StringUtils.isBlank(appAccessToken)) {
      throw new RuntimeException("应用token获取失败");
    }
    MiniAppToken miniAppToken = feiShuApiService.getMiniAppUserAccessToken(appId, code, appAccessToken);
    if (Objects.isNull(miniAppToken)) {
      throw new RuntimeException("用户token获取失败");
    }
    UserInfo userInfo = feiShuApiService.getUserInfo(appId, miniAppToken.getAccessToken());
    String mobile = userInfo.getMobile().replace(MOBILE_PREFIX, StringUtils.EMPTY);

    // 判断登录用户是否是外协人员
    com.lark.oapi.service.contact.v3.model.User contactUser = feishuContactService.getUserInfoByUserId(
        appId, userInfo.getUserId());
    Integer employeeType = contactUser.getEmployeeType();
    if (EmployeeTypeEnum.OUTSOURCING.getValue().equals(employeeType)){
      //外协登陆接口
      //判断用户的所在部门里面哪个酒店给排班了，就登陆哪个
      //根据飞书部门id获取酒店id
      List<String> departmentIds = Arrays.asList(contactUser.getDepartmentIds());
      List<FeiShuDeptRes> feiShuDeptResList = modDeptDao.queryHotelIdByFeiShuDeptId(departmentIds);

      for (FeiShuDeptRes dept: feiShuDeptResList){
        OutSourceFeiShuLoginDto dto = new OutSourceFeiShuLoginDto();
        dto.setHotelId(dept.getHotelId());
        dto.setUserMobile(mobile);
        try {
          return getLoginResultVo(dto,request);
        }catch (ServiceException e){
          log.info("手机号为{}的外协人员在当前酒店={},无法登录，原因={}",mobile,dept.getHotelId(), e.getMessage());
        }
      }
      log.error("手机号为{}的外协人员有{}家酒店，都无法登录",mobile,feiShuDeptResList.size());
      throw new RuntimeException("没有可以登录的酒店，外协人员手机号=" + mobile);
    }else {
      return getResultInfo(mobile, request);
    }
  }

  @Override
  public ModUserLoginVo loginByFeiShuWebAppCode(String appId, String code, HttpServletRequest request) {
    String appAccessToken = feiShuApiService.getAppAccessToken(appId);
    if (StringUtils.isBlank(appAccessToken)) {
      throw new RuntimeException("应用token获取失败");
    }
    UserAccessTokenInfo webAppUserAccessToken = feiShuApiService.getWebAppUserAccessToken(appId, code, appAccessToken);
    if (Objects.isNull(webAppUserAccessToken)) {
      throw new RuntimeException("用户token获取失败");
    }
    String mobile = webAppUserAccessToken.getMobile().replace(MOBILE_PREFIX, StringUtils.EMPTY);
    // 判断登录用户是否是外协人员
    com.lark.oapi.service.contact.v3.model.User contactUser = feishuContactService.getUserInfoByUserId(
        appId, webAppUserAccessToken.getUserId());
    Integer employeeType = contactUser.getEmployeeType();
    if (EmployeeTypeEnum.OUTSOURCING.getValue().equals(employeeType)){
      //外协登陆接口
      //判断用户的所在部门里面哪个酒店给排班了，就登陆哪个
      //根据飞书部门id获取酒店id
      List<String> departmentIds = Arrays.asList(contactUser.getDepartmentIds());
      List<FeiShuDeptRes> feiShuDeptResList = modDeptDao.queryHotelIdByFeiShuDeptId(departmentIds);

      for (FeiShuDeptRes dept: feiShuDeptResList){
        OutSourceFeiShuLoginDto dto = new OutSourceFeiShuLoginDto();
        dto.setHotelId(dept.getHotelId());
        dto.setUserMobile(mobile);
        try {
          return getLoginResultVo(dto,request);
        }catch (ServiceException e){
          log.info("手机号为{}的外协人员在当前酒店={},无法登录，原因={}",mobile,dept.getHotelId(), e.getMessage());
        }
      }
      log.error("手机号为{}的外协人员有{}家酒店，都无法登录",mobile,feiShuDeptResList.size());
      throw new RuntimeException("没有可以登录的酒店，外协人员手机号=" + mobile);
    }else {
      return getResultInfo(mobile, request);
    }
  }

  @Override
  public ModUserLoginVo changeOrgLogin(OutSourceFeiShuLoginDto outSourceFeiShuLoginDto,
      HttpServletRequest request) {

    return getLoginResultVo(outSourceFeiShuLoginDto,request);
  }

  /**
   * 根据用户手机号 获取登录返回对象参数
   * @param mobile
   * @return
   */
  private ModUserLoginVo getResultInfo(String mobile, HttpServletRequest httpServletRequest){

    //返回对象
    ModUserLoginVo result = new ModUserLoginVo();

    //查询用户通宝信息
    UserAuthInfo userAuthInfo = ucUserService.ucLoginByUsername(mobile);

    //同步更新用户最新信息
    ucLoginService.updUcUserInfo(userAuthInfo);

    //判断用户当前是否处于离职状态
    if(!userAuthInfo.isStatus()){
      throw new RuntimeException("该人员处于离职状态");
    }

    //查询用户信息
    ModUser modUser = modUserDao.queryByUcid(userAuthInfo.getUserId());
    if(modUser == null){
      throw new RuntimeException("用户信息为空");
    }
    //查询用户所属机构信息
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryByUcCompanyId(userAuthInfo.getCompanyId());
    if(modHotelInfo == null || modHotelInfo.getHotelStatus() == 0){
      throw new RuntimeException("未查询到您的机构酒店信息");
    }

    int userId = modUser.getId();

    // 创建token
    String token = JwtUtils.generateToken(String.valueOf(userId), this.secret, this.expiration);

    // 获取用户角色信息
    List<RoleInfo> roleInfos = userAuthInfo.getRoles();
    List<String> keys = roleInfos.stream().
        map(RoleInfo :: getCode).collect(Collectors.toList());

    if(keys.contains("Fan_Xiaoer")){
      result.setShandsPlusFlag(true);
    }

    if(keys.contains("admin") || keys.contains("It_maintainer")){
      result.setAdminFlag(true);
    }

    //同步用户角色视图信息
//    syncRoleViews(roleInfos,userId,modHotelInfo.getHotelCode());

    //记录用户token信息
    modUserTokenService.saveTokenInfo(userId,token,expiration,"APP");

    // 更新用户状态信息
    userMapper.updateUserStatusById(UserStatusEnum.ONLINE.getCode(),modUser.getId());

    //更新用户设备信息
    updUserDevice(httpServletRequest,userId);

    //组装返回参数
    result.setId(userId);
    result.setToken(token);
    result.setRoles(keys);
    result.setReceiveOrderMode(modUser.getReceiveOrderMode());
    result.setCompany(modHotelInfo.getHotelId());
    result.setUcCompanyId(userAuthInfo.getCompanyId());
    if("000001".equals(modHotelInfo.getHotelCode()) || "HOTELGO".equals(modHotelInfo.getHotelCode())){
      result.setHotelName("德胧集团");
    }else{
      result.setHotelName(modHotelInfo.getHotelName());
    }
    result.setHotelCode(modHotelInfo.getHotelCode());
    result.setName(modUser.getName());
    result.setMobile(modUser.getMobile());
    result.setUcId(userAuthInfo.getUserId());
    result.setUserStatus(UserStatusEnum.ONLINE.getCode());
    //缓存用户信息
    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    this.redisTemplate.opsForValue().set(key, result, this.expiration, TimeUnit.SECONDS);

    //缓存用户和token(用户剔除其他用户)
    String deviceNumber = httpServletRequest.getHeader("DeviceNumber");
    if(StringUtils.isNotBlank(deviceNumber) && !accountSecurityConfig.getTokenWhiteList().contains(modUser.getMobile())) {
      String loginUserKey = Tools.buildKey(BaseConstants.CACHE_LOGIN_USER, result.getMobile());
      if( this.redisTemplate.hasKey(loginUserKey)) {
        String oldToken =  (String)this.redisTemplate.opsForValue().get(loginUserKey);
        if(StringUtils.isNotBlank(oldToken)) {
          String oldTokenKey = Tools.buildKey(BaseConstants.CACHE_USER, oldToken);
          this.redisTemplate.delete(oldTokenKey);
        }
      }
      this.redisTemplate.opsForValue().set(loginUserKey, token, this.expiration, TimeUnit.SECONDS);
    }


    //清空用户权限缓存信息
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_UC_INFO, String.valueOf(userId)));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_UC_ROLE, String.valueOf(userId)));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_MOD_INFO, String.valueOf(userId)));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_MOD_RIGHT, String.valueOf(userId)));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_MOD_SWITCH, String.valueOf(userId)));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_MOD_DATABORD, String.valueOf(userId)));

    return result;
  }

  /**
   * 外协人员登录
   * @param dto
   * @param httpServletRequest
   * @return
   */
  private ModUserLoginVo getLoginResultVo(OutSourceFeiShuLoginDto dto, HttpServletRequest httpServletRequest){

    ModUserLoginVo result = new ModUserLoginVo();
    //查询酒店信息
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryById(dto.getHotelId());

    //查询人员信息
    ModUser modUser = modUserDao.queryByUserNameAndUcCompanyId(dto.getUserMobile(),modHotelInfo.getUcCompanyId());
    if(modUser == null){
      throw new ServiceException("用户信息为空");
    }
    //查询当前酒店是否给登录人员进行排班
    boolean taskFlag = hsPlanSanitateTaskService.userForTask(modUser.getId(),dto.getHotelId());
    if(!taskFlag){
      throw new ServiceException("当前酒店未给您排班，无法登录！");
    }

    //计算秒
    int expiration = this.getLastSeconds();

    // 创建token
    String token = JwtUtils.generateToken(modUser.getId().toString(), this.secret, expiration);

    // 获取用户角色信息
    List<String> roles = outUserRoleDao.getRolesForLogin(dto.getHotelId(),modUser.getId());

//    List<String> queryAll = modRoleCheckMapper.queryAll();
//
//    queryAll.retainAll(roles);
//
//    if(roles != null && !queryAll.isEmpty() && queryAll.size()>0){
//      boolean taskFlag = taskService.userForTask(modUser.getId());
//
//      if(!taskFlag){
//        throw new ServiceException("当前酒店未给您排班，无法登录！");
//      }
//    }

    //组装返回参数
    result.setId(modUser.getId());
    result.setToken(token);
    result.setRoles(roles);
    result.setReceiveOrderMode(modUser.getReceiveOrderMode());
    result.setCompany(modHotelInfo.getHotelId());
    result.setUcCompanyId(modHotelInfo.getUcCompanyId());
    result.setHotelName(modHotelInfo.getHotelName());
    result.setHotelCode(modHotelInfo.getHotelCode());
    result.setName(modUser.getName());
    result.setMobile(modUser.getUsername());
    result.setUcId(modUser.getUcId());
    result.setPlatform("OUT");

    //缓存用户数据到redis
    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    this.redisTemplate.opsForValue().set(key, result, expiration, TimeUnit.SECONDS);

    //更新用户设备信息
    String device = Tools.getHeader(httpServletRequest, BaseConstants.HEADER_DEVICE);
    String deviceType = Tools.getHeader(httpServletRequest, BaseConstants.HEADER_APP);
    if (StringUtils.isNotBlank(device) && StringUtils.isNotBlank(deviceType)) {

      User bean = new User();

      //先把此设备登陆的账号deviceId全部清空
      userMapper.updateDeviceNullByDeviceId(device);

      bean.setDeviceId(device);
      bean.setUpdateUser(modUser.getId());
      bean.setUpdateTime(new Date());
      bean.setDeviceType(deviceType);
      bean.setId(modUser.getId());
      userMapper.updateByPrimaryKeySelective(bean);
    }

    try{
      modUserTokenService.saveTokenInfo(modUser.getId(),token,expiration,"OUT");
    }catch (Exception e){
      log.info("用户token信息记录失败：userId:{},token:{}",modUser.getId(), token);
    }

    return result;
  }

//  /**
//   * 解析用户角色视图信息
//   * @param ucRoleInfos
//   */
//  private void syncRoleViews(List<RoleInfo> ucRoleInfos,int userId,String userHotelCode){
//    List<String> hotels = new ArrayList<>();
//    List<String> datas = new ArrayList<>();
//    if(ucRoleInfos != null && !ucRoleInfos.isEmpty()){
//
//      try{
//
//        ucRoleInfos.forEach(x -> {
//
//          if(org.springframework.util.StringUtils.hasText(x.getViewCode())) {
//
//            //解析用户酒店切换视图
//            if (x.getViewCode().contains("hotel_switch")) {
//
//              JSONArray jsonArray = JSON.parseArray(x.getViewValue());
//              List<UcViewListVo> viewListVos = jsonArray.toJavaList(UcViewListVo.class);
//              viewListVos.forEach(y -> {
//
//                if (y.getType().equals("companyCode")) {
//                  hotels.add(y.getValue());
//                }
//
//                if (y.getType().equals("brandPepId")) {
//                  HotelIinfoFindDto brandDto = new HotelIinfoFindDto();
//                  brandDto.setHotelBrandCode(y.getValue());
//                  List<ModHotelInfo> brandHotelInfos = hotelInfoCommonService
//                      .findHotelInfos(brandDto);
//
//                  if (brandHotelInfos != null && !brandHotelInfos.isEmpty()) {
//                    brandHotelInfos.forEach(e -> {
//                      hotels.add(e.getHotelCode());
//                    });
//                  }
//                }
//
//                if (y.getType().equals("ownership")) {
//                  HotelIinfoFindDto brandDto = new HotelIinfoFindDto();
//                  brandDto.setHotelOwnershipCode("FUNMANAGERENUM&" + y.getValue());
//                  List<ModHotelInfo> brandHotelInfos = hotelInfoCommonService
//                      .findHotelInfos(brandDto);
//
//                  if (brandHotelInfos != null && !brandHotelInfos.isEmpty()) {
//                    brandHotelInfos.forEach(e -> {
//                      hotels.add(e.getHotelCode());
//                    });
//                  }
//                }
//              });
//            }
//
//            //解析用户数据权限视图
//            if (x.getViewCode().contains("hotel_data")) {
//              JSONArray jsonArray = JSON.parseArray(x.getViewValue());
//              List<UcViewListVo> viewListVos = jsonArray.toJavaList(UcViewListVo.class);
//              viewListVos.forEach(y -> {
//
//                if (y.getType().equals("companyCode")) {
//                  datas.add(y.getValue());
//                }
//
//                //品牌
//                if (y.getType().equals("brandPepId")) {
//                  List<String> brandHotelInfos = modHotelInfoDao.findHotelCodes("brandCode",y.getValue());
//                  if (brandHotelInfos != null && !brandHotelInfos.isEmpty()) {
//                    datas.addAll(brandHotelInfos);
//                  }
//                }
//
//                //事业部
//                if (y.getType().equals("ownership")) {
//                  List<String> brandHotelInfos = modHotelInfoDao.findHotelCodes("departmentCode","FUNMANAGERENUM&" + y.getValue());
//                  if (brandHotelInfos != null && !brandHotelInfos.isEmpty()) {
//                    datas.addAll(brandHotelInfos);
//                  }
//                }
//              });
//            }
//          }
//        });
//      }catch (Exception e){
//        e.printStackTrace();
//        log.info("用户通宝视图信息" +JSONObject.toJSONString(ucRoleInfos));
//      }
//    }
//
//    //缓存用户数据到redis
//    hotels.add(userHotelCode);
//    String hotelKey = Tools.buildKey(BaseConstants.CACHE_USER_HOTELS, String.valueOf(userId));
//    this.redisTemplate.opsForValue().set(hotelKey, hotels);
//
//    String dataKey = Tools.buildKey(BaseConstants.CACHE_USER_DATAS, String.valueOf(userId));
//    this.redisTemplate.opsForValue().set(dataKey, datas);
//  }

  /**
   * 同步更新用户设备信息
   * @param httpServletRequest
   * @param userId
   */
  private void updUserDevice(HttpServletRequest httpServletRequest,int userId){
    String device = Tools.getHeader(httpServletRequest, BaseConstants.HEADER_DEVICE);
    String deviceType = Tools.getHeader(httpServletRequest, BaseConstants.HEADER_APP);
    if (StringUtils.isNotBlank(device) && StringUtils.isNotBlank(deviceType)) {

      User bean = new User();

      //先把此设备登陆的账号deviceId全部清空
      userMapper.updateDeviceNullByDeviceId(device);

      bean.setDeviceId(device);
      bean.setUpdateUser(userId);
      bean.setUpdateTime(new Date());
      bean.setDeviceType(deviceType);
      bean.setId(userId);
      userMapper.updateByPrimaryKeySelective(bean);
    }
  }

  @Override
  public Boolean tokenValid(String token) {
    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    Object cacheToken = redisTemplate.opsForValue().get(key);
    return Objects.nonNull(cacheToken);
  }

  @Override
  public Boolean newDeviceValid(String mobile, HttpServletRequest httpServletRequest) {
    //查询用户信息
    ModUser modUser = modUserDao.queryByUserName(mobile);
    if(modUser == null){
      return false;
    }
    String newDeviceId = httpServletRequest.getHeader(DEVICE_NUMBER);
    return ObjectUtils.equals(modUser.getDeviceNumber(),newDeviceId);
  }

  @Override
  public ModUserLoginVo getModUserLoginVo(String mobile, HttpServletRequest httpServletRequest,boolean changeDevice) {
      return getResultInfo(mobile,httpServletRequest);
  }

  @Override
  public void validateUser(String mobile) {
    //查询用户通宝信息
    UserAuthInfo userAuthInfo = ucUserService.ucLoginByUsername(mobile);
    //判断用户当前是否处于离职状态
    if(!userAuthInfo.isStatus()){
      throw new RuntimeException("该人员处于离职状态");
    }
  }

  @Override
  public int sendCode(SendCodeVO sendCodeVO) {
    ValidateCodeEnum validateCodeEnum = ValidateCodeEnum.getByType(sendCodeVO.getCodeType());
    if(null == validateCodeEnum) {
      throw new ServiceException("参数错误");
    }
    String mobile = sendCodeVO.getMobile();
    if(StringUtils.isNotBlank(sendCodeVO.getId())) {
      ModUser modUser = modUserDao.queryById(Integer.parseInt(sendCodeVO.getId()));
      if(null == modUser) {
        throw new ServiceException("用户信息为空");
      }
      mobile = modUser.getMobile();
    }
    //查询是否已经发送过验证码
    String key = Tools.buildKey(validateCodeEnum.getRedisKeyPre(), mobile);

    if(this.redisTemplate.hasKey(key)) {
      SendCodeRedisVO sendCodeRedisVO = (SendCodeRedisVO)this.redisTemplate.opsForValue().get(key);
      if(null != sendCodeRedisVO && null != sendCodeRedisVO.getExpireTime() && sendCodeRedisVO.getExpireTime().getTime() > System.currentTimeMillis()) {
        return (int)(sendCodeRedisVO.getExpireTime().getTime() - System.currentTimeMillis()) / 1000;
      }
    }
    String code = "123456";
    //发送验证码
    if(!ArrayUtils.contains(BaseConstants.ENV_TEST_LIST, environment.getActiveProfiles()[0])) {
      code = RandomStringUtils.randomNumeric(6);
      MessageVO messageVO = new MessageVO();
      messageVO.setMessageType(BaseConstants.MESSAGE_TYPE_LOGIN_CODE);
      messageVO.setReceivers(mobile);
      messageVO.setReceiverType(BaseConstants.RECEIVE_TYPE_CUSTOMER);
      messageVO.setContent(String.format(validateCodeEnum.getContent(), code));
      messageVO.setCompanyId(BaseConstants.PLATFORM_COMPANY);
      messageVO.setGroupId(BaseConstants.PLATFORM_COMPANY);
      messageVO.setCreateUser(BaseConstants.SYSTEM_USER);
      int amount = this.messageService.sendSms(messageVO.getMessageType(), messageVO.getMessageSubtype(), messageVO.getReceivers(), messageVO.getContent(), 0, messageVO.getReceiverType(), messageVO.getCompanyId(), messageVO.getGroupId(), messageVO.getCreateUser());
      if(amount <= 0) {
        throw new ServiceException("验证码发送失败");
      }
    }
    SendCodeRedisVO sendCodeRedisVO = new SendCodeRedisVO();
    sendCodeRedisVO.setCode(code);
    sendCodeRedisVO.setExpireTime(DateUtil.addSecond(new Date(),validateCodeEnum.getExpireSeconds()));
    // 短信验证码10分钟有效
    this.redisTemplate.opsForValue().set(key, sendCodeRedisVO, 600, TimeUnit.SECONDS);

    return validateCodeEnum.getExpireSeconds();

  }

  @Override
  public ModUserLoginVo securityVerification(ModUserSecurityVerificationDto dto, HttpServletRequest httpServletRequest) {
    ModUser modUser = modUserDao.queryById(dto.getId());
    if(dto.getSecurityVerificationType() == 1) {
      //密码验证
      //先校验登录二维码
      String key = Tools.buildKey(ValidateCodeEnum.LOGIN.getRedisKeyPre(), modUser.getMobile());
      if(!this.redisTemplate.hasKey(key)) {
        throw new ServiceException("非法请求");
      }
      SendCodeRedisVO sendCodeRedisVO = (SendCodeRedisVO)this.redisTemplate.opsForValue().get(key);
      if(null == sendCodeRedisVO || !sendCodeRedisVO.getCode().equals(dto.getCode())) {
        throw new ServiceException("非法请求");
      }
      //校验密码是否正确
      String username = modUser.getUsername();
      String token = ucUserService.loginByMobileAndPassword(username, DigestUtils.md5Hex(dto.getPassword()));
      if (StringUtils.isBlank(token)) {
        throw new ServiceException("密码错误");
      }
    } else if(dto.getSecurityVerificationType() == 2) {
      //验证码
      //先校验密码是否正确
      String username = modUser.getUsername();
      String token = ucUserService.loginByMobileAndPassword(username, DigestUtils.md5Hex(dto.getPassword()));
      if (StringUtils.isBlank(token)) {
        throw new ServiceException("非法请求");
      }
      //校验验证码
      String key = Tools.buildKey(ValidateCodeEnum.SECURITY_VERIFICATION.getRedisKeyPre(), modUser.getMobile());
      if(!this.redisTemplate.hasKey(key)) {
        throw new ServiceException("验证码错误");
      }
      SendCodeRedisVO sendCodeRedisVO = (SendCodeRedisVO)this.redisTemplate.opsForValue().get(key);
      if(null == sendCodeRedisVO || !sendCodeRedisVO.getCode().equals(dto.getCode())) {
        throw new ServiceException("验证码错误");
      }
    }
    //替换新的设备id
    modUser.setDeviceNumber(httpServletRequest.getHeader(DEVICE_NUMBER));
    modUserDao.update(modUser);
    return getResultInfo(modUser.getUsername(), httpServletRequest);
  }


  @Override
  public ModUserLoginVo initPassword(ModUserSecurityVerificationDto dto, HttpServletRequest httpServletRequest) {
    //先校验登录二维码
    ModUser modUser = modUserDao.queryById(dto.getId());
    String key = Tools.buildKey(ValidateCodeEnum.LOGIN.getRedisKeyPre(), modUser.getMobile());
    if(!this.redisTemplate.hasKey(key)) {
      throw new ServiceException("非法请求");
    }
    SendCodeRedisVO sendCodeRedisVO = (SendCodeRedisVO)this.redisTemplate.opsForValue().get(key);
    if(null == sendCodeRedisVO || !sendCodeRedisVO.getCode().equals(dto.getCode())) {
      throw new ServiceException("非法请求");
    }
    //判断密码复杂性
    if (!isMatch(dto.getPassword())) {
      throw new ServiceException("密码必须为8-18大小写字母+数字+特殊符号组合");
    }

    //同步修改通宝密码
    ResultVO resultVO = userService.updateUcPassword(modUser.getUcId(), dto.getPassword());
    if(!resultVO.getCode().equals(ResultCode.SUCCESS.getCode())) {
      throw new ServiceException(resultVO.getMessage());
    }
    //修改密码
    modUser.setPassword(DigestUtils.md5Hex(dto.getPassword()));
    //替换新的设备id
    modUser.setDeviceNumber(httpServletRequest.getHeader(DEVICE_NUMBER));
    modUserDao.update(modUser);
    return getResultInfo(modUser.getUsername(), httpServletRequest);
  }

  /**
   * 获取当前时间距离今天结束还有多少秒
   * @return
   */
  private int getLastSeconds(){

    Calendar calendar = Calendar.getInstance();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    String last = sdf.format(new Date()) + " 23:59:59";

    SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    try {
      Date latDate = sdf1.parse(last);
      return (int)(latDate.getTime() - System.currentTimeMillis()) / 1000;
    } catch (ParseException e) {
      log.error(e.getMessage(), e);
    }

    return 0;
  }

  private boolean isMatch(String value) {
    boolean result = false;
    if (StringUtils.isNotBlank(value)) {
      Pattern pattern = Pattern.compile(REGEX_PASSWORD);
      Matcher matcher = pattern.matcher(value);

      result = matcher.find();
    }
    return result;
  }





}
