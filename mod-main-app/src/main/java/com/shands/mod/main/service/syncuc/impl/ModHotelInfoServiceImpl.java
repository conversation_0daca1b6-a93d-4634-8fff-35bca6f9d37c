package com.shands.mod.main.service.syncuc.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.mapper.mp.MpHotelGradeLogDao;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.mp.bo.HotelGradeBo;
import com.shands.mod.dao.model.mp.bo.HotelGradeListBo;
import com.shands.mod.dao.model.mp.bo.HotelGradeLogListBo;
import com.shands.mod.dao.model.mp.po.MpHotelGradeLog;
import com.shands.mod.dao.model.mp.vo.HotelGradeListVo;
import com.shands.mod.dao.model.mp.vo.HotelGradeLogVo;
import com.shands.mod.dao.model.req.hotel.GetAllOpenHotelReq;
import com.shands.mod.dao.model.res.hotel.OpenHotelRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.main.service.syncuc.IModHotelInfoService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.PageVO;
import com.shands.mod.vo.ResultVO;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Slf4j
@Service
public class ModHotelInfoServiceImpl implements IModHotelInfoService {

  @Resource
  private ModHotelInfoDao modHotelInfoDao;
  @Autowired
  private MpHotelGradeLogDao mpHotelGradeLogDao;
  @Autowired
  private ModUserDao modUserDao;

  @Override
  public ModHotelInfo queryById(Integer hotelId) {
    return modHotelInfoDao.queryById(hotelId);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void editInfo(ModHotelInfo req) {
    req.setUpdateTime(new Date());
    if (req.getSiteStatus() == 1) {
      Assert.isTrue(req.getLngGd() != null, "经度不能为空");
      Assert.isTrue(req.getLatGd() != null, "维度不能为空");
      Assert.isTrue(req.getRadius() != null, "范围不能为空");
    }
    modHotelInfoDao.update(req);
  }

  @Override
  public ResultVO companyList(ModHotelInfo modHotelInfo,PageVO pageVO) {
    PageVO pageInfo = com.shands.mod.util.Tools.getPageInfo(pageVO);
    PageHelper.startPage(pageInfo.getPageNo(), pageInfo.getPageSize());
    List<ModHotelInfo> allCompany = modHotelInfoDao.companyList(modHotelInfo);
    PageInfo companyPageInfo = new PageInfo<>(allCompany);
    return ResultVO.success(companyPageInfo);
  }

  @Override
  public int update(ModHotelInfo req) {
    req.setUpdateTime(new Date());
    return modHotelInfoDao.update(req);
  }

  @Override
  public PageInfo<HotelGradeListVo> hotelGradeList(HotelGradeListBo hotelGradeListBo) {
    PageHelper.startPage(hotelGradeListBo.getPageNo(), hotelGradeListBo.getPageSize());
    List<HotelGradeListVo> hotelGradeListVos = modHotelInfoDao.selectHotelGradeList(
        hotelGradeListBo);
    return new PageInfo<>(hotelGradeListVos);
  }

  @Override
  public boolean setHotelGrade(HotelGradeBo hotelGradeBo) {
    ModHotelInfo modHotelInfo = new ModHotelInfo();
    modHotelInfo.setHotelId(hotelGradeBo.getHotelId());
    modHotelInfo.setGrade(hotelGradeBo.getGrade());
    ModHotelInfo select = modHotelInfoDao.queryById(hotelGradeBo.getHotelId());
    int update = modHotelInfoDao.update(modHotelInfo);
    ModUser modUser = modUserDao.queryById(ThreadLocalHelper.getUser().getId());
    MpHotelGradeLog mpHotelGradeLog = MpHotelGradeLog.builder()
        .hotelCode(select.getHotelCode())
        .grade(hotelGradeBo.getGrade())
        .remark(hotelGradeBo.getRemark())
        .picture(hotelGradeBo.getPicture())
        .userName(hotelGradeBo.getUserName())
        .userId(hotelGradeBo.getUserId())
        .star(hotelGradeBo.getStar())
        .createUserMobile(modUser.getMobile())
        .createUserName(modUser.getName())
        .createTime(new Date())
        .createUser(ThreadLocalHelper.getUser().getId()).build();
    mpHotelGradeLogDao.insert(mpHotelGradeLog);
    return true;
  }

  @Override
  public PageInfo<HotelGradeLogVo> hotelGradeLog(HotelGradeLogListBo hotelGradeLogListBo) {
    PageHelper.startPage(hotelGradeLogListBo.getPageNo(), hotelGradeLogListBo.getPageSize());
    MpHotelGradeLog mpHotelGradeLog = MpHotelGradeLog.builder()
        .hotelCode(hotelGradeLogListBo.getHotelCode()).build();
    List<HotelGradeLogVo> hotelGradeLogVos = mpHotelGradeLogDao.queryAllByVo(mpHotelGradeLog);
    return new PageInfo<>(hotelGradeLogVos);
  }

  @Override
  public PageInfo<OpenHotelRes> getAllOpenHotel(GetAllOpenHotelReq req) {
    PageHelper.startPage(req.getPageNo(), req.getPageSize());
    List<OpenHotelRes> hotelList = modHotelInfoDao.getAllOpenHotel(req.getHotelName());
    return new PageInfo<>(hotelList);
  }
}
