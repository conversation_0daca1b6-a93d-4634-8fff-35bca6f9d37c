package com.shands.mod.main.controller.ocr;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.model.ocr.bo.IDCardBo;
import com.shands.mod.dao.model.ocr.vo.IDCardVo;
import com.shands.mod.main.service.ocr.IdCardOCRService;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/idCardVerify")
@Api(value = "身份验证控制层",tags = "身份验证控制层")
@Slf4j
public class IdCardOCRController {

  private final IdCardOCRService idCardOCRService;

  @Autowired
  public IdCardOCRController(IdCardOCRService idCardOCRService){
   this.idCardOCRService=idCardOCRService;
  }

  @PostMapping("/getVerifyByUc")
  @ApiOperation(value = "通过uc接口判断用户是否已经验证")
  @ApiResponse(code = 0, message = "成功", response = IDCardVo.class)
  public ResultVO getVerifyByUc(){
    return ResultVO.success(idCardOCRService.getVerifyByUc());
  }


  @PostMapping("/idCardAuthentication")
  @ApiOperation(value = "身份验证")
  public ResultVO idCardAuthentication(@Valid @RequestBody IDCardBo idCardBo){
    return ResultVO.success(idCardOCRService.idCardAuthentication(idCardBo));
  }
}
