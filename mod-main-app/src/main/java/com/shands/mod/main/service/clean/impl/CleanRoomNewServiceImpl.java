package com.shands.mod.main.service.clean.impl;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.hs.HotelRoomTypeMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckItemMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckLinenMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomCheckUserMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomLinenConfigMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomTaskMapper;
import com.shands.mod.dao.model.enums.CleanStatusEnum;
import com.shands.mod.dao.model.hs.cleanRoom.HsCleanRoomTask;
import com.shands.mod.dao.model.req.clean.UpdateCheckUserReq;
import com.shands.mod.dao.model.res.clean.AllotCheckUserDetailRes;
import com.shands.mod.dao.model.res.clean.CheckByTaskRes;
import com.shands.mod.dao.model.res.clean.CheckItemRes;
import com.shands.mod.dao.model.res.clean.CheckUserAllocatedRes;
import com.shands.mod.dao.model.res.clean.CleanRoomTaskDetailRes;
import com.shands.mod.dao.model.res.clean.LinenListRes;
import com.shands.mod.dao.model.v0701.dto.RoomStatusDto;
import com.shands.mod.dao.model.v0701.vo.GuestInfoVo;
import com.shands.mod.dao.model.v0701.vo.RoomInfoDetailVo;
import com.shands.mod.dao.model.v0701.vo.RoomInfoVo;
import com.shands.mod.main.service.clean.ICleanRoomNewService;
import com.shands.mod.main.service.crs.ICrsRoomStatusService;
import com.shands.mod.main.util.hs.DateUtils;
import com.shands.mod.vo.ResultVO;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CleanRoomNewServiceImpl implements ICleanRoomNewService {

  @Resource
  HsCleanRoomCheckUserMapper checkUserMapper;

  @Resource
  ICrsRoomStatusService crsRoomStatusService;

  @Resource
  HsCleanRoomTaskMapper cleanRoomTaskMapper;

  @Resource
  HsCleanRoomCheckMapper cleanRoomCheckMapper;

  @Resource
  HsCleanRoomCheckItemMapper roomCheckItemMapper;

  @Resource
  HsCleanRoomCheckLinenMapper linenMapper;

  @Resource
  HsCleanRoomLinenConfigMapper linenConfigMapper;

  @Resource
  HotelRoomTypeMapper hotelRoomTypeMapper;

  @Override
  public List<AllotCheckUserDetailRes> checkUserAllocatedDetail(Integer companyId,
      Integer checkUser) {
    List<CheckUserAllocatedRes> checkUserAllocatedDetail = checkUserMapper
        .checkUserAllocatedDetail(companyId, checkUser);
    List<AllotCheckUserDetailRes> res = new ArrayList<>();
    Map<String, List<CheckUserAllocatedRes>> buildMap = checkUserAllocatedDetail.stream().collect(
        Collectors.groupingBy(CheckUserAllocatedRes::getBuildingName));
    Set<String> building = buildMap.keySet();
    List<AllotCheckUserDetailRes> finalRes = res;
    building.forEach(buildKey -> {
      List<CheckUserAllocatedRes> buildRes = buildMap.get(buildKey);
      Map<String, List<CheckUserAllocatedRes>> floorMap = buildRes.stream().collect(
          Collectors.groupingBy(CheckUserAllocatedRes::getFloorName));
      Set<String> floor = floorMap.keySet();
      floor.forEach(floorKey -> {
        AllotCheckUserDetailRes newDetail = new AllotCheckUserDetailRes();
        List<CheckUserAllocatedRes> floorRes = floorMap.get(floorKey);
        newDetail.setFloorAndBuild(floorKey + "/" + buildRes.get(0).getBuildingName());
        newDetail.setRoomCount(floorRes.size());
        String room = floorRes.stream().map(CheckUserAllocatedRes::getRoomNo)
            .collect(Collectors.joining(","));
        newDetail.setRoomNo(room);
        newDetail.setSort(floorRes.get(0).getSort());
        finalRes.add(newDetail);
      });
    });
    res = res.stream().sorted(Comparator
        .comparing(AllotCheckUserDetailRes::getSort, Comparator.nullsLast(Integer::compareTo)))
        .collect(
            Collectors.toList());
    return res;
  }

  @Override
  public List<CheckUserAllocatedRes> checkUserAllocated(Integer companyId) {
    List<CheckUserAllocatedRes> checkUserAllocated = checkUserMapper.checkUserAllocated(companyId);
    return checkUserAllocated;
  }

  @Override
  public ResultVO updateCheckUserForTask(
      UpdateCheckUserReq req) {
    synchronized (this) {
      HsCleanRoomTask task = cleanRoomTaskMapper.selectByPrimaryKey(req.getId());
      if (task == null || !req.getCompanyId().equals(task.getCompanyId())) {
        return ResultVO.failed("任务为空，请确定后修改");
      }
      if (!req.getRoomNo().equals(task.getRoomNo())) {
        return ResultVO.failed("房间号与任务中不一致,请刷新后重试");
      }
      if (req.getCheckUser().equals(task.getCheckUser())) {
        return ResultVO.failed("当前任务的检查人是您哦");
      }
      if (task.getCleanStatus() != 0 && task.getCleanStatus() != 2 && task.getCleanStatus() != 3) {
        return ResultVO.failed("当前任务不能修改检查人");
      }
      if (task.getPass() == 1) {
        return ResultVO.failed("当前任务是第二次检查，无法修改");
      }
      int a = cleanRoomTaskMapper.updateCheckUserForTask(req);
      if (a > 0) {
        return ResultVO.success();
      } else {
        return ResultVO.failed("修改失败，请重试");
      }
    }
  }

  @Override
  public ResultVO taskDetail(Integer hotelId, Integer id) throws ParseException {
    //任务详情
    CleanRoomTaskDetailRes taskDetail = cleanRoomTaskMapper.taskDetail(hotelId, id);
    if (taskDetail == null) {
      return ResultVO.failed("暂未找到该任务详情");
    }
    //得到不通过项
    List<CheckByTaskRes> noPassCheck = cleanRoomCheckMapper.checkByTask(taskDetail.getId());
    noPassCheck = noPassCheck.stream().filter(t-> t.getEndCheck() !=null).collect(Collectors.toList());
    Iterator<CheckByTaskRes> iterator = noPassCheck.iterator();
    while (iterator.hasNext()) {
      CheckByTaskRes res = iterator.next();
        List<CheckItemRes> noPassItem = roomCheckItemMapper.checkItem(res.getCheckId(), 0);
        res.setItemRes(noPassItem);
    }
    //布草项目
    List<LinenListRes> linenListRes = new ArrayList<>();
    Integer checkId = cleanRoomCheckMapper.getId(id);
    if (checkId != null) {
      linenListRes = linenMapper.linenItemList(checkId, hotelId);
    } else {
      linenListRes = linenConfigMapper.allLinen(hotelId, taskDetail.getRoomCode());
    }
    taskDetail.setLinenListRes(linenListRes);
    taskDetail.setNoPass(noPassCheck);
    //查找房态 房态实时，宾客信息也要实时
    RoomStatusDto dto = new RoomStatusDto();
    dto.setCompanyId(hotelId);
    dto.setRmno(taskDetail.getRoomNo());
    //得到房态
    log.info("任务详情调用房态信息请求参数:{}", JSONObject.toJSONString(dto));
    RoomInfoDetailVo vo = crsRoomStatusService.getRoomStaByRmno(dto);
    if (vo != null) {
      taskDetail.setRoomSta(vo.getSta());
      //如果是待清扫的时候，返回实时宾客信息和预离状态
      if (CleanStatusEnum.WAITING_CLEAN.getCode().equals(taskDetail.getCleanStatus())) {

        List<GuestInfoVo> masterBaseRes = vo.getMasterBaseRes();

        if (masterBaseRes != null && !masterBaseRes.isEmpty()) {

          GuestInfoVo guestInfoVo = masterBaseRes.get(0);
          taskDetail.setArrivalTime(
              org.springframework.util.StringUtils.hasText(guestInfoVo.getArr()) ? DateUtils
                  .strConverDate(guestInfoVo.getArr()) : null);
          taskDetail.setLeaveTime(
              org.springframework.util.StringUtils.hasText(guestInfoVo.getDep()) ? DateUtils
                  .strConverDate(guestInfoVo.getDep()) : null);

          StringBuilder name = new StringBuilder();
          for (GuestInfoVo guest : masterBaseRes) {
            name.append(guest.getName()).append("/");
          }

          taskDetail.setGuestName(name.substring(0, name.length() - 1));
        }
        taskDetail.setIsDep(vo.getIsDep());
        String roomType = hotelRoomTypeMapper
            .findRoomNameByRoomTypeByRoomTypeCode(vo.getRmtype(), hotelId);
        taskDetail.setRoomType(roomType);
      }
    }
    return ResultVO.success(taskDetail);
  }

  @Override
  public ResultVO cleanDetail(Integer hotelId, Integer id) {
    //清扫检查详情
    List<CheckByTaskRes> checkByTaskRes = cleanRoomCheckMapper.checkByTask(id);
    checkByTaskRes.forEach(x -> {
      List<CheckItemRes> checkItem = roomCheckItemMapper.checkItem(x.getCheckId(), null);
      x.setItemRes(checkItem);
      if (StringUtils.isNotEmpty(x.getTotalTime())) {
        x.setTotalTime(DateUtils.secToString(Integer.valueOf(x.getTotalTime())));
      }
      List<LinenListRes> linenRes = linenMapper.linenItemList(x.getCheckId(), hotelId);
      x.setLinenRes(linenRes);
    });
    return ResultVO.success(checkByTaskRes);
  }


}















