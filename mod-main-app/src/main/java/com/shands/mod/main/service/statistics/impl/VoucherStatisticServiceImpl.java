package com.shands.mod.main.service.statistics.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.delonix.bi.dao.mapper.AdsPowerDlEcoAppEmployeePerfDMapper;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo;
import com.shands.mod.dao.model.enums.DeptDataEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.req.statChart.DeptDataReq;
import com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO;
import com.shands.mod.dao.util.ThousandSeparatorUtil;
import com.shands.mod.main.service.statistics.VoucherStatisticService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
@Slf4j
public class VoucherStatisticServiceImpl implements VoucherStatisticService {


    @Autowired
    private AdsPowerDlEcoAppEmployeePerfDMapper adsPowerDlEcoAppEmployeePerfDMapper;

    @Autowired
    private ModHotelInfoDao hotelInfoDao;

    @Autowired
    private ModUserCommonService modUserCommonService;

    @Autowired
    private ModNewDataBoardMapper modNewDataBoardMapper;

    @Override
    public List<DeptDataVo> queryVoucherDeptData(DeptDataReq deptDataReq) {
        List<DeptDataVo> res = Lists.newArrayList();

        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);

        boolean permission = moduleMenuVos.stream()
                .anyMatch(v -> DataBoardModuleEnum.HOTEL_VOUCHER_DATA.name().equals(v.getModuleCode()) );
        if (!permission) {
            return res;
        }


        Date stDate = DateUtil.parse(deptDataReq.getStartTime(), BaseConstants.FORMAT_DATE2);
        Date edDate = DateUtil.parse(deptDataReq.getEndTime(), BaseConstants.FORMAT_DATE2);
        String type = deptDataReq.getType();

        List<DeptDataVo> deptData = Lists.newArrayList();

        // 处理各种券类型
        if (isVoucherType(type)) {
            // 从新的数据源获取券数据
            List<AdsHotelPerformanceHDTO> voucherData = adsPowerDlEcoAppEmployeePerfDMapper.queryVoucherDataByEmployee(
                    hotelCode, stDate, edDate, type);
            
            // 过滤掉发放数量和核销数量都为0的数据
            // 转换为DeptDataVo格式
            deptData = voucherData.stream()
                    .filter(data -> {
                        long sendCount = data.getVoucherSendCount() != null ? data.getVoucherSendCount() : 0L;
                        long useCount = data.getVoucherUseCount() != null ? data.getVoucherUseCount() : 0L;
                        return sendCount > 0 || useCount > 0;
                    })
                    .map(data -> {
                        DeptDataVo dataVo = new DeptDataVo();
                        dataVo.setDeptName(data.getEmployeeDept());
                        dataVo.setKey(data.getEmployeeName());
                        // 第一个值为发放数量，第二个值为核销数量
                        long sendCount = data.getVoucherSendCount() != null ? data.getVoucherSendCount() : 0L;
                        long useCount = data.getVoucherUseCount() != null ? data.getVoucherUseCount() : 0L;
                        dataVo.setFirstValueNum(BigDecimal.valueOf(sendCount));
                        dataVo.setSecondValueNum(BigDecimal.valueOf(useCount));
                        dataVo.setFirstValue(ThousandSeparatorUtil.format(dataVo.getFirstValueNum()));
                        dataVo.setSecondValue(ThousandSeparatorUtil.format(dataVo.getSecondValueNum()));
                        return dataVo;
                    }).collect(Collectors.toList());
        }

        // 过滤掉部门名称为空的数据
        deptData = deptData.stream()
                .filter(deptDataVo ->  StrUtil.isNotEmpty(deptDataVo.getDeptName()))
                .collect(Collectors.toList());

        // 按部门分组
        Map<String, List<DeptDataVo>> deptNameMap = deptData.stream()
                .collect(groupingBy(DeptDataVo::getDeptName));

        for (String deptName : deptNameMap.keySet()) {
            // 部门内排序
            List<DeptDataVo> deptDataVos = deptNameMap.get(deptName).stream()
                    .sorted(Comparator.comparing(DeptDataVo::getFirstValueNum, Comparator.reverseOrder())
                            .thenComparing(DeptDataVo::getSecondValueNum, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            
            DeptDataVo vo = new DeptDataVo();
            vo.setKey(deptName);
            BigDecimal firstValueNum = deptDataVos.stream()
                    .map(DeptDataVo::getFirstValueNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal secondValueNum = deptDataVos.stream()
                    .map(DeptDataVo::getSecondValueNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setFirstValueNum(firstValueNum);
            vo.setSecondValueNum(secondValueNum);
            vo.setFirstValue(ThousandSeparatorUtil.format(firstValueNum));
            vo.setSecondValue(ThousandSeparatorUtil.format(secondValueNum));
            vo.setChild(deptDataVos);
            res.add(vo);
        }
        
        // 部门级排序
        res = res.stream()
                .sorted(Comparator.comparing(DeptDataVo::getFirstValueNum, Comparator.reverseOrder())
                        .thenComparing(DeptDataVo::getSecondValueNum, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        // 计算合计
        if (CollectionUtils.isNotEmpty(res)) {
            DeptDataVo totalDataVo = new DeptDataVo();
            totalDataVo.setKey("合计");
            BigDecimal totalFirstValue = res.stream()
                    .map(DeptDataVo::getFirstValueNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalSecondValue = res.stream()
                    .map(DeptDataVo::getSecondValueNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalDataVo.setFirstValueNum(totalFirstValue);
            totalDataVo.setSecondValueNum(totalSecondValue);
            totalDataVo.setFirstValue(ThousandSeparatorUtil.format(totalFirstValue));
            totalDataVo.setSecondValue(ThousandSeparatorUtil.format(totalSecondValue));
            res.add(0, totalDataVo);
        }
        
        return res;
    }

    /**
     * 判断是否为券类型
     */
    private boolean isVoucherType(String type) {
        return DeptDataEnum.HOTEL_VOUCHER.name().equals(type) ||
               DeptDataEnum.APP_DISCOUNT_VOUCHER.name().equals(type) ||
               DeptDataEnum.BREAKFAST_VOUCHER.name().equals(type) ||
               DeptDataEnum.UPGRADE_ROOM_VOUCHER.name().equals(type) ||
               DeptDataEnum.DELAY_CHECKOUT_VOUCHER.name().equals(type);
    }
}
