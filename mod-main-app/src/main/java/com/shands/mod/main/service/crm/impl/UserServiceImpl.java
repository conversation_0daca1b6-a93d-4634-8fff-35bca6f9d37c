package com.shands.mod.main.service.crm.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shands.mod.config.RocketMqConfig;
import com.shands.mod.dao.mapper.ModMenuMapper;
import com.shands.mod.dao.mapper.UserMapper;
import com.shands.mod.dao.mapper.hs.cleanRoom.HsCleanRoomConfigMapper;
import com.shands.mod.dao.mapper.remaindergroup.ReminderGroupDetailMapper;
import com.shands.mod.dao.mapper.remaindergroup.ReminderGroupMapper;
import com.shands.mod.dao.mapper.syncuc.HsDutifulAreaDao;
import com.shands.mod.dao.mapper.syncuc.ModDeptDao;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModPostInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModRoleUcDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.Company;
import com.shands.mod.dao.model.Department;
import com.shands.mod.dao.model.ModMenu;
import com.shands.mod.dao.model.ReminderGroup;
import com.shands.mod.dao.model.ReminderGroupDetail;
import com.shands.mod.dao.model.User;
import com.shands.mod.dao.model.enums.UserStatusEnum;
import com.shands.mod.dao.model.res.ReceiveOrderModeRes;
import com.shands.mod.dao.model.res.UserDeptRes;
import com.shands.mod.dao.model.rolepermissionnew.RoleNew;
import com.shands.mod.dao.model.syncuc.ModDept;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModPostInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.v0701.vo.DeptUsers;
import com.shands.mod.dao.model.v0701.vo.ModUserLoginVo;
import com.shands.mod.dao.model.v0701.vo.TreeVo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.service.UcAuthenticationService;
import com.shands.mod.main.service.app.UcUserService;
import com.shands.mod.main.service.common.UserInfoCommonService;
import com.shands.mod.main.service.crm.IDepartmentService;
import com.shands.mod.main.service.crm.IUserExtendService;
import com.shands.mod.main.service.crm.IUserService;
import com.shands.mod.main.service.crm.UcLoginService;
import com.shands.mod.main.service.message.MessageConstant;
import com.shands.mod.main.service.message.MessageTypeEnum;
import com.shands.mod.main.service.message.ShandsMessageService;
import com.shands.mod.main.service.rolepermissionnew.RoleNewService;
import com.shands.mod.main.util.BaiDaWuRSAUtils;
import com.shands.mod.main.util.MainConstants;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.vo.LoginVO;
import com.shands.mod.main.vo.UserStatusVO;
import com.shands.mod.main.vo.feign.MessageVO;
import com.shands.mod.message.service.IMessageService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.mod.util.RequestUtils;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.KeyValueVO;
import com.shands.mod.vo.ResultCode;
import com.shands.mod.vo.ResultVO;
import com.shands.mod.vo.UserDetailVO;
import com.shands.mod.vo.UserExtendVO;
import com.shands.mod.vo.UserInfoVO;
import com.shands.mod.vo.UserReq;
import com.shands.uc.model.auth.UserLoginRes;
import com.shands.uc.model.req.user.SetPassword;
import com.shands.uc.model.req.v3.FindUserInfoByMobileReq;
import com.shands.uc.model.req.v3.PublicLoginReq;
import com.shands.uc.model.req.v3.auth.RoleInfo;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements IUserService {

  private static final String USER_LOGIN_SESSION = "mod3:USER_LOGIN_SESSION:";

  private static final long USER_LOGIN_SESSION_TIME = 86400;

  private static final String REGEX_PASSWORD = "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[!@#$%^&*()_+{}\":;'<>,.?/~`]).{8,18}$";

  @Value("${app.token.expiration:259200}")
  private long expiration;

  @Value("${uc.sercret:380583f66a4c462ba62ca690c231be9f}")
  private String ucSercret;

  @Value("${uc.appId:mod3}")
  private String ucAppId;

  @Value("${sks.url:http://test.server.rest.sks.com}")
  private String sksUrl;

  @Value("${uc.domain:http://test-uc3.kaiyuanhotels.com}")
  private String domain;

  @Value("${uc.ucUserName:admin}")
  private String ucUserName;

  @Value("${uc.ucPassword:123456}")
  private String ucPassword;

  @Resource
  private UserMapper userMapper;

  @Resource
  private ModDeptDao modDeptDao;

  @Resource
  HsDutifulAreaDao dutifulAreaDao;

  @Resource
  private ModPostInfoDao modPostInfoDao;

  @Resource
  private ModUserDao modUserDao;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Autowired
  private IMessageService messageService;
/*
  @Autowired
  private MessageService messageService;*/

  @Autowired
  private IUserExtendService userExtendService;

  @Autowired
  private IDepartmentService departmentService;

  @Autowired
  private RedisTemplate redisTemplate;

  @Autowired
  private RoleNewService roleService;

  @Autowired
  private ModRoleUcDao modRoleUcDao;

  @Autowired
  private HsCleanRoomConfigMapper configMapper;

  @Autowired
  private ModMenuMapper modMenuMapper;

  @Autowired
  private ReminderGroupDetailMapper reminderGroupDetailMapper;

  @Autowired
  private ShandsMessageService shandsMessageService;

  @Autowired
  private ReminderGroupMapper reminderGroupMapper;

  private final UcUserService ucUserService;

  private final UcAuthenticationService ucAuthenticationService;

  private final UcLoginService ucLoginService;

  private final UserInfoCommonService userInfoCommonService;

  public UserServiceImpl(UcUserService ucUserService,
      UcAuthenticationService ucAuthenticationService,
      UcLoginService ucLoginService, UserInfoCommonService userInfoCommonService) {
    this.ucUserService = ucUserService;
    this.ucAuthenticationService = ucAuthenticationService;
    this.ucLoginService = ucLoginService;
    this.userInfoCommonService = userInfoCommonService;
  }

  @Override
  public ResultVO updatePasswordByCode(String mobile, String code,String password, boolean platform)
      throws Exception {
    //判断密码复杂性
    if (!isMatch(password)) {
      throw new ServiceException("密码必须为8-18大小写字母+数字+特殊符号组合");
    }
    if (StringUtils.isNotBlank(mobile) && StringUtils.isNotBlank(code)) {
      String key = Tools.buildKey(BaseConstants.UPDATE_PASSWORD, code);
      Object obj = this.redisTemplate.opsForValue().get(key);
      if (obj != null && obj.toString().equals(mobile)) {
        this.redisTemplate.delete(key);
          User user = this.userMapper.getByMobile(mobile,false);
          if (user == null) {
            throw new ServiceException("用户信息错误");
          }
          user.setPassword(DigestUtils.md5Hex(password));
          user.setUpdateTime(new Date());

          boolean upflag = this.userMapper.updateByPrimaryKeySelective(user) > 0;

          //同步修改通宝密码
          if(upflag){

            //查询用户通宝用户信息
            UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
            ucAuthenticationDto.setAppId(ucAppId);
            ucAuthenticationDto.setSecret(ucSercret);
            ucAuthenticationDto.setDomain(domain);

            PublicLoginReq publicLoginReq = new PublicLoginReq();
            publicLoginReq.setAppId(ucAppId);
            publicLoginReq.setUsername(ucUserName);
            publicLoginReq.setPassword(ucPassword);

            //调用通宝接口 换取token信息
            UserLoginRes userLoginRes = ucAuthenticationService.
                loginByUserName(ucAuthenticationDto,publicLoginReq);

            if(userLoginRes == null ||
                !org.springframework.util.StringUtils.hasText(userLoginRes.getToken())){
              throw new RuntimeException("uc接口鉴权信息获取失败");
            }

            //设置token请求头
            ucAuthenticationDto.setToken(userLoginRes.getToken());

            SetPassword setPassword = new SetPassword();
            setPassword.setPassword(password);
            setPassword.setUserId(ThreadLocalHelper.getUser().getUcId());

            com.shands.uc.base.vo.ResultVO resultVO = ucAuthenticationService.updatePasswd(
                ucAuthenticationDto, setPassword);
            if (resultVO == null || !resultVO.isSuccess()) {
              String failedMsg = resultVO == null ? "修改失败" : resultVO.getMessage();
              return ResultVO.failed(failedMsg);
            }
          }

          return ResultVO.success(upflag);
      }else{
        return ResultVO.failed("验证码错误");
      }
    }
    return ResultVO.failed("修改失败");
  }

  @Override
  public ResultVO updatePasswordByParam(String param) throws Exception {
    // 解密
    String decrypt = BaiDaWuRSAUtils.decrypt(param,
        BaiDaWuRSAUtils.getPrivateKey(BaiDaWuRSAUtils.DL_RSA_PRIVATE_KEY));
    LoginVO vo = JSON.parseObject(decrypt, LoginVO.class);
    if (org.apache.commons.lang3.StringUtils.isAnyEmpty(vo.getMobile(), vo.getPassword(),
        vo.getCode())) {
      return ResultVO.failed("手机号，验证码，密码是必须要填写的，检查下再提交哦~");
    }
    return updatePasswordByCode(vo.getMobile(), vo.getCode(), vo.getPassword(), vo.isPlatform());
  }


  @Override
  public User getById(int id) {
    User result = null;
    if (id > 0) {
      result = this.userMapper.selectByPrimaryKey(id);
    }
    return result;
  }

  private boolean checkCompany(Company company) {
    if (company != null
        && company.getDeleted() != null
        && company.getDeleted().intValue() == BaseConstants.DATA_UNDELETED
        && company.getActivity() != null
        && company.getActivity().intValue() == BaseConstants.DATA_ACTIVATED) {
      return true;
    }
    return false;
  }

  @Override
  public boolean clearDeviceIdByToken(String token, int userId, String deviceId) {
    boolean result = false;
    if (userId > 0 && StringUtils.isNotBlank(token) && StringUtils.isNotBlank(deviceId)) {
      // 通过token获取用户信息
      UserInfoVO vo = this.getUserByToken(token);

      if (vo != null) {
        User user = new User();
        user.setId(userId);
        user.setDeviceId(deviceId);
        this.userMapper.clearDeviceId(user);
      }
    }
    return result;
  }

  @Override
  public List<KeyValueVO> listByDept(int deptId, boolean recursion) {
    List<KeyValueVO> result = null;
    if (deptId > 0) {
      if (recursion) {
        Department dept = this.departmentService.getById(deptId);
        if (dept == null) {
          return result;
        }

        // 构建部门ID列表
        List<Department> list = this.departmentService.list(dept.getCompanyId(), null, 1, false);
        Set<Integer> deptIds = this.getDeptIdSetByPid(deptId, list);

        // 从部门ID列表构建用户列表
        result = new ArrayList<>();
        for (Integer row : deptIds) {
          List<KeyValueVO> tmp = this.getKeyValueListByDept(row);
          if (tmp != null && !tmp.isEmpty()) {
            result.addAll(tmp);
          }
        }
      } else {
        result = this.getKeyValueListByDept(deptId);
      }
    }
    return result;
  }

  @Override
  public UserDetailVO toUserDetailVO(User user, int companyId) {
    UserDetailVO result = null;
    if (user != null) {
      UserExtendVO bean = this.userExtendService.getUserExtendVO(user.getId(), companyId);

      //查询用户的所有roleId
      List<RoleNew> roleByUserId = roleService.findRoleByUserId(user.getId());

      List<Integer> roleIds = Lists.newArrayList();

      roleByUserId.forEach(o -> {
        roleIds.add(o.getId());
      });

      if (bean != null) {
        result =
            new UserDetailVO(
                user,
                bean.getRoles(),
                null,
                bean.getDeptId(),
                bean.getDeptName(),
                BaseConstants.DATA_ACTIVATED, roleIds);
      }
    }
    return result;
  }

  @Override
  public ReceiveOrderModeRes getReceiveOrderModeAndMore(Integer id) {
    ReceiveOrderModeRes resp = new ReceiveOrderModeRes();
    User user = userMapper.selectByPrimaryKey(id);
    if (user == null) {
      throw new ServiceException("此用户不存在");
    }
    resp.setReceiveOrderMode(user.getReceiveOrderMode());
    int chooseAreas = dutifulAreaDao.chooseArea(id,ThreadLocalHelper.getCompanyId());
    if(chooseAreas > 0){
      resp.setArea(true);
    }else{
      resp.setArea(false);
    }

    //查询用户接单开关权限
    List<String> roles = ThreadLocalHelper.getROLES();

    if(roles != null && !roles.isEmpty()){

      List<String> param = modRoleUcDao.selectPcPerms(roles,"APP");

      if(param.contains("receiving_flag")){
        resp.setReceivingFlag(true);
      }
    }

    return resp;
  }

  @Override
  @Transactional
  public Boolean updateReceiveOrderMode(UserInfoVO userInfoVO) {
    User record = new User();
    record.setId(userInfoVO.getId());
    record.setReceiveOrderMode(userInfoVO.getReceiveOrderMode());
    if (userMapper.updateByPrimaryKeySelective(record) > 0) {
      return Boolean.TRUE;
    }
    return Boolean.FALSE;
  }

  /**
   * 根据PID递归获取部门ID集合
   *
   * @param pid  要搜索的部门的PID
   * @param list 部门对象列表
   * @return
   */
  private Set<Integer> getDeptIdSetByPid(Integer pid, List<Department> list) {
    Set<Integer> result = new HashSet<>();
    if (list != null && !list.isEmpty() && pid != null && pid > 0) {
      for (Department row : list) {
        if (pid.equals(row.getId())) {
          result.add(pid);
        }
        if (pid.equals(row.getpId())) {
          Set<Integer> tmp = this.getDeptIdSetByPid(row.getId(), list);
          if (tmp != null && !tmp.isEmpty()) {
            result.addAll(tmp);
          }
        }
      }
    }
    return result;
  }

  /**
   * 获取部门用户信息（ID，NAME）
   *
   * @param deptId 部门ID
   * @return
   */
  private List<KeyValueVO> getKeyValueListByDept(int deptId) {
    List<KeyValueVO> result = null;
    if (deptId > 0) {
      List<User> list = this.listByDepartment(deptId);
      if (list != null && !list.isEmpty()) {
        result = new ArrayList<>(list.size());
        // 添加本级部门用户
        for (User row : list) {
          result.add(new KeyValueVO(row.getId().toString(), row.getName()));
        }
      }
    }
    return result;
  }

  @Override
  public boolean logout(String token) {
    boolean result = false;
    if (StringUtils.isNotBlank(token)) {
      String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
      this.redisTemplate.delete(key);
    }
    return result;
  }

  @Override
  public UserInfoVO getUserByToken(String token) {

    UserInfoVO resultVo = null;
    UserInfoVO result = new UserInfoVO();

    if(!StringUtils.isNotBlank(token)){
      return null;
    }

    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    Object obj = this.redisTemplate.opsForValue().get(key);
    if (obj != null) {
      resultVo = (UserInfoVO) obj;
    }else{
      return null;
    }

    //定期校验当前在职状态（24小时执行一次）
    //定期校验用户当前状态 or uc离职
    String sessionKey = USER_LOGIN_SESSION + token;
    if(redisTemplate.hasKey(sessionKey)){
      return resultVo;
    }else {

      try{

        String mobile = resultVo.getMobile();

        //查询用户通宝用户信息
        UserAuthInfo userAuthInfo = ucUserService.ucLoginByUsername(mobile);

        if (userAuthInfo == null) {
          return null;
        }

        //解析通宝用户信息
        if (userAuthInfo.getRoles() == null || userAuthInfo.getRoles().isEmpty()) {
          return null;
        }

        //更新用户信息
        ucLoginService.updUcUserInfo(userAuthInfo);

        //如果用户为离职状态，则直接返回空对象
        if (!userAuthInfo.isStatus()) {
          return null;
        }

        ModUser modUser = new ModUser();
        modUser.setDeleted(0);
        modUser.setUcId(userAuthInfo.getUserId());
        modUser.setUcCompanyId(userAuthInfo.getCompanyId());
        ModUser user1 = modUserDao.queryByObj(modUser);

        if (user1 == null) {
          return null;
        }

        BeanUtils.copyProperties(user1, result);

        //设置酒店编码
        result.setCompany(userAuthInfo.getCompanyId());
        //设置岗位信息
        ModPostInfo modPostInfo = modPostInfoDao.queryByUcId(userAuthInfo.getPostId());
        if (modPostInfo != null) {
          result.setPosition(modPostInfo.getName());
        }

        //设置用户角色权限赋值
        if(org.springframework.util.StringUtils.hasText(resultVo.getPlatform())
            && ("PC".equals(resultVo.getPlatform()) || "PLAT".equals(resultVo.getPlatform()))){
          ucLoginService.getRights(result, userAuthInfo, null);
        }else{
          ucLoginService.getRights(result, userAuthInfo, "APP");
        }

        result.setToken(token);
        result.setUcUser(true);
        result.setHotelName(userAuthInfo.getHotelName());

        Integer ucDeptId = userAuthInfo.getDeptId();
        if (ucDeptId != null) {

          //根据通宝部门id查询部门信息
          ModDept modDept = modDeptDao.queryByUcDeptId(ucDeptId);
          if (modDept != null) {
            result.setDeptId(modDept.getId());
            result.setUcDeptId(ucDeptId);
            result.setDeptName(modDept.getName());
          }
        }

        //初始化用户角色信息
        List<RoleInfo> roleInfos = userAuthInfo.getRoles();
        if (roleInfos != null && !roleInfos.isEmpty()) {

          List<String> roleCode = new ArrayList<>();
          List<String> roleName = new ArrayList<>();

          roleInfos.forEach(x -> {

            roleCode.add(x.getCode());
            roleName.add(x.getName());
          });

          result.setRoles(roleCode);
          result.setUcRoleCode(String.join(",", roleCode));
          result.setRoleName(String.join(",", roleName));
        }

        //缓存用户数据到redis
        String inkey = Tools.buildKey(BaseConstants.CACHE_USER, token);
        this.redisTemplate.opsForValue().set(inkey, result, this.expiration, TimeUnit.SECONDS);
        //设置24小时定期更新用户信息
        this.redisTemplate.opsForValue().set(sessionKey, result, USER_LOGIN_SESSION_TIME, TimeUnit.SECONDS);

        return result;

      }catch (Exception e){
        log.error(e.getMessage(), e);
        return null;
      }
    }
  }

  @Override
  public List<UserDetailVO> listByCompany(int companyId, UserDetailVO search) {
    List<UserDetailVO> result = new ArrayList<>();
    if (companyId > 0) {
      Map<String, Object> data = new HashMap<>();
      if (StringUtils.isNotBlank(search.getName())) {
        data.put("name", search.getName());
      }
      if (StringUtils.isNotBlank(search.getUsername())) {
        data.put("username", search.getUsername());
      }
      if (StringUtils.isNotBlank(search.getMobile())) {
        data.put("mobile", search.getMobile());
      }
      if (search.getDeptId() != null && search.getDeptId() > 0) {
        data.put("deptId", search.getDeptId());
      }
      if (CollectionUtil.isNotEmpty(search.getDeptList())) {
        data.put("deptList", search.getDeptList());
      }
      if (CollectionUtil.isNotEmpty(search.getUserStatusList())) {
        data.put("userStatusList", search.getUserStatusList());
      }
      data.put("companyId", companyId);

      List<User> list = this.userMapper.listByExtendAndWeb(data);
      if (list != null && !list.isEmpty()) {
        for (User row : list) {
          //获取新角色信息
          // 获取部门信息
          String deptName = StringUtils.EMPTY;
          if (row.getDeptId() != null && row.getDeptId() > 0) {
            Department dept = this.departmentService.getById(row.getDeptId());
            if (dept != null) {
              deptName = dept.getName();
            }
          }

          result.add(
              new UserDetailVO(
                  row, null, null, row.getDeptId(), deptName,null,
                  null));
        }
      }
    }
    return result;
  }

  //递归查找所有菜单的子类菜单
  private List<TreeVo> getChildrens(int id,List<TreeVo> all){

    //子菜单
    List<TreeVo> childList = new ArrayList<>();
    for(TreeVo vo : all){
      if(!vo.getpId().equals(vo.getUcId())){
        if(vo.getpId().equals(id)){
          childList.add(vo);
        }
      }
    }

    //递归
    for(TreeVo child : childList){
      List<TreeVo> vos = getChildrens(child.getUcId(),all);
      child.setChildren(vos);
      child.setAmount(vos.size());
    }

    if(childList.size() == 0){
      return new ArrayList<>();
    }

    return childList;
  }

  @Override
  public List<TreeVo> listUserTree(int companyId) {

    //根据酒店编码查询部门信息
    Integer hotelId = ThreadLocalHelper.getCompanyId();
    List<TreeVo> allVos = modDeptDao.queryDeptTree(hotelId);

    //获取顶级部门信息
    List<TreeVo> topLevel = allVos.stream().filter(x ->
        x.getpId().equals(x.getUcId())).collect(Collectors.toList());

    for(TreeVo vo : topLevel){
      List<TreeVo> childList = getChildrens(vo.getUcId(),allVos);
      vo.setChildren(childList);
      vo.setNode(true);
      vo.setAmount(childList.size());
    }

    return topLevel;
  }

  @Override
  public List<TreeVo> listUserTree(int companyId, boolean onlyQueryFeishuDeparmentId) {

    //根据酒店编码查询部门信息
    Integer hotelId = ThreadLocalHelper.getCompanyId();
    List<TreeVo> allVos = modDeptDao.queryDeptTree(hotelId);

    if (onlyQueryFeishuDeparmentId) {
      allVos = allVos.stream().filter(o -> StringUtils.isNotBlank(o.getFeishuDepartmentId())).collect(Collectors.toList());
    }

    //获取顶级部门信息
    List<TreeVo> topLevel = allVos.stream().filter(x ->
        x.getpId().equals(x.getUcId())).collect(Collectors.toList());

    for(TreeVo vo : topLevel){
      List<TreeVo> childList = getChildrens(vo.getUcId(),allVos);
      vo.setChildren(childList);
      vo.setNode(true);
      vo.setAmount(childList.size());
    }

    return topLevel;
  }

  @Override
  public List<User> listByDepartment(int deptId) {
    List<User> result = null;
    if (deptId > 0) {
      Map<String, Object> data = new HashMap<>(1);
      data.put("deptId", deptId);
      data.put("companyId", ThreadLocalHelper.getCompanyId());
      result = this.userMapper.listByExtend(data);
    }
    return result;
  }

  @Override
  public String sendLoginCode(String mobile) {
    String result = StringUtils.EMPTY;

    if (StringUtils.isNotBlank(mobile)) {
      String code = RandomStringUtils.randomNumeric(6);

      MessageVO messageVO = new MessageVO();
      messageVO.setMessageType(BaseConstants.MESSAGE_TYPE_LOGIN_CODE);
      messageVO.setReceivers(mobile);
      messageVO.setReceiverType(BaseConstants.RECEIVE_TYPE_CUSTOMER);
      messageVO.setContent(String.format(MainConstants.FORMAT_SMS_LOGIN, code));
      messageVO.setCompanyId(BaseConstants.PLATFORM_COMPANY);
      messageVO.setGroupId(BaseConstants.PLATFORM_COMPANY);
      messageVO.setCreateUser(BaseConstants.SYSTEM_USER);

      int amount = this.messageService.sendSms(messageVO.getMessageType(), messageVO.getMessageSubtype(), messageVO.getReceivers(), messageVO.getContent(), 0, messageVO.getReceiverType(), messageVO.getCompanyId(), messageVO.getGroupId(), messageVO.getCreateUser());

      if (amount > 0) {
        String key = Tools.buildKey(BaseConstants.CACHE_LOGIN, code);
        // 短信验证码10分钟有效
        this.redisTemplate.opsForValue().set(key, mobile, 600, TimeUnit.SECONDS);
        result = code;
      } else {
        throw new ServiceException("验证码发送失败");
      }
    }
    return result;
  }

  @Override
  public String updatePasswordCode(String mobile) {
    String result = StringUtils.EMPTY;

    if (StringUtils.isNotBlank(mobile)) {
      User user = this.userMapper.getByMobile(mobile, false);

      if (user == null) {
        throw new ServiceException("无此用户");
      }

      String code = RandomStringUtils.randomNumeric(6);

      MessageVO messageVO = new MessageVO();
      messageVO.setMessageType(BaseConstants.MESSAGE_TYPE_LOGIN_CODE);
      messageVO.setReceivers(mobile);
      messageVO.setReceiverType(BaseConstants.RECEIVE_TYPE_CUSTOMER);
      messageVO.setContent(String.format(MainConstants.UPDATE_PASSWORD_SMS, code));
      messageVO.setCompanyId(BaseConstants.PLATFORM_COMPANY);
      messageVO.setGroupId(BaseConstants.PLATFORM_COMPANY);
      messageVO.setCreateUser(BaseConstants.SYSTEM_USER);

      int amount = this.messageService.sendSms(messageVO.getMessageType(), messageVO.getMessageSubtype(), messageVO.getReceivers(), messageVO.getContent(), 0, messageVO.getReceiverType(), messageVO.getCompanyId(), messageVO.getGroupId(), messageVO.getCreateUser());


      if (amount > 0) {
        String key = Tools.buildKey(BaseConstants.UPDATE_PASSWORD, code);
        // 短信验证码10分钟有效
        this.redisTemplate.opsForValue().set(key, mobile, 600, TimeUnit.SECONDS);
        result = code;
      } else {
        throw new ServiceException("验证码发送失败");
      }
    }
    return result;
  }

  @Override
  public Integer getSoundSelect() {
    User user = userMapper.selectByPrimaryKey(ThreadLocalHelper.getUser().getId());
    return user.getSoundSelect();
  }

  @Override
  public void setSoundSelect(UserInfoVO userInfoVO) {
    User record = new User();
    record.setId(ThreadLocalHelper.getUser().getId());
    record.setSoundSelect(userInfoVO.getSoundSelect());
    userMapper.updateByPrimaryKeySelective(record);
  }

  @Override
  public List<UserExtendVO> getUserCompanyInfos(){
    //查询用户通宝用户信息
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);

    PublicLoginReq publicLoginReq = new PublicLoginReq();
    publicLoginReq.setAppId(ucAppId);
    publicLoginReq.setUsername(ucUserName);
    publicLoginReq.setPassword(ucPassword);

    //调用通宝接口 换取token信息
    UserLoginRes userLoginRes = ucAuthenticationService.
        loginByUserName(ucAuthenticationDto,publicLoginReq);

    if(userLoginRes == null ||
        !org.springframework.util.StringUtils.hasText(userLoginRes.getToken())){
      throw new RuntimeException("uc接口鉴权信息获取失败");
    }

    //设置token请求头
    ucAuthenticationDto.setToken(userLoginRes.getToken());

    FindUserInfoByMobileReq findUserInfoByMobileReq = new FindUserInfoByMobileReq();
    findUserInfoByMobileReq.setAppId(ucAppId);
    findUserInfoByMobileReq.setMobile(ThreadLocalHelper.getUser().getMobile());

    //查询用户通宝信息
    UserAuthInfo userAuthInfo = ucAuthenticationService
        .loginByMobile(ucAuthenticationDto,findUserInfoByMobileReq);

    if(userAuthInfo == null){
      throw new RuntimeException("uc用户信息查询为空");
    }

    //解析通宝用户信息
    if(userAuthInfo.getRoles() == null || userAuthInfo.getRoles().isEmpty()){
      throw new RuntimeException("uc用户角色为空");
    }

    ucLoginService.updUcUserInfo(userAuthInfo);

    if(!userAuthInfo.isStatus()){
      throw new RuntimeException("该人员处于离职状态");
    }

    List<RoleInfo> roleInfos = userAuthInfo.getRoles();
    Map<String,RoleInfo> roleInfoMap = roleInfos.stream().
        collect(Collectors.toMap(RoleInfo :: getCode, RoleInfo -> RoleInfo));

    List<String> keys = new ArrayList<>();
    for(String kk : roleInfoMap.keySet()){

      keys.add(kk);
    }

    Integer ucCompanyId = ThreadLocalHelper.getUser().getUcCompanyId();

    //查询权限列表
    List<String> perms = modRoleUcDao.selectPcPerms(keys,"APP");

    //查询酒店列表
    ModHotelInfo modHotelInfo = new ModHotelInfo();

    //如果是admin或者IT维护人员，查询所有微管家已上线酒店列表信息
    ModHotelInfo hotelInfo = modHotelInfoDao.queryByUcCompanyId(ucCompanyId);

    if(hotelInfo == null){
      throw new RuntimeException("机构或酒店信息未查询到");
    }

    if(!roleInfoMap.containsKey("admin") && !roleInfoMap.containsKey("It_maintainer")){

      if(hotelInfo == null || hotelInfo.getHotelStatus() == 0){
        throw new RuntimeException("未查询到您的机构酒店信息");
      }
      modHotelInfo.setUcCompanyId(ucCompanyId);
    }else{
      modHotelInfo.setHotelStatus(1);
    }

    List<ModHotelInfo> modHotelInfos = modHotelInfoDao.queryAll(modHotelInfo);

    if(modHotelInfos != null && !modHotelInfos.isEmpty()){

      List<UserExtendVO> rights = new ArrayList<>();
      modHotelInfos.forEach( x -> {

        UserExtendVO vo = new UserExtendVO();
        vo.setPermissions(perms);
        vo.setCompanyId(x.getHotelId());
        vo.setCompanyName(x.getHotelName());
        vo.setRoles(String.join(",",keys));
        rights.add(vo);
      });

      return rights;
    }
    return null;
  }

  @Override
  public List<DeptUsers> getUserByDeptId(Integer deptId) {

    ModDept modDept = modDeptDao.queryById(deptId);
    if(modDept == null){
      throw new RuntimeException("未查询到用户信息");
    }

    return modUserDao.queryAllByDeptId(modDept.getUcId());
  }

  @Override
  public List<UserDeptRes> selectByUser(Integer hotelId,String userName,String name,Integer deptId) {
    return this.res(hotelId,userName,name,deptId);
  }

  @Override
  public List<UserDeptRes> deptForUser(Integer hotelId, String userName) {
    List<UserDeptRes> userDeptRes = new ArrayList<>();
    List<UserDeptRes> res = this.res(hotelId,userName,null,null);
    Map<Integer, List<UserDeptRes>> listMap = res.stream()
        .collect(Collectors.groupingBy(UserDeptRes::getDeptId));
    Set<Integer> deptId = listMap.keySet();
    deptId.forEach(key->{
      UserDeptRes dept = new UserDeptRes();
      dept.setId(key);
      dept.setName(listMap.get(key).get(0).getDeptName());
      dept.setUsers(listMap.get(key));
      userDeptRes.add(dept);
    });
    return userDeptRes;
  }

  List<UserDeptRes> res(Integer hotelId,String userName,String name,Integer deptId){
    String dept = configMapper.getDept(hotelId);
    if(StringUtils.isEmpty(dept)){
      return new ArrayList<>();
    }
    List<String> stringList = Arrays.asList(dept.split(","));
    List<Integer> integers = stringList.stream().map(Integer :: valueOf).collect(Collectors.toList());
    return userMapper.selectByUser(hotelId,userName,name,deptId,integers);
  }

  @Override
  public List<ModHotelInfo> currentNew() {

    List<ModHotelInfo> resultVo = new ArrayList<>();

    //查询用户个人所属机构信息
    ModHotelInfo hotelInfo = modHotelInfoDao.queryByUcCompanyId(ThreadLocalHelper.getUser().getUcCompanyId());

    List<String> codes = userInfoCommonService.getUserSwitchs(ThreadLocalHelper.getUser().getId());

    resultVo = modHotelInfoDao.queryUserHotelInfosByCodes(codes);

    //如果用户切换酒店信息为空，添加用户个人所属机构名称
    if(resultVo == null || resultVo.isEmpty()){
      resultVo.add(hotelInfo);
    }

    return resultVo;
  }

  @Override
  public ResultVO<Boolean> adjustUserStatus(UserStatusVO statusVO, HttpServletRequest request) {

    UserStatusEnum userStatusEnum = UserStatusEnum.findEnumByCode(statusVO.getUserStatus());
    if (UserStatusEnum.NULL.equals(userStatusEnum)) {
      throw new ServiceException("用户状态系统未定义");
    }
    User user = userMapper.selectByPrimaryKey(statusVO.getUserId());
    if (user == null) {
      throw new ServiceException("用户不存在");
    }

    UserStatusEnum oldUserStatusEnum = UserStatusEnum.findEnumByCode(user.getUserStatus());
    if (userStatusEnum.equals(oldUserStatusEnum)) {
      throw new ServiceException("用户已处于" + userStatusEnum.getDesc() + "状态");
    }
    int updateCount = userMapper.updateUserStatusById(userStatusEnum.getCode(),
        statusVO.getUserId());
    boolean result = updateCount > 0;
    if (!result) {
      return ResultVO.failed();
    }
    // 更新redis
    String token = RequestUtils.getToken(request);
    ModUserLoginVo resultVo;
    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    Object obj = this.redisTemplate.opsForValue().get(key);
    if (obj != null) {
      resultVo = (ModUserLoginVo) obj;
      resultVo.setUserStatus(statusVO.getUserStatus());
      // 获得剩余过期时间
      long expireTime = redisTemplate.getExpire(key);
      // 重新缓存redis
      this.redisTemplate.opsForValue().set(key, resultVo, expireTime, TimeUnit.SECONDS);
    }

    return ResultVO.success();
  }

  @Override
  public ResultVO<Boolean> selfAdjustStatus(UserStatusVO statusVO, HttpServletRequest request) {
    log.info("用户调整自身状态请求参数: [{}]", statusVO);
    UserInfoVO userInfoVO = ThreadLocalHelper.getUser();
    Assert.notNull(statusVO.getUserId(), "用户ID不能为空");
    if (!statusVO.getUserId().equals(userInfoVO.getId())) {
      throw new ServiceException("当前用户不能修改其他用户状态");
    }

    ResultVO<Boolean> resultVO = this.adjustUserStatus(statusVO, request);

    // 更新成功
    if (resultVO != null && ResultVO.success().getCode().equals(resultVO.getCode())) {
      // 通知管理员
      this.noticeManager(userInfoVO, statusVO);
    }
    return resultVO;
  }

  @Override
  public ResultVO<Boolean> managerAdjustStatus(UserStatusVO statusVO, HttpServletRequest request) {
    log.info("管理员调整用户状态请求参数: [{}]", statusVO);
    UserInfoVO userInfoVO = ThreadLocalHelper.getUser();
    String companyId = BaseThreadLocalHelper.getCompany();
    if (StringUtils.isEmpty(companyId)) {
      throw new ServiceException("管理员用户关联酒店ID为空");
    }
    ModHotelInfo hotelInfo = modHotelInfoDao.queryById(Integer.parseInt(companyId));
    if (hotelInfo == null) {
      throw new ServiceException("管理员用户所在酒店信息不存在");
    }
    // 校验用户是否拥有该权限
    if (!this.checkManagerPermission(userInfoVO)) {
      throw new ServiceException("当前用户无员工状态管理权限");
    }
    // 检查被修改用户是否存在
    User user = userMapper.selectByPrimaryKey(statusVO.getUserId());
    if (user == null) {
      throw new ServiceException("被调整员工不存在");
    }
    if (!user.getUcCompanyId().equals(hotelInfo.getUcCompanyId())) {
      throw new ServiceException("不能调整其他机构的员工状态");
    }
    ResultVO<Boolean> resultVO = this.adjustUserStatus(statusVO, request);
    // 更新成功
    if (resultVO != null && ResultVO.success().getCode().equals(resultVO.getCode())) {
      // 通知被修改员工
      UserStatusEnum userStatusEnum = UserStatusEnum.findEnumByCode(statusVO.getUserStatus());
      String title = MessageConstant.MANAGER_CHANGE_USER_STATUS_TITLE;
      String content = MessageFormat.format(MessageConstant.MANAGER_CHANGE_USER_STATUS_TEXT,
          userStatusEnum.getDesc());
      try {
        this.noticeMsg(statusVO.getUserId(), user.getUcCompanyId(), title, content);
        log.info("管理员修改员工状态通知成功: [{}] -> [{}]", statusVO.getUserId(), userInfoVO.getId());
      } catch (Exception e) {
        log.error("管理员修改员工状态通知失败: [{}] -> [{}]", statusVO.getUserId(), userInfoVO.getId(), e);
      }
    }
    return resultVO;
  }

  @Override
  public List<UserStatusVO> findUserStatusList() {
    return Arrays.stream(UserStatusEnum.values())
        .filter(userStatusEnum -> !UserStatusEnum.NULL.equals(userStatusEnum))
        .map(userStatusEnum -> {
          UserStatusVO userStatusVO = new UserStatusVO();
          userStatusVO.setUserStatus(userStatusEnum.getCode());
          userStatusVO.setUserStatusDesc(userStatusEnum.getDesc());
          return userStatusVO;
        }).collect(Collectors.toList());
  }

  @Override
  public PageInfo<User> listUserV2(UserReq userResp) {
    String companyId = BaseThreadLocalHelper.getCompany();
    if (StringUtils.isEmpty(companyId)) {
      throw new ServiceException("用户关联酒店ID为空");
    }
    ModHotelInfo hotelInfo = modHotelInfoDao.queryById(Integer.parseInt(companyId));
    if (hotelInfo == null) {
      throw new ServiceException("用户所在酒店不存在");
    }
    userResp.setCompanyId(hotelInfo.getUcCompanyId());
    log.info("员工列表模糊查询:{}", userResp);
    //设置分页参数
    PageHelper.startPage(userResp.getPageNo(), userResp.getPageSize());
    List<User> userList = userMapper.findUserByParam(userResp.getName(), userResp.getCompanyId(),
        userResp.getDeptList(), userResp.getUserStatusList());
    return new PageInfo<>(userList);
  }

  /**
   * 检查当前用户是否拥有管理员权限
   */
  private boolean checkManagerPermission(UserInfoVO userInfoVO) {
    List<String> roles = BaseThreadLocalHelper.getROLES();
    if (CollectionUtil.isEmpty(roles)) {
      log.error("[{}]角色信息为空", userInfoVO);
      return false;
    }
    ModMenu modMenu = modMenuMapper.selectByPerms(BaseConstants.MENU_USER_STATUS);
    if (modMenu == null) {
      log.error("[{}]不存在或无效", BaseConstants.MENU_USER_STATUS);
      return false;
    }
    List<String> roleList = modRoleUcDao.queryRoleCodeByMenuInfo(modMenu.getId());
    if (CollectionUtil.isEmpty(roleList)) {
      log.error("[{}]未分配给相关角色", BaseConstants.MENU_USER_STATUS);
      return false;
    }
    Optional<String> optionalS = roles.stream().filter(roleList::contains).findFirst();
    return optionalS.isPresent();
  }

  private void noticeManager(UserInfoVO userInfoVO, UserStatusVO statusVO) {
    UserStatusEnum userStatusEnum = UserStatusEnum.findEnumByCode(statusVO.getUserStatus());
    if (UserStatusEnum.ONLINE.equals(userStatusEnum)) {
      log.info("员工状态改变后取消通知对应人员，修改后状态为在线:{}", userInfoVO);
      return;
    }
    ReminderGroup reminderGroup = null;
    try {
      reminderGroup = reminderGroupMapper.uniqSelect(userInfoVO.getCompany(),
          BaseConstants.MENU_USER_STATUS_DESC);
    } catch (Exception e) {
      log.error("查询[{}]小组异常", BaseConstants.MENU_USER_STATUS_DESC, e);
    }
    if (reminderGroup == null) {
      log.info("员工状态改变后取消通知对应人员，未查询到小组:[{}][{}]", userInfoVO.getCompany(),
          BaseConstants.MENU_USER_STATUS_DESC);
      return;
    }
    // 获得小组成员列表
    List<ReminderGroupDetail> groupUserList = reminderGroupDetailMapper
        .selectByReminderGroupIds(Collections.singletonList(reminderGroup.getId()));
    if (CollectionUtil.isEmpty(groupUserList)) {
      log.info("员工状态改变后取消通知对应人员，[{}]人员为空", BaseConstants.MENU_USER_STATUS_DESC);
      return;
    }
    String title = MessageConstant.SELF_CHANGE_USER_STATUS_TITLE;
    String content = MessageFormat.format(MessageConstant.SELF_CHANGE_USER_STATUS_TEXT,
        userInfoVO.getName(), userStatusEnum.getDesc());
    groupUserList.forEach(manager -> {
      try {
        this.noticeMsg(manager.getUserId(), userInfoVO.getUcCompanyId(), title, content);
        log.info("员工自改状态通知管理员成功: [{}] -> [{}]", userInfoVO.getId(), manager.getUserId());
      } catch (Exception e) {
        log.error("员工自改状态通知管理员失败: [{}] -> [{}]:[{}]", userInfoVO.getId(),
            manager.getUserId(), e);
      }
    });
  }

  /**
   * @param userId    用户ID
   * @param companyId 公司ID
   * @param title     标题
   * @param content   内容
   */
  private void noticeMsg(Integer userId, Integer companyId, String title, String content) {
    shandsMessageService.sendDirectYmMsg(RocketMqConfig.DIRECT_MESSAGE_APP_TOPIC,
        "", userId,
        content, title,
        MessageTypeEnum.MESSAGE_TYPE_USER_STATUS.getTypeCode(),
        "", companyId, -1, null,"-1");
  }

  private boolean isMatch(String value) {
    boolean result = false;
    if (StringUtils.isNotBlank(value)) {
      Pattern pattern = Pattern.compile(REGEX_PASSWORD);
      Matcher matcher = pattern.matcher(value);

      result = matcher.find();
    }
    return result;
  }

  @Override
  public ResultVO updateUcPassword(Integer ucId, String password) {

    //查询用户通宝用户信息
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);

    PublicLoginReq publicLoginReq = new PublicLoginReq();
    publicLoginReq.setAppId(ucAppId);
    publicLoginReq.setUsername(ucUserName);
    publicLoginReq.setPassword(ucPassword);

    //调用通宝接口 换取token信息
    UserLoginRes userLoginRes = ucAuthenticationService.
        loginByUserName(ucAuthenticationDto,publicLoginReq);

    if(userLoginRes == null ||
        !org.springframework.util.StringUtils.hasText(userLoginRes.getToken())){
      return new ResultVO();
    }

    //设置token请求头
    ucAuthenticationDto.setToken(userLoginRes.getToken());

    SetPassword setPassword = new SetPassword();
    setPassword.setPassword(password);
    setPassword.setUserId(ucId);

    com.shands.uc.base.vo.ResultVO resultVO = ucAuthenticationService.updatePasswd(
        ucAuthenticationDto, setPassword);
    if(resultVO == null) {
      return new ResultVO();
    }
    if(!resultVO.isSuccess()) {
      return new ResultVO(ResultCode.ERROR.getCode(),resultVO.getMessage());
    }
    return ResultVO.success(true);
  }
}
