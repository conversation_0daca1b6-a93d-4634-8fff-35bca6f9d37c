package com.shands.mod.main.controller.dataBoard;

import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.model.developmember.DevelopmentMemberBo;
import com.shands.mod.dao.model.req.GiftOrderReq;
import com.shands.mod.main.service.app.DevelopmentMembershipService;
import com.shands.mod.main.service.development.DevelopmentService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.Map;

/** app个人中心会员发展记录 礼包销售记录
 * <AUTHOR>
 * @date 2022.4.6 16:42:53
 */
@RestController
@RequestMapping(value = "/developmentMember")
@Api(value = "发展会员、礼包订单", tags = "发展会员")
public class MembershipController extends BaseController {


  private final DevelopmentMembershipService developmentMembershipService;
  private final DevelopmentService developmentService;

  public MembershipController(
      DevelopmentMembershipService developmentMembershipService,
      DevelopmentService developmentService) {
    this.developmentMembershipService = developmentMembershipService;
    this.developmentService = developmentService;
  }

  /**
   * 获取发展会员记录接口
   */
  @GetMapping(value = "/getInfo")
  @ApiOperation(value = "获取发展会员记录接口")
  public ResultVO getInfo(
      @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
      @RequestParam("type") String type,
      @RequestParam("time") String time) {
    //获取手机
    String mobile = ThreadLocalHelper.getUser().getMobile();
    if (StringUtils.isEmpty(mobile)) {
      ResultVO.failed("手机号为空");
    }
    return developmentMembershipService.getInfo(mobile, type, time, pageNo, pageSize);
  }

  /**
   * 获取礼包订单记录接口
   */
  @PostMapping(value = "/getGiftPackOrder")
  @ApiOperation(value = "获取礼包订单记录接口")
  public ResultVO getGiftPackOrder(
      @RequestBody @Valid GiftOrderReq GiftOrderReq) {
    //获取手机
    String mobile = ThreadLocalHelper.getUser().getMobile();
    if (StringUtils.isEmpty(mobile)) {
      ResultVO.failed("手机号为空");
    }
    return developmentMembershipService.getGiftPackOrder(mobile, GiftOrderReq);
  }

  /**
   * 获取发展会员记录接口
   */
  @PostMapping(value = "/getDevelopmentMemberList")
  @ApiOperation(value = "获取发展会员记录接口")
  public ResultVO getDevelopmentMemberList(@RequestBody DevelopmentMemberBo developmentMemberBo) {
    return ResultVO.success(developmentService.getDevelopmentMemberList(developmentMemberBo));
  }

  /**
   * 获取发展会员记录最新三条数据接口
   */
  @PostMapping(value = "/getDevelopmentMemberListByLimit3")
  @ApiOperation(value = "获取发展会员记录最新三条数据接口")
  public ResultVO getDevelopmentMemberListByLimit3(@Valid @RequestBody Map<String, String> map) {
    return ResultVO.success(developmentService.getDevelopmentMemberListByLimit3(map.get("userId")));
  }


  /**
   * 获取发展会员记录最新三条数据接口
   */
  @PostMapping(value = "/getDevelopmentMemberListByLimit3V2")
  @ApiOperation(value = "获取发展会员记录最新三条数据接口")
  public ResultVO getDevelopmentMemberListByLimit3V2(@Valid @RequestBody Map<String, String> map) {
    return ResultVO.success(developmentService.getDevelopmentMemberListByLimit3V2(map.get("userId")));
  }

  /**
   * 获取发展会员记录接口
   */
  @PostMapping(value = "/getDevelopmentMemberListV2")
  @ApiOperation(value = "获取发展会员记录接口")
  public ResultVO getDevelopmentMemberListV2(@RequestBody DevelopmentMemberBo developmentMemberBo) {
    return ResultVO.success(developmentService.getDevelopmentMemberListV2(developmentMemberBo));
  }
}
