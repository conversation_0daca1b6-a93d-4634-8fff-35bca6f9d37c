package com.shands.mod.main.service.statistics.impl;

import cn.hutool.core.date.DateUtil;
import com.delonix.bi.dao.mapper.AdsPowerDlEcoAppEmployeePerfDMapper;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.datarevision.vo.ModuleMenuVo;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.res.AdsHotelPerformanceHDTO;
import com.shands.mod.dao.model.datarevision.vo.DeptDataVo;
import com.shands.mod.dao.model.enums.DeptDataEnum;
import com.shands.mod.dao.model.req.statChart.DeptDataReq;
import com.shands.mod.dao.util.ThousandSeparatorUtil;
import com.shands.mod.main.service.statistics.BdwCardStatisticService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
@Slf4j
public class BdwCardStatisticServiceImpl implements BdwCardStatisticService {

    @Autowired
    private ModHotelInfoDao hotelInfoDao;

    @Autowired
    private ModUserCommonService modUserCommonService;

    @Autowired
    private ModNewDataBoardMapper modNewDataBoardMapper;

    @Autowired
    private AdsPowerDlEcoAppEmployeePerfDMapper adsPowerDlEcoAppEmployeePerfDMapper;


    @Override
    public List<DeptDataVo> queryBdwCardDeptData(DeptDataReq deptDataReq) {
        List<DeptDataVo> res = Lists.newArrayList();

        // 1. 获取当前用户id和酒店信息
        Integer userId = ThreadLocalHelper.getUser().getId();
        int hotelId = ThreadLocalHelper.getCompanyId();
        String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();

        // 2. 检查用户权限
        List<String> userRoles = modUserCommonService.getUserRoles(userId);
        List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList("HOTEL", userRoles, userId);

        boolean permission = moduleMenuVos.stream()
                .anyMatch(v -> DataBoardModuleEnum.BETTERWOOD_CARD_SELL_DATA.name().equals(v.getModuleCode()) );
        if (!permission) {
            return res;
        }


        Date startDate = DateUtil.parse(deptDataReq.getStartTime(), BaseConstants.FORMAT_DATE2);
        Date endDate = DateUtil.parse(deptDataReq.getEndTime(), BaseConstants.FORMAT_DATE2);
        String type = deptDataReq.getType();

        // 如果不是百达卡相关类型，直接返回空列表
        if (!isBdwCardType(type)) {
            return res;
        }

        // 从ads_power_dl_eco_app_employee_perf_d表获取百达卡数据
        List<AdsHotelPerformanceHDTO> cardData = adsPowerDlEcoAppEmployeePerfDMapper.queryBdwCardDataByEmployee(
                hotelCode, startDate, endDate, type);

        // 过滤掉售卡数和奖金都为0的记录
        List<DeptDataVo> deptData = cardData.stream()
                .filter(data -> {
                    long sellCount = data.getBdCardCnt() != null ? data.getBdCardCnt() : 0L;
                    BigDecimal bonusAmt = data.getBonusAmt() != null ? BigDecimal.valueOf(data.getBonusAmt()) : BigDecimal.ZERO;
                    return sellCount > 0 || bonusAmt.compareTo(BigDecimal.ZERO) > 0;
                })
                .map(data -> {
                    DeptDataVo dataVo = new DeptDataVo();
                    dataVo.setDeptName(data.getEmployeeDept());
                    dataVo.setKey(data.getEmployeeName());
                    
                    long sellCount = data.getBdCardCnt() != null ? data.getBdCardCnt() : 0L;
                    BigDecimal bonusAmt = data.getBonusAmt() != null ? BigDecimal.valueOf(data.getBonusAmt()) : BigDecimal.ZERO;
                    
                    dataVo.setFirstValueNum(BigDecimal.valueOf(sellCount));
                    dataVo.setSecondValueNum(bonusAmt);
                    dataVo.setFirstValue(ThousandSeparatorUtil.format(dataVo.getFirstValueNum()));
                    dataVo.setSecondValue(ThousandSeparatorUtil.format(bonusAmt));
                    return dataVo;
                })
                .collect(Collectors.toList());

        // 按部门分组
        Map<String, List<DeptDataVo>> deptNameMap = deptData.stream().collect(groupingBy(DeptDataVo::getDeptName));

        for (String deptName : deptNameMap.keySet()) {
            // 部门内排序：按售卡数降序，再按奖金降序
            List<DeptDataVo> deptDataVos = deptNameMap.get(deptName).stream()
                    .sorted(Comparator.comparing(DeptDataVo::getFirstValueNum, Comparator.reverseOrder())
                            .thenComparing(DeptDataVo::getSecondValueNum, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            DeptDataVo vo = new DeptDataVo();
            vo.setKey(deptName);
            BigDecimal firstValueNum = deptDataVos.stream().map(DeptDataVo::getFirstValueNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal secondValueNum = deptDataVos.stream().map(DeptDataVo::getSecondValueNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setFirstValueNum(firstValueNum);
            vo.setSecondValueNum(secondValueNum);
            vo.setFirstValue(ThousandSeparatorUtil.format(firstValueNum));
            vo.setSecondValue(ThousandSeparatorUtil.format(secondValueNum));
            vo.setChild(deptDataVos);
            res.add(vo);
        }

        // 部门级排序：按售卡数降序，再按奖金降序
        res = res.stream().sorted(Comparator.comparing(DeptDataVo::getFirstValueNum, Comparator.reverseOrder())
                .thenComparing(DeptDataVo::getSecondValueNum, Comparator.reverseOrder())).collect(Collectors.toList());

        // 计算合计
        if (CollectionUtils.isNotEmpty(res)) {
            DeptDataVo totalDataVo = new DeptDataVo();
            totalDataVo.setKey("合计");
            BigDecimal totalSellCount = res.stream().map(DeptDataVo::getFirstValueNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalBonusAmt = res.stream().map(DeptDataVo::getSecondValueNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalDataVo.setFirstValueNum(totalSellCount);
            totalDataVo.setSecondValueNum(totalBonusAmt);
            totalDataVo.setFirstValue(ThousandSeparatorUtil.format(totalSellCount));
            totalDataVo.setSecondValue(ThousandSeparatorUtil.format(totalBonusAmt));
            res.add(0, totalDataVo);
        }

        return res;
    }

    /**
     * 判断是否为百达卡类型
     */
    private boolean isBdwCardType(String type) {
        return DeptDataEnum.BETTERWOOD_CARD_TOTAL.name().equals(type) ||
               DeptDataEnum.QIHANG_CARD.name().equals(type) ||
               DeptDataEnum.MANLU_CARD.name().equals(type) ||
               DeptDataEnum.KAITUO_CARD.name().equals(type);
    }
}
