package com.shands.mod.main.service.sales.tool.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.betterwood.log.core.annotation.ResultLog;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.shands.greencloud.util.Tools;
import com.shands.mod.dao.model.enums.ShareChannelEnum;
import com.shands.mod.dao.model.sales.tool.domain.ModProxyOrder;
import com.shands.mod.dao.model.sales.tool.dto.QrCodeParameters;
import com.shands.mod.dao.model.sales.tool.dto.ShareInfoDTO;
import com.shands.mod.dao.model.sales.tool.dto.ShareParameters;
import com.shands.mod.dao.model.v0701.dto.OrderMainDTO;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.remote.hotel.RemoteBdwHotelService;
import com.shands.mod.main.service.sales.tool.IModProxyOrderService;
import com.shands.mod.message.util.RedisUtils;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.vo.ResultCode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 微信小程序二维码分享
 * @Author: wj
 * @Date: 2024/8/12 18:07
 */
@Service("qrCodeStrategy")
@Slf4j
public class QrCodeStrategy implements ShareStrategy {

  public static final String CARD_TITLE = "请扫码确认行程";

  public static final String CARD_DESC = "请扫码确认行程信息";

  private final int oneDaySeconds = 60 * 60 * 24;

  @Autowired
  private IModProxyOrderService modProxyOrderService;

  @Autowired
  private RemoteBdwHotelService remoteBdwHotelService;

  @Autowired
  private BetterwoodConfig betterwoodConfig;

  @Autowired
  private RedisUtils redisUtils;

  @Override
  public String getChannelId() {
    return ShareChannelEnum.QR_CODE.getChannelId();
  }

  @Override
  public ShareParameters execute(ShareInfoDTO dto) {
    QrCodeParameters parameters = new QrCodeParameters();
    parameters.setTitle(CARD_TITLE);
    parameters.setDescription(CARD_DESC);
    parameters.setImageUrl(dto.getImageUrl());

    String orderMainNo = dto.getOrderMainNo();
    ShareInfoDTO info = this.getQrCodeInfo(orderMainNo);
    if (Objects.nonNull(info) && StringUtils.isNotBlank(info.getQrCode())){
      parameters.setQrCode(info.getQrCode());
      return parameters;
    }

    String qrCode = this.getQrCodePic(orderMainNo);
    if (StringUtils.isNotBlank(qrCode)){
      parameters.setQrCode(qrCode);
      dto.setQrCode(qrCode);
      this.setQrCodeInfo(orderMainNo, dto);
    }
    return parameters;
  }

  @Override
  public ShareInfoDTO queryShareParam(String param) {
    ShareInfoDTO qrCodeInfo = this.getQrCodeInfo(param);
    if (Objects.isNull(qrCodeInfo)){
      throw new ServiceException(ResultCode.QR_CODE_QUERY_PARAM_ERROR);
    }
    return qrCodeInfo;
  }

  @ResultLog(name = "QrCodeStrategy.getQrCodeInfo", methodType = MethodTypeEnum.REDIS)
  public ShareInfoDTO getQrCodeInfo(String orderMainNo) {
    String key = String.format(BaseConstants.CACHE_MINI_APP_QR_CODE_INFO, orderMainNo);
    String obj = redisUtils.getString(key);
    if (StringUtils.isEmpty(obj)) {
      ModProxyOrder proxyOrder = modProxyOrderService.getModProxyOrder(orderMainNo);
      if (proxyOrder == null) {
        return null;
      }

      // 兜底处理
      ShareInfoDTO dto = new ShareInfoDTO();
      dto.setOrderMainNo(proxyOrder.getOrderMainNo());
      dto.setEventType(betterwoodConfig.getEventTypeOrderShare());
      OrderMainDTO mainDTO = remoteBdwHotelService.queryMainOrderInfo(orderMainNo,
          BaseConstants.CHANNEL_TYPE_DELONG);
      if (Objects.nonNull(mainDTO)) {
        dto.setSalesId(mainDTO.getSalesId());
      }
      return dto;
    }
    return JSON.parseObject(obj, ShareInfoDTO.class);
  }

  @ResultLog(name = "QrCodeStrategy.setQrCodeInfo", methodType = MethodTypeEnum.REDIS)
  public void setQrCodeInfo(String orderMainNo, ShareInfoDTO dto) {
    String key = String.format(BaseConstants.CACHE_MINI_APP_QR_CODE_INFO, orderMainNo);
    long expireTimeSecond = oneDaySeconds * 30;
    boolean flag = redisUtils.set(key, JSON.toJSONString(dto), expireTimeSecond);
    log.info("save qrCodeInfo success:{} orderMainNo:{}", flag, orderMainNo);
  }

  @ResultLog(name = "QrCodeStrategy.getQrCodePic", methodType = MethodTypeEnum.HTTP_DOWN)
  public String getQrCodePic(String orderMainNo) throws ServiceException {
    try {
      String baseApiUrl = betterwoodConfig.getBetterwoodApiUrl();
      String orderScanUrl = betterwoodConfig.getBetterwoodOrderScan().replace("{0}", betterwoodConfig.getBetterwoodOrderScanAppId());
      String fullApiUrl = baseApiUrl + orderScanUrl;

      Map<String, Object> requestData = new HashMap<>();
      String param = String.format("t=sales&k=%s", orderMainNo);
      requestData.put("param", param);
      requestData.put("page", "modules/hotel/views/order-detail/OrderDetail");

      log.info("Invoking betterwood getQrCodePic with URL: {} and parameters: {}", fullApiUrl, JSON.toJSONString(requestData));

      String response = Tools.postJson(fullApiUrl, requestData, null);
      log.info("Response from betterwood getQrCodePic: {}", response);

      JSONObject jsonResponse = JSON.parseObject(response);
      if (jsonResponse.getIntValue("code") == 1) {
        return jsonResponse.getJSONObject("data").getString("url");
      }
    } catch (ServiceException e) {
      log.error("invoke betterwood getQrCodePic error: ", e);
    }
    return null;
  }

}
